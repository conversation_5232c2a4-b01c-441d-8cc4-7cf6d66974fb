import cv2
import numpy as np
from pathlib import Path
from typing import List, Optional, Dict, Any

from plugins.plugin_base import PluginBase

__all__ = ["TemplateMatcher", "TemplateMatch"]

class TemplateMatcher:
    """Simple template matcher supporting OpenCV methods and drawing result."""

    METHODS = {
        'TM_CCOEFF_NORMED': cv2.TM_CCOEFF_NORMED,
        'TM_CCORR_NORMED': cv2.TM_CCORR_NORMED,
        'TM_SQDIFF_NORMED': cv2.TM_SQDIFF_NORMED,
    }
    SHAPE_METHOD = 'SHAPE'  # special method key for contour shape match

    def __init__(self, method: str = 'TM_CCOEFF_NORMED', min_conf: float = 0.7):
        self.method_name = method
        self.method = self.METHODS.get(method, None)
        self.min_conf = min_conf
        self.tpl = None  # template image (gray)
        self.tpl_cnt = None  # template contour

    # ------------ API ------------
    def set_template(self, tpl_img: np.ndarray):
        """Store template (expects BGR or gray)."""
        if tpl_img is None:
            raise ValueError('template image is None')
        if tpl_img.ndim == 3:
            tpl_img = cv2.cvtColor(tpl_img, cv2.COLOR_BGR2GRAY)
        self.tpl = tpl_img
        # extract largest contour for shape matching
        _, th = cv2.threshold(self.tpl, 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        cnts,_ = cv2.findContours(th, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        self.tpl_cnt = max(cnts, key=cv2.contourArea) if cnts else None

    def load_template(self, path: str):
        """Load template image.

        1. 如果传入的是相对路径且文件不存在，则在项目 recipes 目录下递归搜索同名文件（解决旧配方中相对路径问题）。
        2. 使用 np.fromfile + cv2.imdecode 以支持包含非 ASCII 字符的路径；失败时回退到 cv2.imread。
        """
        from pathlib import Path as _P
        p = _P(path)
        if not p.is_absolute() and not p.exists():
            # 使用路径管理模块搜索模板文件
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
                from path_manager import get_recipe_path

                recipes_dir = _P(get_recipe_path())
                rel_pattern = path.replace("\\", "/").lstrip("./")
                for candidate in recipes_dir.rglob(rel_pattern):
                    if candidate.is_file():
                        print(f"[TM] 相对路径未找到，使用搜索到的模板: {candidate}")
                        p = candidate
                        break
            except ImportError:
                # 回退到原来的逻辑
                proj_root = _P(__file__).resolve().parents[3]
                rel_pattern = path.replace("\\", "/").lstrip("./")
                for candidate in (proj_root / "recipes").rglob(rel_pattern):
                    if candidate.is_file():
                        print(f"[TM] 相对路径未找到，使用搜索到的模板: {candidate}")
                        p = candidate
                        break
        path = str(p)
        arr = None
        try:
            arr = np.fromfile(path, dtype=np.uint8)
            img = cv2.imdecode(arr, cv2.IMREAD_GRAYSCALE) if arr.size > 0 else None
        except Exception as e:
            print(f"[TM] np.fromfile failed: {e}")
            img = None
        if img is None:
            # fallback to imread
            img = cv2.imread(path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            raise ValueError(f'Failed to load template image: {path}')
        self.set_template(img)

    def save_template(self, path: str):
        if self.tpl is None:
            raise RuntimeError('template not set')
        cv2.imencode(Path(path).suffix, self.tpl)[1].tofile(path)

    def match(self, img_bgr: np.ndarray, *, angles: Optional[List[float]] = None, draw: bool = True):
        """Return dict with match info and result image.

        angles: list of degrees to rotate template before匹配; None 表示不做旋转搜索。
        """
        if self.tpl is None:
            raise RuntimeError('template not set')
        if img_bgr is None:
            raise ValueError('input image is None')
        # 允许输入已为灰度图，避免通道数错误
        if img_bgr.ndim == 2:
            gray_src = img_bgr.copy()
        else:
            gray_src = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

        # 确保用于显示的 out 为 3 通道，便于着色绘制 ROI
        out = img_bgr if img_bgr.ndim == 3 else cv2.cvtColor(img_bgr, cv2.COLOR_GRAY2BGR)

        best = {'score': -1, 'top_left': (0, 0), 'w': 0, 'h': 0, 'angle': 0}

        # special shape matching path
        if self.method_name == self.SHAPE_METHOD:
            return self._match_shape(img_bgr, draw=draw)

        angle_list = angles if angles else [0]
        for ang in angle_list:
            # 旋转模板
            tpl_rot = self._rotate_keep(self.tpl, ang) if ang != 0 else self.tpl
            # ensure template not larger than source
            if tpl_rot.shape[0] > gray_src.shape[0] or tpl_rot.shape[1] > gray_src.shape[1]:
                continue
            res = cv2.matchTemplate(gray_src, tpl_rot, self.method)
            if self.method in (cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED):
                min_val, _, min_loc, _ = cv2.minMaxLoc(res)
                score = 1 - min_val
                loc = min_loc
            else:
                _, max_val, _, max_loc = cv2.minMaxLoc(res)
                score = max_val
                loc = max_loc
            if score > best['score']:
                best.update({'score': score, 'top_left': loc, 'w': tpl_rot.shape[1],
                             'h': tpl_rot.shape[0], 'angle': ang})

        match_ok = best['score'] >= self.min_conf
        out = img_bgr.copy()
        color = (0, 255, 0) if match_ok else (0, 0, 255)
        if best['angle'] == 0:
            cv2.rectangle(out, best['top_left'], (best['top_left'][0] + best['w'], best['top_left'][1] + best['h']), color, 2)
            # 计算角点/中点
            x, y = best['top_left']; w, h = best['w'], best['h']
            corners = [(x, y), (x + w, y), (x + w, y + h), (x, y + h)]
            midpts = [((corners[i][0] + corners[(i + 1) % 4][0]) // 2,
                       (corners[i][1] + corners[(i + 1) % 4][1]) // 2) for i in range(4)]
        else:
            # 画旋转矩形
            cx = best['top_left'][0] + best['w'] / 2
            cy = best['top_left'][1] + best['h'] / 2
            rect = ((cx, cy), (best['w'], best['h']), best['angle'])
            pts = cv2.boxPoints(rect).astype(int)
            corners = [tuple(p) for p in pts]
            midpts = [((corners[i][0] + corners[(i + 1) % 4][0]) // 2,
                       (corners[i][1] + corners[(i + 1) % 4][1]) // 2) for i in range(4)]
            if draw:
                cv2.polylines(out, [pts], isClosed=True, color=color, thickness=2)
        # 统一计算中心/中点
        center_pt = (int(best['top_left'][0] + best['w'] / 2), int(best['top_left'][1] + best['h'] / 2))
        info = {
            'score': float(f"{best['score']:.3f}"),
            'ok': match_ok,
            'center': center_pt,
            'size': (best['w'], best['h']),
            'angle': best['angle'],
            'corners': corners,
            'midpoints': midpts,
        }
        if draw:
            cv2.drawMarker(out, center_pt, color, markerType=cv2.MARKER_CROSS, thickness=2)
        # 保证返回图像为 BGR 三通道，便于彩色显示
        if out.ndim == 2:
            out = cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)
        return {'info': info, 'img_out': out}

    # ---------- helpers ----------
    @staticmethod
    def _rotate_keep(img: np.ndarray, angle: float):
        """Rotate image keeping entire image (expand)."""
        h, w = img.shape[:2]
        center = (w / 2, h / 2)
        M = cv2.getRotationMatrix2D(center, angle, 1.0)
        # calculate new bounding dimensions
        cos = abs(M[0, 0]); sin = abs(M[0, 1])
        new_w = int(h * sin + w * cos)
        new_h = int(h * cos + w * sin)
        # adjust translation
        M[0, 2] += (new_w / 2) - center[0]
        M[1, 2] += (new_h / 2) - center[1]
        return cv2.warpAffine(img, M, (new_w, new_h), flags=cv2.INTER_LINEAR)

    def _match_shape(self, img_bgr, *, draw: bool = True):
        # 允许灰度输入；同时准备 out（BGR）用于绘制彩色 ROI
        if img_bgr.ndim == 2:
            gray = img_bgr.copy()
            out = cv2.cvtColor(img_bgr, cv2.COLOR_GRAY2BGR)
        else:
            gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)
            out = img_bgr.copy()
        _, th = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        cnts,_ = cv2.findContours(th, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        best_d = 1e9; best_cnt=None
        for c in cnts:
            d = cv2.matchShapes(self.tpl_cnt, c, cv2.CONTOURS_MATCH_I1, 0)
            if d < best_d:
                best_d, best_cnt = d, c
        score = 1/(1+best_d) if best_cnt is not None else 0
        ok = score >= self.min_conf  # 是否通过阈值
        if best_cnt is not None:
            rect = cv2.minAreaRect(best_cnt)
            center = (int(rect[0][0]), int(rect[0][1]))
            box = cv2.boxPoints(rect).astype(int)
            corners = [tuple(p) for p in box]
            midpts = [((corners[i][0] + corners[(i + 1) % 4][0]) // 2,
                       (corners[i][1] + corners[(i + 1) % 4][1]) // 2) for i in range(4)]
            color = (0,255,0) if ok else (0,0,255)
            if draw:
                cv2.polylines(out, [box], True, color, 2)
                cv2.drawMarker(out, center, color, markerType=cv2.MARKER_CROSS, thickness=2)
        else:
            center = (0, 0); corners = []; midpts = []

        info = {
            'score': float(f"{score:.3f}"),
            'ok': ok,
            'angle': rect[2] if best_cnt is not None else 0,
            'center': center,
            'corners': corners,
            'midpoints': midpts,
        }
        # 保证返回图像为 BGR
        if out.ndim == 2:
            out = cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)
        return {'info': info, 'img_out': out}
