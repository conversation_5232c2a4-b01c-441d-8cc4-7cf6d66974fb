"""Distance Tool - 测两点距离或点线距离
操作：
  1. 点两下 → 显示两点距离
  2. 点三下 → 第3点到前两点连线距离
返回 {'dist':d, 'type':'pp'|'pl'}
"""
import tkinter as tk
from tkinter import filedialog, messagebox
import cv2, numpy as np
from math import hypot

class DistanceTool(tk.Toplevel):
    def __init__(self, master=None, img=None):
        super().__init__(master); self.title('距离测量'); self.geometry('800x600')
        self.pts=[]; self.img=img.copy() if img is not None else None
        self.canvas=tk.Canvas(self,bg='black'); self.canvas.pack(fill=tk.BOTH,expand=True)
        self.canvas.bind('<Button-1>',self._click)
        ctrl=tk.Frame(self); ctrl.pack(fill=tk.X)
        tk.Button(ctrl,text='载入图片',command=self._load).pack(side=tk.LEFT)
        tk.Button(ctrl,text='清除',command=self._clear).pack(side=tk.LEFT)
        self.lbl=tk.Label(ctrl,text='点击两点或三点'); self.lbl.pack(side=tk.LEFT,padx=10)
        if self.img is not None: self._show()
        self.result=None; self.wait_window(self)

    def _click(self,e):
        if self.img is None: return
        self.pts.append((e.x,e.y)); r=3
        self.canvas.create_oval(e.x-r,e.y-r,e.x+r,e.y+r,outline='red',fill='red')
        n=len(self.pts)
        if n==2:
            d=hypot(self.pts[1][0]-self.pts[0][0], self.pts[1][1]-self.pts[0][1])
            self.canvas.create_line(*self.pts[0],*self.pts[1],fill='lime',width=2)
            self.lbl.config(text=f'两点距离: {d:.2f}px')
            self.result={'dist':float(d),'type':'pp'}
        elif n==3:
            # distance point→line
            p0,p1,p2=self.pts
            d=self._pt_line_dist(p2,p0,p1)
            self.canvas.create_line(*p0,*p1,fill='cyan',dash=(4,2))
            self.canvas.create_line(*p2,*self._proj(p2,p0,p1),fill='yellow',dash=(2,2))
            self.lbl.config(text=f'点到线距离: {d:.2f}px')
            self.result={'dist':float(d),'type':'pl'}
    def _pt_line_dist(self,p,l1,l2):
        x0,y0=p; x1,y1=l1; x2,y2=l2
        return abs((y2-y1)*x0-(x2-x1)*y0+x2*y1-y2*x1)/hypot(x2-x1,y2-y1)
    def _proj(self,p,l1,l2):
        x0,y0=p; x1,y1=l1; x2,y2=l2
        dx,dy=x2-x1,y2-y1
        t=((x0-x1)*dx+(y0-y1)*dy)/(dx*dx+dy*dy)
        return (x1+t*dx, y1+t*dy)
    def _load(self):
        f=filedialog.askopenfilename(filetypes=[('Image','*.png;*.jpg;*.bmp')]);
        if not f:return
        img=cv2.imread(f);
        if img is None: messagebox.showerror('错误','无法读取'); return
        self.img=img; self._show()
    def _clear(self):
        self.pts.clear(); self.canvas.delete('all'); self.lbl.config(text='点击两点或三点'); self._show()
    def _show(self):
        if self.img is None: return
        rgb=cv2.cvtColor(self.img,cv2.COLOR_BGR2RGB)
        h,w=rgb.shape[:2]
        self.tk_img=tk.PhotoImage(width=w,height=h,data=cv2.imencode('.png',cv2.cvtColor(rgb,cv2.COLOR_RGB2BGR))[1].tobytes(),format='png')
        self.canvas.create_image(0,0,anchor='nw',image=self.tk_img)

def launch(img=None):
    return DistanceTool(master=tk._default_root or tk.Tk(), img=img).result
