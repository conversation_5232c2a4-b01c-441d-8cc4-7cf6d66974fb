# 逻辑判断插件默认配置
# 这个配置展示了如何设置多条件判断和IO输出

# 基本设置
enable_judgment: true
judgment_mode: "strict"  # strict/tolerant/custom
default_result: "UNKNOWN"

# 判断条件列表
conditions:
  - name: "尺寸检查"
    source_plugin: "geometry_measurement"
    source_key: "length_mm"
    operator: "IN_RANGE"
    target_value: [12.8, 13.8]  # 长度在12.8-13.8mm范围内
    weight: 1.0
    enabled: true
    
  - name: "宽度检查"
    source_plugin: "geometry_measurement"
    source_key: "width_mm"
    operator: "IN_RANGE"
    target_value: [4.0, 4.8]   # 宽度在4.0-4.8mm范围内
    weight: 1.0
    enabled: true
    
  - name: "轮廓数量检查"
    source_plugin: "contour_detection"
    source_key: "contour_count"
    operator: "=="
    target_value: 1            # 只能有1个轮廓
    weight: 0.8
    enabled: true
    
  - name: "模板匹配度检查"
    source_plugin: "template_matching"
    source_key: "match_score"
    operator: ">="
    target_value: 0.85         # 匹配度大于等于85%
    weight: 0.6
    enabled: true

# 逻辑表达式：所有条件都必须满足
logic_expression: "C1 AND C2 AND C3 AND C4"

# IO输出配置
io_outputs:
  - name: "OK指示灯"
    io_type: "digital"
    address: "DO1"
    ok_value: true
    ng_value: false
    warn_value: null
    enabled: true
    
  - name: "NG指示灯"
    io_type: "digital"
    address: "DO2"
    ok_value: false
    ng_value: true
    warn_value: null
    enabled: true
    
  - name: "蜂鸣器"
    io_type: "digital"
    address: "DO3"
    ok_value: false
    ng_value: true
    warn_value: true
    enabled: true
    
  - name: "分拣气缸"
    io_type: "digital"
    address: "DO4"
    ok_value: false  # OK时不动作
    ng_value: true   # NG时推出
    warn_value: null
    enabled: true
    
  - name: "质量信号"
    io_type: "analog"
    address: "AO1"
    ok_value: 10.0   # OK时输出10V
    ng_value: 0.0    # NG时输出0V
    warn_value: 5.0  # WARN时输出5V
    enabled: true

# 统计设置
enable_statistics: true
statistics_window: 100  # 统计最近100个结果

# 数据记录
enable_logging: true
log_level: "INFO"  # DEBUG/INFO/WARN/ERROR

# 报警设置
ng_threshold: 5      # 连续5次NG触发报警
warn_threshold: 10   # 连续10次WARN触发报警
