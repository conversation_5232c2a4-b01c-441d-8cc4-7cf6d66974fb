"""YOLO Detect core module (supports .pt via ultralytics and .onnx via OpenCV DNN).

Public API
----------
load_model(model_path: str, input_size=(640,640)) -> YOLODetector
YOLODetector.detect(img_bgr, conf_thres=0.25, iou_thres=0.45) -> list[dict]
Each dict: {"box":(x1,y1,x2,y2), "score":float, "label":str}

Usage example:
    det=load_model('models/best.onnx')
    res=det.detect(cv2.imread('test.jpg'))
"""
from __future__ import annotations
import os
import sys, types, importlib
os.environ.setdefault('ULTRALYTICS_AUTO_INSTALL', '0')  # disable warning auto install
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional

import cv2
import numpy as np

# --- workaround: some old .pt reference numpy._core.* ---
def _mock_numpy_core():
    try:
        import numpy as _np
    except Exception:
        return
    base_name='numpy._core'
    if base_name not in sys.modules:
        proxy=types.ModuleType(base_name)
        proxy.__getattr__=lambda attr: getattr(_np.core, attr)
        sys.modules[base_name]=proxy
    for sub in ['multiarray','numerictypes','umath']:
        real_name=f'numpy.core.{sub}'
        proxy_name=f'{base_name}.{sub}'
        try:
            real_mod=importlib.import_module(real_name)
        except Exception:
            real_mod=types.ModuleType(proxy_name)
        sys.modules[proxy_name]=real_mod

_mock_numpy_core()

# ultralytics is optional, import lazily
YOLO = None
try:
    # 在打包环境中，可能需要特殊的导入处理
    import sys
    import os

    # 确保ultralytics能够找到其依赖
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller环境
        ultralytics_path = os.path.join(sys._MEIPASS, 'ultralytics')
        if os.path.exists(ultralytics_path) and ultralytics_path not in sys.path:
            sys.path.insert(0, ultralytics_path)

    from ultralytics import YOLO  # type: ignore
    from ultralytics.utils import LOGGER  # type: ignore
    import logging
    LOGGER.setLevel(logging.ERROR)  # suppress verbose warnings
    print(f"[YOLO] ultralytics导入成功")
except ImportError as e:
    print(f"[YOLO] ultralytics导入失败: {e}")
    YOLO = None  # pytype: disable=invalid-name
except Exception as e:
    print(f"[YOLO] ultralytics导入异常: {e}")
    YOLO = None  # pytype: disable=invalid-name

CLASS_NAMES_CACHE: Dict[str, List[str]] = {}

def _load_names(names_path: Path) -> List[str]:
    if names_path.suffix == '.txt':
        return [l.strip() for l in names_path.read_text(encoding='utf-8').splitlines() if l.strip()]
    return []

class YOLODetector:
    def __init__(self, model_path: str, input_size: Tuple[int, int] = (640, 640)):
        self.model_path = str(model_path)
        self.input_size = input_size
        self.is_onnx = self.model_path.lower().endswith('.onnx')
        self.is_pt = self.model_path.lower().endswith('.pt')
        if not (self.is_onnx or self.is_pt):
            raise ValueError('Only .onnx or .pt model is supported')

        self.net = None  # for onnx
        self.ultra_model = None  # for pt
        self.class_names: List[str] = []
        self.last_roi: Optional[Tuple[int,int,int,int]] = None  # (x,y,w,h)
        self.last_roi_pts: Optional[np.ndarray] = None  # 4x2 points
        self.last_score: Optional[float] = None
        # ------- optional template ROI -------
        self.template: Optional[np.ndarray] = None  # gray uint8 (legacy)
        self.tpl_cnt: Optional[np.ndarray] = None  # contour for shape match
        self.tpl_threshold: float = 0.8
        self.roi_expand: float = 0.1  # 10% expand on each side
        self._load_model()

    # ---------------- internal ----------------
    def _load_model(self):
        if self.is_onnx:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f'ONNX模型文件不存在: {self.model_path}')
            self.net = cv2.dnn.readNetFromONNX(self.model_path)
            self.net.setPreferableBackend(cv2.dnn.DNN_BACKEND_DEFAULT)
            self.net.setPreferableTarget(cv2.dnn.DNN_TARGET_CPU)
            # try load names file alongside model
            names_path = Path(self.model_path).with_suffix('.txt')
            if names_path.exists():
                self.class_names = _load_names(names_path)
        else:
            if YOLO is None:
                error_msg = "ultralytics not installed; required for .pt models"
                print(f"[YOLO] 错误: {error_msg}")
                print(f"[YOLO] 建议: 请使用ONNX格式的模型文件(.onnx)替代PyTorch格式(.pt)")
                raise ImportError(error_msg)

            # 确保模型文件存在，避免自动下载默认模型
            if not os.path.exists(self.model_path):
                error_msg = f'PyTorch模型文件不存在: {self.model_path}'
                print(f"[YOLO] 错误: {error_msg}")
                raise FileNotFoundError(error_msg)

            try:
                print(f"[YOLO] 加载自定义模型: {self.model_path}")
                self.ultra_model = YOLO(self.model_path, task='detect')
                self.class_names = self.ultra_model.names  # type: ignore
                print(f"[YOLO] 模型加载成功，类别数: {len(self.class_names)}")
            except Exception as e:
                error_msg = f"YOLO模型加载失败: {e}"
                print(f"[YOLO] 错误: {error_msg}")
                print(f"[YOLO] 建议: 请检查模型文件是否正确，或使用ONNX格式替代")
                raise RuntimeError(error_msg) from e

    # ---------------- public ----------------
    def load_template(self, tpl_path: str, threshold: float = 0.8):
        tpl = cv2.imread(tpl_path, cv2.IMREAD_GRAYSCALE)
        if tpl is None:
            raise ValueError('模板读取失败')
        self.template = tpl
        self.tpl_threshold = threshold
        # extract contour
        _, th = cv2.threshold(self.template,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        cnts,_ = cv2.findContours(th, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        self.tpl_cnt = max(cnts, key=cv2.contourArea) if cnts else None

    def make_template_from_image(self, img: np.ndarray, threshold: float=0.8):
        """Create contour template from current image (largest external contour)."""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim==3 else img
        _, th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        cnts,_ = cv2.findContours(th, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not cnts:
            raise ValueError('当前图未检测到轮廓')
        self.tpl_cnt = max(cnts, key=cv2.contourArea)
        self.template = None
        self.tpl_threshold = threshold

    def detect(self, img: np.ndarray, conf: float = 0.25, iou: float = 0.45, use_roi: bool=False):
        if img is None:
            raise ValueError('img is None')
        offset = (0,0)
        crop_img = img
        # ---------- optional ROI cropping ----------
        self.last_roi = None
        self.last_roi_pts = None
        self.last_score = None
        if use_roi and self.tpl_cnt is not None:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim==3 else img
            _, th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
            cnts,_ = cv2.findContours(th, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            best_d = 1e9; best_cnt=None
            # 从配置获取最小轮廓面积阈值，默认100像素
            min_contour_area = getattr(self, 'min_contour_area', 100)
            filtered_count = 0
            total_count = len(cnts)

            for c in cnts:
                # 添加面积过滤
                area = cv2.contourArea(c)
                if area < min_contour_area:
                    filtered_count += 1
                    continue  # 跳过面积太小的轮廓

                d = cv2.matchShapes(self.tpl_cnt, c, cv2.CONTOURS_MATCH_I1, 0)
                if d < best_d:
                    best_d,best_cnt = d,c

            if filtered_count > 0:
                print(f"DEBUG: 面积过滤 - 总轮廓: {total_count}, 过滤掉: {filtered_count}, 剩余: {total_count - filtered_count}, 阈值: {min_contour_area}px")
            if best_cnt is not None:
                score = 1/(1+best_d)
                self.last_score = score
                print(f"DEBUG: ROI轮廓匹配 - 分数: {score:.3f}, 阈值: {self.tpl_threshold:.3f}")

                # 无论匹配分数如何，都计算并保存ROI信息用于显示
                rect = cv2.minAreaRect(best_cnt)
                (cx,cy),(w0,h0),angle = rect
                print(f"DEBUG: 找到的轮廓 - 中心: ({cx:.1f}, {cy:.1f}), 原始尺寸: {w0:.1f}x{h0:.1f}, 角度: {angle:.1f}°")

                # expand size
                scale = 1.0 + self.roi_expand
                rect = ((cx,cy),(w0*scale,h0*scale),angle)
                pts = cv2.boxPoints(rect).astype(int)
                print(f"DEBUG: 扩展后ROI - 扩展比例: {scale:.3f}, 扩展尺寸: {w0*scale:.1f}x{h0*scale:.1f}")

                # 始终保存ROI轮廓点用于显示
                self.last_roi_pts = pts

                # 计算边界框用于裁剪（如果需要）
                xs = pts[:,0]; ys = pts[:,1]
                x,y = int(xs.min()), int(ys.min())
                w,h = int(xs.max()-x), int(ys.max()-y)
                print(f"DEBUG: 旋转矩形边界框: x={x}, y={y}, w={w}, h={h}")
                # clamp to image bounds
                H,W = img.shape[:2]
                x1 = max(0,x); y1 = max(0,y)
                x2 = min(W, x+w); y2 = min(H, y+h)
                w_new = x2 - x1; h_new = y2 - y1
                print(f"DEBUG: 限制到图像边界后: x1={x1}, y1={y1}, x2={x2}, y2={y2}, w_new={w_new}, h_new={h_new}")

                # 保存矩形ROI信息
                self.last_roi = (x1,y1,w_new,h_new)

                # 无论匹配分数如何，都使用ROI区域进行检测（ROI的目的是排除干扰项）
                if w_new>=10 and h_new>=10:
                    crop_img = img[y1:y2, x1:x2]
                    offset = (x1,y1)
                    print(f"DEBUG: 原图尺寸: {img.shape}, ROI裁剪区域: ({x1},{y1},{w_new},{h_new}), 裁剪后图像尺寸: {crop_img.shape}")
                    if score >= self.tpl_threshold:
                        print(f"DEBUG: ROI形状匹配OK，使用ROI区域检测: ({x1},{y1},{w_new},{h_new})")
                    else:
                        print(f"DEBUG: ROI形状匹配NG({score:.3f} < {self.tpl_threshold:.3f})，但仍使用ROI区域检测: ({x1},{y1},{w_new},{h_new})")
                else:
                    print(f"DEBUG: ROI区域太小({w_new}x{h_new})，使用全图检测")
            else:
                print(f"DEBUG: 未找到匹配的轮廓，使用全图检测")
        elif use_roi and self.template is not None:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim==3 else img
            res = cv2.matchTemplate(gray, self.template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(res)
            self.last_score = float(max_val)
            if max_val >= self.tpl_threshold:
                x,y = max_loc
                h,w = self.template.shape[:2]
                x2 = min(x+w, img.shape[1])
                y2 = min(y+h, img.shape[0])
                crop_img = img[y:y2, x:x2]
                offset = (x,y)
                self.last_roi = (x,y,w,h)
        # ---------- detection ----------
        if self.is_onnx:
            dets = self._detect_onnx(crop_img, conf, iou)
        else:
            dets = self._detect_pt(crop_img, conf, iou)
        # offset boxes
        if offset!=(0,0):
            ox,oy = offset
            print(f"DEBUG: 应用坐标偏移: offset=({ox},{oy}), 检测结果数量: {len(dets)}")
            for i, d in enumerate(dets):
                old_box = d['box']  # tuple可以直接赋值，不需要copy
                d['box'] = [d['box'][0]+ox, d['box'][1]+oy, d['box'][2]+ox, d['box'][3]+oy]
                print(f"DEBUG: 检测结果{i}: 原始坐标{old_box} -> 偏移后坐标{d['box']}")
        else:
            print(f"DEBUG: 无坐标偏移，检测结果数量: {len(dets)}")
        return dets

    # ---------------- onnx path ----------------
    def _detect_onnx(self, img: np.ndarray, conf: float, iou: float):
        h0, w0 = img.shape[:2]
        blob = cv2.dnn.blobFromImage(img, 1/255.0, self.input_size, swapRB=True, crop=False)
        self.net.setInput(blob)
        preds = self.net.forward()
        # YOLOv5 ONNX output: (1,N,85) [x,y,w,h,conf,80class]
        preds = preds[0]
        boxes, scores, labels = [], [], []
        for det in preds:
            conf_det = det[4]
            if conf_det < conf:
                continue
            class_prob = det[5:]
            cls_id = np.argmax(class_prob)
            score = conf_det * class_prob[cls_id]
            if score < conf:
                continue
            cx, cy, w, h = det[:4]
            x1 = (cx - w/2) * w0 / self.input_size[0]
            y1 = (cy - h/2) * h0 / self.input_size[1]
            x2 = (cx + w/2) * w0 / self.input_size[0]
            y2 = (cy + h/2) * h0 / self.input_size[1]
            boxes.append([x1, y1, x2, y2])
            scores.append(float(score))
            labels.append(int(cls_id))
        # NMS
        if not boxes:
            return []
        idxs = cv2.dnn.NMSBoxes(boxes, scores, conf, iou)
        results = []
        for i in idxs.flatten():
            box = [int(x) for x in boxes[i]]
            results.append({
                'box': tuple(box),
                'score': scores[i],
                'label': self.class_names[labels[i]] if self.class_names else str(labels[i])
            })
        return results

    # ---------------- pt path ----------------
    def _detect_pt(self, img: np.ndarray, conf: float, iou: float):
        if self.ultra_model is None:
            error_msg = "YOLO模型未正确加载，无法进行检测"
            print(f"[YOLO] 错误: {error_msg}")
            print(f"[YOLO] 建议: 请检查模型文件路径和ultralytics库安装")
            raise RuntimeError(error_msg)

        try:
            res = self.ultra_model(img, conf=conf, iou=iou, imgsz=self.input_size, verbose=False, device='cpu')[0]
            results = []
            for box, score, cls in zip(res.boxes.xyxy.cpu().numpy(), res.boxes.conf.cpu().numpy(), res.boxes.cls.cpu().numpy()):
                results.append({
                    'box': tuple(int(x) for x in box),
                    'score': float(score),
                    'label': self.class_names[int(cls)] if self.class_names else str(int(cls))
                })
            return results
        except Exception as e:
            error_msg = f"YOLO检测过程中发生错误: {e}"
            print(f"[YOLO] 错误: {error_msg}")
            raise RuntimeError(error_msg) from e

def load_model(model_path: str, input_size=(640, 640)) -> YOLODetector:
    """Load detector and perform a warm-up inference to reduce first-time latency."""
    det = YOLODetector(model_path, input_size)
    try:
        dummy = np.zeros((input_size[1], input_size[0], 3), dtype=np.uint8)
        det.detect(dummy, conf=0.25, use_roi=False)  # warm-up
    except Exception:
        pass  # ignore warm-up errors, continue
    return det
