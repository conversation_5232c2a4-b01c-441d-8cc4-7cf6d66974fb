"""Camera configuration helper.

Provides typed access to ``config/camera_config.yaml``.
"""
from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, List
import yaml

_CFG_PATH = Path(__file__).resolve().parents[1] / "config" / "camera_config.yaml"

class CameraConfig:
    """Wrap camera YAML with helper methods."""
    def __init__(self, data: Dict[str, Any] | None = None):
        self._data: Dict[str, Any] = data or {}
        self._data.setdefault("cameras", [])

    # ---------------- properties ----------------
    @property
    def cameras(self) -> List[Dict[str, Any]]:
        return self._data["cameras"]

    # ---------------- CRUD ----------------
    def get(self, cam_id: str) -> Dict[str, Any] | None:
        return next((c for c in self.cameras if c.get("id") == cam_id), None)

    # ---------------- persistence ----------------
    @staticmethod
    def load(path: str | Path = _CFG_PATH) -> "CameraConfig":
        p = Path(path)
        data = yaml.safe_load(p.read_text(encoding="utf-8")) if p.is_file() else {}
        return CameraConfig(data)

    def save(self, path: str | Path = _CFG_PATH):
        p = Path(path)
        p.parent.mkdir(parents=True, exist_ok=True)
        yaml.safe_dump(self._data, p.open("w", encoding="utf-8"), allow_unicode=True, sort_keys=False)
