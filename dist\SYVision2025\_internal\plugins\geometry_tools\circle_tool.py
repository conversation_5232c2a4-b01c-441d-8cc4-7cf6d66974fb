# -*- coding: utf-8 -*-
"""Circle measurement tool.

Given an approximate circle ROI (center & estimated radius), this module extracts
edge points with <PERSON><PERSON>, fits the circle robustly via RANSAC/least-squares and
returns center & radius. It reuses most of the logic from `arc_tool.measure_arc`
but omits the arc-specific calculations.
"""
from __future__ import annotations

from typing import <PERSON>, Di<PERSON>, <PERSON><PERSON>, List
import numpy as np
import cv2
import random

__all__ = ['measure_circle']

# --- helpers -----------------------------------------------------------------

def _circle_from_3pts(p1: np.ndarray, p2: np.ndarray, p3: np.ndarray) -> Tuple[np.ndarray, float]:
    x1, y1 = p1
    x2, y2 = p2
    x3, y3 = p3
    det = 2 * (x1 * (y2 - y3) - y1 * (x2 - x3) + x2 * y3 - y2 * x3)
    if abs(det) < 1e-6:
        raise ValueError('Colinear')
    ux = ((x1 ** 2 + y1 ** 2) * (y2 - y3) + (x2 ** 2 + y2 ** 2) * (y3 - y1) + (x3 ** 2 + y3 ** 2) * (y1 - y2)) / det
    uy = ((x1 ** 2 + y1 ** 2) * (x3 - x2) + (x2 ** 2 + y2 ** 2) * (x1 - x3) + (x3 ** 2 + y3 ** 2) * (x2 - x1)) / det
    center = np.array([ux, uy], dtype=np.float32)
    radius = float(np.linalg.norm(center - p1))
    return center, radius

def _ransac_circle(points: np.ndarray, thresh: float = 2.0, iterations: int = 500) -> Tuple[np.ndarray, float, np.ndarray]:
    best_inliers: List[int] = []
    best_center = np.array([0.0, 0.0])
    best_r = 0.0
    n = len(points)
    if n < 3:
        raise ValueError('Not enough points')
    idxs = list(range(n))
    for _ in range(iterations):
        sample = random.sample(idxs, 3)
        try:
            c, r = _circle_from_3pts(points[sample[0]], points[sample[1]], points[sample[2]])
        except ValueError:
            continue
        dists = np.linalg.norm(points - c, axis=1)
        inliers = np.where(np.abs(dists - r) < thresh)[0]
        if inliers.size > len(best_inliers):
            best_inliers = inliers.tolist()
            best_center = c
            best_r = r
    if len(best_inliers) < 3:
        raise ValueError('RANSAC failed')
    inlier_mask = np.zeros(n, dtype=bool)
    inlier_mask[best_inliers] = True
    # refine
    pts_in = points[inlier_mask]
    A = np.hstack((2 * pts_in, np.ones((pts_in.shape[0], 1))))
    b = (pts_in ** 2).sum(axis=1)
    x = np.linalg.lstsq(A, b, rcond=None)[0]
    center = x[:2]
    r = float(np.sqrt((center ** 2).sum() + x[2]))
    dists = np.linalg.norm(points - center, axis=1)
    inlier_mask = np.abs(dists - r) < thresh
    return center, r, inlier_mask

# -----------------------------------------------------------------------------

def _extract_subpixel_edges(gray: np.ndarray, cx: float, cy: float, r_est: float,
                           canny_th1: int = 50, canny_th2: int = 150, debug: bool = False) -> np.ndarray:
    """提取亚像素精度的圆形边缘点

    Args:
        gray: 灰度图像
        cx, cy: 圆心估计位置
        r_est: 半径估计值
        canny_th1, canny_th2: Canny边缘检测阈值
        debug: 是否输出调试信息

    Returns:
        亚像素边缘点数组 (N, 2)
    """
    if debug:
        print(f"[subpixel] 开始亚像素边缘检测: 中心({cx:.1f},{cy:.1f}), 半径{r_est:.1f}")

    # 1. 使用Canny检测边缘
    edges = cv2.Canny(gray, canny_th1, canny_th2)
    edge_count = np.sum(edges > 0)

    if debug:
        print(f"[subpixel] Canny边缘点数: {edge_count}")

    if edge_count < 50:  # 边缘点太少
        if debug:
            print(f"[subpixel] 边缘点太少，尝试降低Canny阈值")
        # 尝试更低的阈值
        edges = cv2.Canny(gray, max(10, canny_th1//2), max(30, canny_th2//2))
        edge_count = np.sum(edges > 0)
        if debug:
            print(f"[subpixel] 降低阈值后边缘点数: {edge_count}")

    # 2. 方法1: 使用轮廓检测
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

    if debug:
        print(f"[subpixel] 找到轮廓数: {len(contours)}")

    best_contour = None
    if contours:
        # 选择最接近预期圆形的轮廓
        min_distance = float('inf')

        for i, contour in enumerate(contours):
            if len(contour) < 10:  # 轮廓点太少
                continue

            # 计算轮廓中心到预期圆心的距离
            M = cv2.moments(contour)
            if M['m00'] == 0:
                continue

            contour_cx = M['m10'] / M['m00']
            contour_cy = M['m01'] / M['m00']
            center_dist = np.sqrt((contour_cx - cx)**2 + (contour_cy - cy)**2)

            if debug:
                print(f"[subpixel] 轮廓{i}: 点数{len(contour)}, 中心({contour_cx:.1f},{contour_cy:.1f}), 距离{center_dist:.1f}")

            if center_dist < min_distance and center_dist < r_est * 0.8:  # 中心距离不能太远
                min_distance = center_dist
                best_contour = contour

    # 3. 如果轮廓方法失败，使用方法2: 直接从边缘点提取
    if best_contour is None or len(best_contour) < 20:
        if debug:
            print(f"[subpixel] 轮廓方法失败，使用边缘点直接提取")

        # 获取所有边缘点
        edge_points = np.column_stack(np.where(edges))  # (row, col)
        if len(edge_points) == 0:
            return np.array([]).reshape(0, 2)

        # 转换为 (x, y) 格式
        edge_points = np.stack((edge_points[:, 1], edge_points[:, 0]), axis=1).astype(np.float32)

        # 过滤距离预期圆心合理范围内的点
        distances = np.linalg.norm(edge_points - np.array([cx, cy]), axis=1)
        valid_mask = np.abs(distances - r_est) < r_est * 0.6  # 允许60%的半径误差

        if debug:
            print(f"[subpixel] 边缘点总数: {len(edge_points)}, 有效点数: {np.sum(valid_mask)}")

        if np.sum(valid_mask) < 10:
            return np.array([]).reshape(0, 2)

        return edge_points[valid_mask]

    # 4. 对轮廓进行亚像素优化
    if debug:
        print(f"[subpixel] 使用轮廓进行亚像素优化，轮廓点数: {len(best_contour)}")

    # 将轮廓点转换为角点格式
    corners = best_contour.reshape(-1, 1, 2).astype(np.float32)

    # 亚像素角点检测参数
    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.01)
    winSize = (3, 3)  # 减小搜索窗口
    zeroZone = (-1, -1)

    # 执行亚像素优化
    try:
        original_corners = corners.copy()
        cv2.cornerSubPix(gray, corners, winSize, zeroZone, criteria)
        subpixel_points = corners.reshape(-1, 2)

        # 检查优化效果
        movement = np.linalg.norm(corners.reshape(-1, 2) - original_corners.reshape(-1, 2), axis=1)
        avg_movement = np.mean(movement)

        if debug:
            print(f"[subpixel] 亚像素优化完成，平均移动距离: {avg_movement:.3f}")

        # 过滤距离预期圆心过远的点
        distances = np.linalg.norm(subpixel_points - np.array([cx, cy]), axis=1)
        valid_mask = np.abs(distances - r_est) < r_est * 0.5

        result_points = subpixel_points[valid_mask]

        if debug:
            print(f"[subpixel] 最终有效点数: {len(result_points)}")

        return result_points

    except cv2.error as e:
        if debug:
            print(f"[subpixel] 亚像素优化失败: {e}")
        # 返回原始轮廓点
        contour_points = best_contour.reshape(-1, 2).astype(np.float32)
        distances = np.linalg.norm(contour_points - np.array([cx, cy]), axis=1)
        valid_mask = np.abs(distances - r_est) < r_est * 0.5
        return contour_points[valid_mask]


def measure_circle(
    image: np.ndarray,
    cx: float,
    cy: float,
    r_est: float,
    *,
    canny_th1: int = 50,
    canny_th2: int = 150,
    ransac_thresh: float = 2.0,
    ransac_iter: int = 500,
    return_debug: bool = False,
    subpixel: bool = True,
) -> Dict[str, Any]:
    """Robust full-circle measurement.

    Parameters
    ----------
    image : np.ndarray (BGR)
    cx, cy, r_est : float
        Approximate center & radius provided by user (in image coords).
    canny_th1, canny_th2 : int
        Canny thresholds.
    ransac_thresh : float
        Inlier distance threshold (pixels).
    return_debug : bool
        If True, include inlier points, etc.
    """
    h, w = image.shape[:2]
    r_pad = int(r_est * 1.1) + 5
    x0, y0 = int(cx - r_pad), int(cy - r_pad)
    x1, y1 = int(cx + r_pad), int(cy + r_pad)
    x0, y0 = max(x0, 0), max(y0, 0)
    x1, y1 = min(x1, w - 1), min(y1, h - 1)
    if x1 - x0 < 5 or y1 - y0 < 5:
        return {'error': 'ROI too small'}

    roi = image[y0:y1, x0:x1]
    gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

    if subpixel:
        # 使用亚像素边缘检测
        # 调整坐标到ROI坐标系
        roi_cx = cx - x0
        roi_cy = cy - y0
        pts_roi = _extract_subpixel_edges(gray, roi_cx, roi_cy, r_est, canny_th1, canny_th2, debug=return_debug)

        if pts_roi.size == 0:
            # 如果亚像素检测失败，降级到传统方法
            if return_debug:
                print("[measure_circle] 亚像素检测失败，降级到传统边缘检测")
            edges = cv2.Canny(gray, canny_th1, canny_th2)
            pts = np.column_stack(np.nonzero(edges))  # (row, col)
            if pts.size == 0:
                return {'error': 'no edges found (both subpixel and traditional methods failed)'}
            pts_img = np.stack((pts[:, 1] + x0, pts[:, 0] + y0), axis=1).astype(np.float32)
        else:
            # 转换回图像坐标系
            pts_img = pts_roi + np.array([x0, y0])
    else:
        # 使用传统整像素边缘检测
        edges = cv2.Canny(gray, canny_th1, canny_th2)
        pts = np.column_stack(np.nonzero(edges))  # (row, col)
        if pts.size == 0:
            return {'error': 'no edges'}
        pts_img = np.stack((pts[:, 1] + x0, pts[:, 0] + y0), axis=1).astype(np.float32)

    try:
        center, radius, inliers = _ransac_circle(pts_img, ransac_thresh, ransac_iter)
    except ValueError as e:
        return {'error': str(e)}

    inlier_pts = pts_img[inliers]
    result: Dict[str, Any] = {
        'center': tuple(center.tolist()),
        'radius': float(radius),
        'diameter': float(radius * 2.0),
    }
    if return_debug:
        result['inlier_pts'] = inlier_pts
        result['all_edge_pts'] = pts_img
    return result


if __name__ == '__main__':
    import argparse, sys
    parser = argparse.ArgumentParser(description='Circle measurement quick test')
    parser.add_argument('image', help='image path')
    parser.add_argument('--cx', type=float, required=True)
    parser.add_argument('--cy', type=float, required=True)
    parser.add_argument('--r', type=float, required=True, help='estimated radius')
    args = parser.parse_args()
    img = cv2.imread(args.image)
    res = measure_circle(img, args.cx, args.cy, args.r, return_debug=True)
    print(res)
    if 'error' not in res:
        c = tuple(map(int, res['center']))
        r = int(res['radius'])
        cv2.circle(img, c, r, (0, 255, 0), 1)
        for pt in res['inlier_pts']:
            cv2.circle(img, tuple(pt.astype(int)), 1, (0, 0, 255), -1)
        cv2.imshow('circle', img)
        cv2.waitKey(0)
