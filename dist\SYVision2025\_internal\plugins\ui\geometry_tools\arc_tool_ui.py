# -*- coding: utf-8 -*-
"""交互式圆弧测量 UI。

与卡尺工具类似的三阶段流程：
1. ROI 圆框：拖拽鼠标定义近似圆（中心与半径）
2. 拟合边缘：调用 geometry_tools.arc_tool.measure_arc 进行 RANSAC 圆拟合
3. 测量：显示圆心、半径、弧角、弧长

仅支持调试可视，方便后续集成到主系统。
"""
from __future__ import annotations

import pathlib, sys
from typing import Optional, Tuple, Any
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

import numpy as np
import yaml
from pathlib import Path
# --- 鲁棒地导入recipe_sync ---
try:
    from ._recipe_sync import update_mm_in_recipe, update_rois_in_recipe, update_arc_params
except ImportError:
    try:
        import sys, pathlib
        _proj_root = pathlib.Path(__file__).resolve().parents[3]
        if str(_proj_root) not in sys.path:
            sys.path.insert(0, str(_proj_root))
        from plugins.ui.geometry_tools._recipe_sync import update_mm_in_recipe, update_rois_in_recipe, update_arc_params
    except ImportError:
        def update_mm_in_recipe(*args, **kwargs):
            pass
        def update_rois_in_recipe(*args, **kwargs):
            pass
        def update_arc_params(*args, **kwargs):
            pass

PREVIEW_SIZE = 550  # 画布固定大小
CFG_DIR = Path(__file__).resolve().parents[3] / 'configs' / 'arc_measure'
CFG_DIR.mkdir(parents=True, exist_ok=True)
import cv2
from PIL import Image, ImageTk

# 运行单文件测试时修正 import 路径
try:
    from plugins.geometry_tools.arc_tool import measure_arc
except ImportError:
    _proj_root = pathlib.Path(__file__).resolve().parents[3]
    if str(_proj_root) not in sys.path:
        sys.path.insert(0, str(_proj_root))
    from plugins.geometry_tools.arc_tool import measure_arc


class ArcToolUI(tk.Toplevel):
    _ST_IDLE = 'idle'
    _ST_ROI = 'roi_defined'
    _ST_FITTED = 'fitted'
    _ST_MEASURED = 'measured'

    def __init__(self, master: Optional[tk.Widget] = None, img: Optional[np.ndarray] = None, *, recipe_path: Optional[str] = None):
        super().__init__(master)
        # 保存主配方路径，后续写回 rois/mm
        self._recipe_path = recipe_path
        self.title('圆弧测量工具')
        self.geometry('900x700')
        self.minsize(800, 600)
        # 关闭窗口回调，确保完全退出
        self.protocol('WM_DELETE_WINDOW', self._on_close)

        # 图像数据
        self.img_orig: Optional[np.ndarray] = None
        self.img_display: Optional[np.ndarray] = None

        # 交互数据
        self.center_est: Optional[Tuple[float, float]] = None
        self.r_est: Optional[float] = None
        self._dragging = False

        # 多 ROI
        self.rois: list[dict[str, Any]] = []
        self.current_idx: Optional[int] = None
        self.next_id: int = 1
        self._recipe_path: Optional[Path] = Path(recipe_path) if recipe_path else None  # 递增编号
        

        # ---- 布局 ----
        self._img_dirty = False
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(1, weight=1)  # 右侧画布可拉伸

        # 左侧参数面板 - 使用滚动框架
        main_frame = ttk.Frame(self)
        main_frame.grid(row=0, column=0, sticky='ns', padx=5, pady=5)

        # 创建滚动画布
        canvas_scroll = tk.Canvas(main_frame, width=300, height=650)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas_scroll.yview)
        scrollable_frame = ttk.Frame(canvas_scroll)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas_scroll.configure(scrollregion=canvas_scroll.bbox("all"))
        )

        canvas_scroll.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas_scroll.configure(yscrollcommand=scrollbar.set)

        canvas_scroll.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        p_frame = scrollable_frame

        # 按钮区域 - 使用网格布局更紧凑
        btn_frame = ttk.Frame(p_frame)
        btn_frame.pack(fill=tk.X, pady=2)

        ttk.Button(btn_frame, text='打开图像', command=self._open_image).grid(row=0, column=0, columnspan=2, sticky='ew', pady=1)
        ttk.Button(btn_frame, text='拟合边缘', command=self._fit_edges, state='disabled').grid(row=1, column=0, sticky='ew', padx=(0,2), pady=1)
        ttk.Button(btn_frame, text='测量', command=self._measure_arc, state='disabled').grid(row=1, column=1, sticky='ew', pady=1)
        ttk.Button(btn_frame, text='清除标记', command=self._clear_marks).grid(row=2, column=0, sticky='ew', padx=(0,2), pady=1)
        ttk.Button(btn_frame, text='保存配置', command=self._save_yaml).grid(row=2, column=1, sticky='ew', pady=1)
        ttk.Button(btn_frame, text='载入配置', command=self._load_yaml).grid(row=3, column=0, columnspan=2, sticky='ew', pady=1)

        btn_frame.columnconfigure(0, weight=1)
        btn_frame.columnconfigure(1, weight=1)
        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        ttk.Label(p_frame, text='圆弧列表').pack(anchor='w')
        self.tree = ttk.Treeview(p_frame, columns=('state','len','ang','r'), show='headings', height=6)
        self.tree.heading('state', text='阶段')
        self.tree.heading('len', text='弧长(px)')
        self.tree.heading('ang', text='角度(°)')
        self.tree.heading('r', text='半径(px)')
        self.tree.column('state', width=70, anchor='center')
        self.tree.column('len', width=80, anchor='center')
        self.tree.column('ang', width=80, anchor='center')
        self.tree.column('r', width=80, anchor='center')
        self.tree.pack(fill=tk.BOTH, expand=False)
        self.tree.bind('<<TreeviewSelect>>', self._on_tree_select)

        # 参数区域 - 使用网格布局
        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        ttk.Label(p_frame, text='算法参数', font=('Arial', 9, 'bold')).pack(anchor='w')

        param_frame = ttk.Frame(p_frame)
        param_frame.pack(fill=tk.X, pady=2)

        # 第一行：Canny参数
        ttk.Label(param_frame, text='Canny1:').grid(row=0, column=0, sticky='w', padx=(0,2))
        self.th1_var = tk.IntVar(value=50)
        ttk.Entry(param_frame, textvariable=self.th1_var, width=6).grid(row=0, column=1, padx=(0,5))
        ttk.Label(param_frame, text='Canny2:').grid(row=0, column=2, sticky='w', padx=(0,2))
        self.th2_var = tk.IntVar(value=150)
        ttk.Entry(param_frame, textvariable=self.th2_var, width=6).grid(row=0, column=3)

        # 第二行：RANSAC和mm/px
        ttk.Label(param_frame, text='RANSAC:').grid(row=1, column=0, sticky='w', padx=(0,2), pady=(2,0))
        self.ransac_var = tk.DoubleVar(value=1.5)
        ttk.Entry(param_frame, textvariable=self.ransac_var, width=6).grid(row=1, column=1, padx=(0,5), pady=(2,0))
        ttk.Label(param_frame, text='mm/px:').grid(row=1, column=2, sticky='w', padx=(0,2), pady=(2,0))
        self.mm_var = tk.DoubleVar(value=0.0)
        ttk.Entry(param_frame, textvariable=self.mm_var, width=6).grid(row=1, column=3, pady=(2,0))

        # 第三行：估计半径和Smooth
        ttk.Label(param_frame, text='半径:').grid(row=2, column=0, sticky='w', padx=(0,2), pady=(2,0))
        self.def_r_var = tk.DoubleVar(value=50.0)
        ttk.Entry(param_frame, textvariable=self.def_r_var, width=6).grid(row=2, column=1, padx=(0,5), pady=(2,0))
        ttk.Label(param_frame, text='Smooth:').grid(row=2, column=2, sticky='w', padx=(0,2), pady=(2,0))
        self.smooth_var = tk.DoubleVar(value=0.2)
        ttk.Entry(param_frame, textvariable=self.smooth_var, width=6).grid(row=2, column=3, pady=(2,0))

        # 第四行：Score阈值和亚像素窗口
        ttk.Label(param_frame, text='Score:').grid(row=3, column=0, sticky='w', padx=(0,2), pady=(2,0))
        self.score_var = tk.DoubleVar(value=0.8)
        ttk.Entry(param_frame, textvariable=self.score_var, width=6).grid(row=3, column=1, padx=(0,5), pady=(2,0))
        ttk.Label(param_frame, text='亚像素:').grid(row=3, column=2, sticky='w', padx=(0,2), pady=(2,0))
        self.subpixel_window_var = tk.IntVar(value=5)
        ttk.Entry(param_frame, textvariable=self.subpixel_window_var, width=6).grid(row=3, column=3, pady=(2,0))

        # 第五行：ROI大小控制
        ttk.Label(param_frame, text='ROI大小:').grid(row=4, column=0, sticky='w', padx=(0,2), pady=(2,0))
        self.roi_size_var = tk.IntVar(value=80)
        roi_entry = ttk.Entry(param_frame, textvariable=self.roi_size_var, width=6)
        roi_entry.grid(row=4, column=1, padx=(0,5), pady=(2,0))
        # 绑定ROI大小变化事件，实时更新显示
        self.roi_size_var.trace('w', lambda *args: self._draw_overlays())

        # ROI快捷设置按钮
        roi_quick_frame = ttk.Frame(param_frame)
        roi_quick_frame.grid(row=4, column=2, columnspan=2, sticky='w', padx=(5,0), pady=(2,0))

        ttk.Button(roi_quick_frame, text='40', width=3,
                  command=lambda: self.roi_size_var.set(40)).pack(side='left', padx=(0,2))
        ttk.Button(roi_quick_frame, text='60', width=3,
                  command=lambda: self.roi_size_var.set(60)).pack(side='left', padx=(0,2))
        ttk.Button(roi_quick_frame, text='80', width=3,
                  command=lambda: self.roi_size_var.set(80)).pack(side='left', padx=(0,2))
        ttk.Button(roi_quick_frame, text='120', width=3,
                  command=lambda: self.roi_size_var.set(120)).pack(side='left')

        # ROI扩展因子（保留兼容性）
        self.roi_expand_var = tk.DoubleVar(value=1.0)

        # 第六行：自动ROI设置
        ttk.Button(param_frame, text='自动ROI', command=self._auto_set_roi_size).grid(row=5, column=0, sticky='w', pady=(4,0))
        ttk.Label(param_frame, text='基于半径自动设置').grid(row=5, column=1, columnspan=3, sticky='w', padx=(5,0), pady=(4,0))

        # 复选框区域
        checkbox_frame = ttk.Frame(param_frame)
        checkbox_frame.grid(row=6, column=0, columnspan=4, sticky='w', pady=(4,0))

        self.subpixel_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(checkbox_frame, text='亚像素拟合', variable=self.subpixel_var).pack(side='left')

        self.adaptive_roi_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(checkbox_frame, text='自适应ROI', variable=self.adaptive_roi_var).pack(side='left', padx=(10,0))

        # 结果显示区域
        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        ttk.Label(p_frame, text='测量结果', font=('Arial', 9, 'bold')).pack(anchor='w')

        result_frame = ttk.Frame(p_frame)
        result_frame.pack(fill=tk.X, pady=2)

        self.result_var = tk.StringVar(value='弧长: - px / - mm\n角度: - °\n半径: - px / - mm')
        result_label = ttk.Label(result_frame, textvariable=self.result_var, wraplength=250, justify='left')
        result_label.pack(anchor='w')

        # 右侧画布
        self.canvas = tk.Canvas(self, bg='black', width=PREVIEW_SIZE, height=PREVIEW_SIZE)
        self.canvas.grid(row=0, column=1, sticky='nsew', padx=5, pady=5)
        self.canvas.bind('<ButtonPress-1>', self._on_drag_start)
        self.canvas.bind('<B1-Motion>', self._on_drag_move)
        self.canvas.bind('<ButtonRelease-1>', self._on_drag_end)

        # 缩放/平移参数
        self._zoom = 1.0
        self._img_tk: Optional[ImageTk.PhotoImage] = None
        self.canvas.bind('<MouseWheel>', self._on_zoom)
        self.canvas.bind('<ButtonPress-2>', self._on_pan_start)
        self.canvas.bind('<B2-Motion>', self._on_pan_move)
        self._pan_start = (0, 0)
        self._offset = [0, 0]

        if img is not None:
            self.load_image(img)

    # ------- ROI 列表 -------
    # ------- ROI 列表 -------
    def _on_close(self):
        """窗口关闭自身并关闭根窗口"""
        try:
            self.destroy()
        finally:
            if self.master and isinstance(self.master, tk.Tk):
                self.master.destroy()

    def _clear_marks(self):
        """清空所有 ROI 并重置状态"""
        self.rois.clear()
        self.current_idx = None
        self.next_id = 1
        self.tree.delete(*self.tree.get_children())
        self.result_var.set('弧长: - px / - mm  角度: - °  半径: - px / - mm')
        self._update_btn_state()
        self._draw_overlays()

    def _update_btn_state(self):
        """更新侧边按钮可用状态"""
        p_frame = self.children['!frame']  # 左面板
        widgets = p_frame.winfo_children()
        # 顺序：0 打开图像 1 拟合边缘 2 测量
        fit_btn = widgets[1]
        meas_btn = widgets[2]
        fit_btn['state'] = 'disabled'
        meas_btn['state'] = 'disabled'
        if self.current_idx is None:
            return
        stage = self.rois[self.current_idx]['stage']
        if stage == self._ST_ROI:
            fit_btn['state'] = 'normal'
        elif stage == self._ST_FITTED:
            meas_btn['state'] = 'normal'



    def _draw_point(self, x: float, y: float, color: str, radius: int = 2):
        xc = x * self._zoom + self._offset[0]
        yc = y * self._zoom + self._offset[1]
        self.canvas.create_oval(xc - radius, yc - radius, xc + radius, yc + radius,
                                outline=color, fill=color, tags='ov')

    def _draw_circle(self, cx: float, cy: float, r: float, color: str):
        cx_c = cx * self._zoom + self._offset[0]
        cy_c = cy * self._zoom + self._offset[1]
        r_c = r * self._zoom
        self.canvas.create_oval(cx_c - r_c, cy_c - r_c, cx_c + r_c, cy_c + r_c,
                                outline=color, width=1, tags='ov')

    def _refresh_tree(self):
        self.tree.delete(*self.tree.get_children())
        for idx, roi in enumerate(self.rois):
            res = roi.get('result')
            length = f"{res['arc_length_px']:.1f}" if res and 'error' not in res else '-'
            angle = f"{np.degrees(res['central_angle_rad']):.1f}" if res and 'error' not in res else '-'
            radius = f"{res['radius']:.1f}" if res and 'error' not in res else '-'
            self.tree.insert('', 'end', iid=str(idx), values=(roi['stage'], length, angle, radius), text=f"#{roi['id']}")
        if self.current_idx is not None:
            self.tree.selection_set(str(self.current_idx))

    def _on_tree_select(self, evt):
        """选中树表行回调"""
        sel = self.tree.selection()
        if not sel:
            self.current_idx = None
            self.result_var.set('弧长: - px / - mm  角度: - °  半径: - px / - mm')
            
            return
        self.current_idx = int(sel[0])
        roi = self.rois[self.current_idx]
        if roi.get('result') and 'error' not in roi['result'] and roi['stage']!=self._ST_ROI:
            res = roi['result']
            angle_deg = np.degrees(res['central_angle_rad'])
            mm_per_px = self.mm_var.get()
            if mm_per_px > 0:
                len_mm = res['arc_length_px'] * mm_per_px
                rad_mm = res['radius'] * mm_per_px
                self.result_var.set(
                    f"弧长: {res['arc_length_px']:.2f} px / {len_mm:.2f} mm  "
                    f"角度: {angle_deg:.2f}°  半径:{res['radius']:.2f}px / {rad_mm:.2f} mm")
            else:
                self.result_var.set(
                    f"弧长: {res['arc_length_px']:.2f} px  角度: {angle_deg:.2f}°  半径:{res['radius']:.2f}px")
        else:
            self.result_var.set('弧长: - px / - mm  角度: - °  半径: - px / - mm')
        # 根据新选中项刷新按钮和覆盖
        self._update_btn_state()
        self._draw_overlays()

    # ---------------- 图像与坐标 -----------------
    def load_image(self, img: np.ndarray):
        self.img_orig = img.copy()
        self._reset_view()

    def _reset_view(self):
        self._zoom = 1.0
        self._offset = [0, 0]
        if self.img_orig is None:
            return
        h, w = self.img_orig.shape[:2]
        disp = cv2.resize(self.img_orig, (int(w*self._zoom), int(h*self._zoom)))
        self.img_display = disp
        img_rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
        img_pil = Image.fromarray(img_rgb)
        self._img_tk = ImageTk.PhotoImage(img_pil)
        self.canvas.config(scrollregion=(0,0,disp.shape[1], disp.shape[0]))
        self.canvas.delete('img')
        self.canvas.create_image(self._offset[0], self._offset[1], anchor='nw', image=self._img_tk, tags='img')

    def _canvas_to_img(self, x: float, y: float) -> Tuple[float, float]:
        return (x - self._offset[0]) / self._zoom, (y - self._offset[1]) / self._zoom

    def _update_canvas_image(self):
        if self.img_orig is None:
            return
        h, w = self.img_orig.shape[:2]
        disp = cv2.resize(self.img_orig, (int(w*self._zoom), int(h*self._zoom)))
        self.img_display = disp
        img_rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
        img_pil = Image.fromarray(img_rgb)
        self._img_tk = ImageTk.PhotoImage(img_pil)
        self.canvas.delete('img')
        self.canvas.create_image(self._offset[0], self._offset[1], anchor='nw', image=self._img_tk, tags='img')

    # ---------------- 事件处理 -----------------
    def _on_drag_start(self, e):
        if self.img_orig is None:
            return
        self._dragging = True
        self.center_est = self._canvas_to_img(e.x, e.y)
        self.r_est = 0.0
        

    def _on_drag_move(self, e):
        if not self._dragging:
            return
        cx, cy = self.center_est
        px, py = self._canvas_to_img(e.x, e.y)
        self.r_est = float(np.hypot(px - cx, py - cy))
        # 即时预览小圆，提高互动体验但不重绘整幅图
        self.canvas.delete('tmp')
        cx_c, cy_c = (cx*self._zoom+self._offset[0], cy*self._zoom+self._offset[1])
        r_c = self.r_est * self._zoom
        self.canvas.create_oval(cx_c-r_c, cy_c-r_c, cx_c+r_c, cy_c+r_c, outline='yellow', tags='tmp')
        

    def _on_drag_end(self, e):
        if not self._dragging:
            return
        self._dragging = False
        if self.r_est < 5:
            
            return
        # 添加新 ROI
        roi = {
            'id': self.next_id,
            'cx': self.center_est[0],
            'cy': self.center_est[1],
            'r': self.r_est,
            'stage': self._ST_ROI,
            'result': None
        }
        self.rois.append(roi)
        self.next_id += 1
        
        self.current_idx = len(self.rois)-1
        # 清除临时圈
        self.canvas.delete('tmp')
        # 更新界面，确保拟合按钮可用
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()
        # 自动拟合边缘
        self._fit_edges()
        
        

    # 缩放与平移
    def _on_zoom(self, e):
        if self.img_orig is None:
            return
        factor = 1.1 if e.delta > 0 else 0.9
        self._zoom *= factor
        self._update_canvas_image()
        

    def _on_pan_start(self, e):
        self._pan_start = (e.x, e.y)

    def _on_pan_move(self, e):
        dx, dy = e.x - self._pan_start[0], e.y - self._pan_start[1]
        self._pan_start = (e.x, e.y)
        self._offset[0] += dx
        self._offset[1] += dy
        self._update_canvas_image()
        

    # ---------------- 按钮动作 -----------------
    def _open_image(self):
        path = filedialog.askopenfilename(filetypes=[('Image','*.png;*.jpg;*.bmp')])
        if not path:
            return
        img = cv2.imread(path)
        if img is None:
            messagebox.showerror('错误', '无法读取图像')
            return
        self.load_image(img)
        

    def _fit_edges(self):
        if self.current_idx is None:
            return
        roi = self.rois[self.current_idx]
        res = measure_arc(
            self.img_orig,
            roi['cx'], roi['cy'], roi['r'],
            canny_th1=self.th1_var.get(),
            canny_th2=self.th2_var.get(),
            ransac_thresh=self.ransac_var.get(),
            subpixel=self.subpixel_var.get(),
            subpixel_window=self.subpixel_window_var.get(),
            adaptive_roi=self.adaptive_roi_var.get(),
            roi_expand_factor=self.roi_expand_var.get(),
            roi_size=self.roi_size_var.get(),
            return_debug=True
        )
        roi['result'] = res
        if 'error' in res:
            messagebox.showerror('错误', res['error'])
            return
        roi['stage'] = self._ST_FITTED
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()
        
        

    # -------- YAML 保存/加载 --------
    def _to_py(self, o):
        import numpy as _np
        if isinstance(o, dict):
            return {k: self._to_py(v) for k, v in o.items()}
        if isinstance(o, (list, tuple)):
            return [self._to_py(v) for v in o]
        if isinstance(o, _np.generic):
            return o.item()
        return o

    def _save_yaml(self, ask: bool = True):
        if not self.rois:
            messagebox.showwarning('提示','没有 ROI 可以保存')
            return
        cfg = {
            'algo': {
                'canny_th1': self.th1_var.get(),
                'canny_th2': self.th2_var.get(),
                'ransac_thresh': self.ransac_var.get(),
                'subpixel': self.subpixel_var.get(),
                'subpixel_window': self.subpixel_window_var.get(),
                'adaptive_roi': self.adaptive_roi_var.get(),
                'roi_expand_factor': self.roi_expand_var.get(),
                'roi_size': self.roi_size_var.get(),
            },
            'mm_per_px': self.mm_var.get(),
            'r_est': self.def_r_var.get(),
            'smooth_alpha': self.smooth_var.get(),
            'score_th': self.score_var.get(),
        'rois': [
                    {'id': r['id'], 'cx': r['cx'], 'cy': r['cy'], 'r': r['r']}
                    for r in self.rois
                ]
        }
        if hasattr(self, 'last_info') and self.last_info:
            cfg['detect_info'] = self._to_py(self.last_info)
        if ask:
            path = filedialog.asksaveasfilename(defaultextension='.yml', initialdir=str(CFG_DIR), initialfile='default.yml', filetypes=[('YAML','*.yml;*.yaml')])
            if not path:
                return
        else:
            path = CFG_DIR / 'default.yml'
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(cfg, f, allow_unicode=True)
        if self._recipe_path:
            update_rois_in_recipe(self._recipe_path, 'arc_tool', cfg['rois'])
            update_mm_in_recipe(self._recipe_path, 'arc_tool', self.mm_var.get())
        update_arc_params(self._recipe_path, r_est=self.def_r_var.get(), smooth_alpha=self.smooth_var.get(), score_th=self.score_var.get())
        messagebox.showinfo('保存完成', f'配置已保存到 {path}')

    def _load_yaml(self):
        path = filedialog.askopenfilename(filetypes=[('YAML','*.yml;*.yaml')], initialdir=str(CFG_DIR))
        if not path:
            return
        data = yaml.safe_load(open(path, 'r', encoding='utf-8')) or {}
        # 基本算法参数
        algo = data.get('algo', {})
        self.th1_var.set(algo.get('canny_th1', 50))
        self.th2_var.set(algo.get('canny_th2', 150))
        self.ransac_var.set(algo.get('ransac_thresh', 2.0))
        self.subpixel_var.set(algo.get('subpixel', True))
        self.subpixel_window_var.set(algo.get('subpixel_window', 5))
        self.adaptive_roi_var.set(algo.get('adaptive_roi', False))
        self.roi_expand_var.set(algo.get('roi_expand_factor', 1.0))
        self.roi_size_var.set(algo.get('roi_size', 80))
        self.mm_var.set(data.get('mm_per_px', 0.0))
        self.def_r_var.set(data.get('r_est', 50.0))
        self.smooth_var.set(data.get('smooth_alpha', 0.2))
        self.score_var.set(data.get('score_th', 0.8))
        # 重新加载 ROI 列表
        self.rois = [{
            'id': roi['id'],
            'cx': roi['cx'],
            'cy': roi['cy'],
            'r': roi['r'],
            'stage': self._ST_ROI,
            'result': None
        } for roi in data.get('rois', [])]

        self._refresh_tree()
        self.next_id = max([r['id'] for r in self.rois], default=0) + 1
        self.current_idx = 0 if self.rois else None
        # 选中第一行，刷新按钮
        if self.rois:
            try:
                self.tree.selection_set('0')
                self.tree.focus('0')
            except Exception:
                pass
        self.last_info = data.get('detect_info', {})
        # 更新按钮 + 画布
        self._update_btn_state()
        self._draw_overlays()
        
        
        

    # -------- 测量函数 --------
    def _measure_arc(self):
        """根据拟合结果计算弧长/半径，并按像素+毫米双单位显示"""
        if self.current_idx is None:
            return
        roi = self.rois[self.current_idx]
        if roi['stage'] != self._ST_FITTED or not roi.get('result'):
            return
        res = roi['result']
        angle_deg = np.degrees(res['central_angle_rad'])
        mm_per_px = self.mm_var.get()
        if mm_per_px > 0:
            len_mm = res['arc_length_px'] * mm_per_px
            rad_mm = res['radius'] * mm_per_px
            self.result_var.set(
                f"弧长: {res['arc_length_px']:.2f} px / {len_mm:.2f} mm  "
                f"角度: {angle_deg:.2f}°  半径:{res['radius']:.2f}px / {rad_mm:.2f} mm")
        else:
            self.result_var.set(
                f"弧长: {res['arc_length_px']:.2f} px  角度: {angle_deg:.2f}°  半径:{res['radius']:.2f}px")
        roi['stage'] = self._ST_MEASURED
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()
    
    
    
    


    def _on_close(self):
        """窗口关闭时销毁自身并退出根 mainloop"""
        try:
            self.destroy()
        finally:
            if self.master and isinstance(self.master, tk.Tk):
                self.master.destroy()

    def _clear_marks(self):
        """清空所有 ROI 并重置界面"""
        self.rois.clear()
        self.current_idx = None
        self.next_id = 1
        self.tree.delete(*self.tree.get_children())
        self.result_var.set('弧长: - px / - mm  角度: - °  半径: - px / - mm')
        self._update_btn_state()
        self._draw_overlays()

    def _auto_set_roi_size(self):
        """根据当前选中ROI的估计半径自动设置ROI大小"""
        if self.current_idx is None:
            messagebox.showwarning('提示', '请先选择一个ROI')
            return

        roi = self.rois[self.current_idx]
        r_est = roi['r']

        # 根据估计半径计算合适的ROI大小
        # 规则：ROI大小 = 半径 * 2.5，确保覆盖完整圆角
        auto_roi_size = int(r_est * 2.5)

        # 限制在合理范围内
        auto_roi_size = max(30, min(auto_roi_size, 200))

        # 设置ROI大小
        self.roi_size_var.set(auto_roi_size)

        # 刷新显示
        self._draw_overlays()

        messagebox.showinfo('自动设置', f'根据估计半径 {r_est:.1f}px\n自动设置ROI大小为 {auto_roi_size}px')

    def _update_btn_state(self):
        """更新侧边按钮可用状态"""
        try:
            # 由于布局改变，需要通过名称查找按钮
            main_frame = self.children['!frame']
            canvas_scroll = main_frame.children['!canvas']
            scrollable_frame = canvas_scroll.nametowidget(canvas_scroll.find_all()[0])
            btn_frame = scrollable_frame.children['!frame']

            # 获取按钮
            fit_btn = None
            meas_btn = None
            for widget in btn_frame.winfo_children():
                if isinstance(widget, ttk.Button):
                    text = widget['text']
                    if text == '拟合边缘':
                        fit_btn = widget
                    elif text == '测量':
                        meas_btn = widget

            if fit_btn is None or meas_btn is None:
                return

            fit_btn['state'] = 'disabled'
            meas_btn['state'] = 'disabled'
            if self.current_idx is None:
                return
            stage = self.rois[self.current_idx]['stage']
            if stage == self._ST_ROI:
                fit_btn['state'] = 'normal'
            elif stage == self._ST_FITTED:
                fit_btn['state'] = 'normal'
                meas_btn['state'] = 'normal'
        except Exception:
            # 如果找不到按钮，忽略错误
            pass

    # ---------------- 绘制 -----------------
    def _draw_overlays(self):
        if self.img_orig is None:
            return
        # 底图先刷新，再删除旧覆盖，顺序很重要
        self._update_canvas_image()
        self.canvas.delete('ov')
        # 绘制全部 ROI
        for idx, roi in enumerate(self.rois):
            cx, cy, r = roi['cx'], roi['cy'], roi['r']
            cx_c = cx * self._zoom + self._offset[0]
            cy_c = cy * self._zoom + self._offset[1]
            r_c = r * self._zoom
            color = 'green'
            self.canvas.create_oval(cx_c - r_c, cy_c - r_c, cx_c + r_c, cy_c + r_c,
                                    outline=color, width=2 if idx == self.current_idx else 1, tags='ov')
            self.canvas.create_text(cx_c, cy_c, text=str(roi['id']), fill=color, tags='ov')

            # 为当前选中的ROI显示检测区域大小
            if idx == self.current_idx:
                try:
                    roi_size = self.roi_size_var.get()
                    if roi_size > 0:
                        half_size = roi_size // 2
                        roi_half_c = half_size * self._zoom
                        self.canvas.create_rectangle(cx_c - roi_half_c, cy_c - roi_half_c,
                                                   cx_c + roi_half_c, cy_c + roi_half_c,
                                                   outline='orange', dash=(3, 3), width=1, tags='ov')
                except (tk.TclError, ValueError):
                    pass  # 忽略无效的ROI大小值
        # 正在拖拽的临时 ROI
        if self._dragging and self.center_est and self.r_est > 0:
            cx, cy = self.center_est
            r = self.r_est
            cx_c = cx * self._zoom + self._offset[0]
            cy_c = cy * self._zoom + self._offset[1]
            r_c = r * self._zoom
            self.canvas.create_oval(cx_c - r_c, cy_c - r_c, cx_c + r_c, cy_c + r_c,
                                    outline='cyan', dash=(4, 2), width=1, tags='ov')

            # 绘制ROI大小预览框
            try:
                roi_size = self.roi_size_var.get()
                if roi_size > 0:
                    half_size = roi_size // 2
                    roi_half_c = half_size * self._zoom
                    self.canvas.create_rectangle(cx_c - roi_half_c, cy_c - roi_half_c,
                                               cx_c + roi_half_c, cy_c + roi_half_c,
                                               outline='orange', dash=(2, 2), width=1, tags='ov')
                    self.canvas.create_text(cx_c, cy_c - roi_half_c - 15,
                                          text=f'ROI: {roi_size}x{roi_size}',
                                          fill='orange', tags='ov')
            except (tk.TclError, ValueError):
                pass  # 忽略无效的ROI大小值
        # 调试信息
        if self.current_idx is not None:
            roi = self.rois[self.current_idx]
            res = roi.get('result')
            if res and 'error' not in res and roi['stage'] in (self._ST_FITTED, self._ST_MEASURED):
                for pt in res.get('inlier_pts', [])[:800]:
                    self._draw_point(pt[0], pt[1], 'red')
                cx, cy = res['center']
                r = res['radius']
                self._draw_circle(cx, cy, r, 'green')
                for ang in (res['start_angle_rad'], res['end_angle_rad']):
                    x = cx + r * np.cos(ang)
                    y = cy + r * np.sin(ang)
                    self._draw_point(x, y, 'blue', 4)

    def _draw_point(self, x: float, y: float, color: str, radius: int = 2):
        xc = x * self._zoom + self._offset[0]
        yc = y * self._zoom + self._offset[1]
        self.canvas.create_oval(xc - radius, yc - radius, xc + radius, yc + radius,
                                outline=color, fill=color, tags='ov')

    def _draw_circle(self, cx: float, cy: float, r: float, color: str):
        cx_c = cx * self._zoom + self._offset[0]
        cy_c = cy * self._zoom + self._offset[1]
        r_c = r * self._zoom
        self.canvas.create_oval(cx_c - r_c, cy_c - r_c, cx_c + r_c, cy_c + r_c,
                                outline=color, width=1, tags='ov')


if __name__ == '__main__':
    import cv2, sys
    img_path = sys.argv[1] if len(sys.argv) > 1 else None
    img = cv2.imread(img_path) if img_path else None
    root = tk.Tk()
    root.withdraw()
    ArcToolUI(root, img)
    root.mainloop()
