"""位置修正插件

提供多种位置修正功能：
1. 透视变换修正 - 俯视矫正
2. 旋转修正 - 角度校正
3. 平移修正 - 位置偏移校正
4. 缩放修正 - 尺寸校正
5. 组合修正 - 多种修正的组合

用法：
- 可以单独使用某种修正方式
- 也可以组合多种修正方式
- 支持手动设置参数或自动检测
"""
from __future__ import annotations
from typing import Dict, Any, Tuple, Optional
import cv2
import numpy as np
import importlib
from plugins.plugin_base import PluginBase


class PositionCorrectionPlugin(PluginBase):
    name = "position_correction"
    label = "位置修正"
    category = "几何测量"
    
    params: Dict[str, Any] = {
        # 修正模式选择
        "correction_mode": "auto",  # auto, manual, perspective, rotation, translation, scale, combined
        
        # 透视变换参数
        "enable_perspective": True,
        "homography": [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0],  # 3x3单应性矩阵
        "dst_width": 640,
        "dst_height": 480,
        
        # 旋转修正参数
        "enable_rotation": True,
        "rotation_angle": 0.0,  # 度数
        "rotation_center_x": 320.0,  # 旋转中心X
        "rotation_center_y": 240.0,  # 旋转中心Y
        "auto_detect_angle": True,  # 自动检测角度
        
        # 平移修正参数
        "enable_translation": False,
        "offset_x": 0.0,  # X方向偏移
        "offset_y": 0.0,  # Y方向偏移
        
        # 缩放修正参数
        "enable_scale": False,
        "scale_x": 1.0,  # X方向缩放
        "scale_y": 1.0,  # Y方向缩放
        
        # 自动检测参数
        "auto_detect_method": "contour",  # contour, template, feature
        "canny_th1": 50,
        "canny_th2": 150,
        "min_contour_area": 1000,
        
        # 调试参数
        "debug_draw": True,
        "show_grid": False,
        "grid_size": 50,
    }
    
    param_labels = {
        "correction_mode": "修正模式",
        "enable_perspective": "启用透视修正",
        "dst_width": "目标宽度",
        "dst_height": "目标高度", 
        "enable_rotation": "启用旋转修正",
        "rotation_angle": "旋转角度(度)",
        "rotation_center_x": "旋转中心X",
        "rotation_center_y": "旋转中心Y",
        "auto_detect_angle": "自动检测角度",
        "enable_translation": "启用平移修正",
        "offset_x": "X偏移",
        "offset_y": "Y偏移",
        "enable_scale": "启用缩放修正",
        "scale_x": "X缩放",
        "scale_y": "Y缩放",
        "auto_detect_method": "自动检测方法",
        "canny_th1": "Canny阈值1",
        "canny_th2": "Canny阈值2",
        "min_contour_area": "最小轮廓面积",
        "debug_draw": "调试绘制",
        "show_grid": "显示网格",
        "grid_size": "网格大小",
    }

    def __init__(self, **params):
        super().__init__(**params)
        self._last_correction_matrix = None
        self._last_correction_info = {}

    def _detect_main_angle(self, img: np.ndarray) -> float:
        """自动检测主要物体的角度"""
        if img.ndim == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
            
        # 边缘检测
        edges = cv2.Canny(gray, self.params["canny_th1"], self.params["canny_th2"])
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return 0.0
            
        # 找到最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        
        if cv2.contourArea(largest_contour) < self.params["min_contour_area"]:
            return 0.0
            
        # 计算最小外接矩形
        rect = cv2.minAreaRect(largest_contour)
        angle = rect[2]
        
        # 调整角度范围到 [-45, 45]
        if angle < -45:
            angle += 90
        elif angle > 45:
            angle -= 90
            
        return float(angle)

    def _apply_perspective_correction(self, img: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用透视变换修正"""
        H_list = self.params["homography"]
        if len(H_list) != 9:
            return img, np.eye(3)
            
        H = np.array(H_list, dtype=np.float64).reshape(3, 3)
        dst_size = (self.params["dst_width"], self.params["dst_height"])
        
        corrected = cv2.warpPerspective(img, H, dst_size)
        return corrected, H

    def _apply_rotation_correction(self, img: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用旋转修正"""
        h, w = img.shape[:2]
        
        # 确定旋转角度
        if self.params["auto_detect_angle"]:
            angle = self._detect_main_angle(img)
        else:
            angle = self.params["rotation_angle"]
            
        # 确定旋转中心
        center_x = self.params["rotation_center_x"]
        center_y = self.params["rotation_center_y"]
        
        # 如果中心超出图像范围，使用图像中心
        if center_x < 0 or center_x >= w or center_y < 0 or center_y >= h:
            center_x, center_y = w // 2, h // 2
            
        center = (center_x, center_y)
        
        # 计算旋转矩阵
        M = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # 应用旋转
        corrected = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)
        
        # 转换为3x3矩阵
        H = np.vstack([M, [0, 0, 1]]).astype(np.float32)
        
        return corrected, H

    def _apply_translation_correction(self, img: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用平移修正"""
        offset_x = self.params["offset_x"]
        offset_y = self.params["offset_y"]
        
        h, w = img.shape[:2]
        
        # 创建平移矩阵
        M = np.float32([[1, 0, offset_x], [0, 1, offset_y]])
        
        # 应用平移
        corrected = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)
        
        # 转换为3x3矩阵
        H = np.vstack([M, [0, 0, 1]]).astype(np.float32)
        
        return corrected, H

    def _apply_scale_correction(self, img: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用缩放修正"""
        scale_x = self.params["scale_x"]
        scale_y = self.params["scale_y"]
        
        h, w = img.shape[:2]
        
        # 创建缩放矩阵
        M = np.float32([[scale_x, 0, 0], [0, scale_y, 0]])
        
        # 计算新的图像尺寸
        new_w = int(w * scale_x)
        new_h = int(h * scale_y)
        
        # 应用缩放
        corrected = cv2.warpAffine(img, M, (new_w, new_h), flags=cv2.INTER_LINEAR)
        
        # 转换为3x3矩阵
        H = np.vstack([M, [0, 0, 1]]).astype(np.float32)
        
        return corrected, H

    def _draw_debug_info(self, img: np.ndarray, correction_info: Dict[str, Any]) -> np.ndarray:
        """绘制调试信息"""
        if not self.params["debug_draw"]:
            return img
            
        debug_img = img.copy()
        
        # 绘制网格
        if self.params["show_grid"]:
            self._draw_grid(debug_img)
            
        # 绘制修正信息
        y_offset = 30
        for key, value in correction_info.items():
            if isinstance(value, (int, float)):
                text = f"{key}: {value:.2f}"
            else:
                text = f"{key}: {value}"
            cv2.putText(debug_img, text, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1)
            y_offset += 25
            
        return debug_img

    def _draw_grid(self, img: np.ndarray):
        """绘制网格"""
        h, w = img.shape[:2]
        grid_size = self.params["grid_size"]
        
        # 绘制垂直线
        for x in range(0, w, grid_size):
            cv2.line(img, (x, 0), (x, h), (128, 128, 128), 1)
            
        # 绘制水平线
        for y in range(0, h, grid_size):
            cv2.line(img, (0, y), (w, y), (128, 128, 128), 1)

    def process(self, img, ctx):
        """主处理函数"""
        if img is None:
            return img, ctx
            
        corrected_img = img.copy()
        combined_matrix = np.eye(3)
        correction_info = {"mode": self.params["correction_mode"]}
        
        mode = self.params["correction_mode"]
        
        try:
            if mode == "perspective" and self.params["enable_perspective"]:
                corrected_img, H = self._apply_perspective_correction(corrected_img)
                combined_matrix = H @ combined_matrix
                correction_info["perspective"] = "applied"
                
            elif mode == "rotation" and self.params["enable_rotation"]:
                corrected_img, H = self._apply_rotation_correction(corrected_img)
                combined_matrix = H @ combined_matrix
                correction_info["rotation_angle"] = self._detect_main_angle(img) if self.params["auto_detect_angle"] else self.params["rotation_angle"]
                
            elif mode == "translation" and self.params["enable_translation"]:
                corrected_img, H = self._apply_translation_correction(corrected_img)
                combined_matrix = H @ combined_matrix
                correction_info["offset_x"] = self.params["offset_x"]
                correction_info["offset_y"] = self.params["offset_y"]
                
            elif mode == "scale" and self.params["enable_scale"]:
                corrected_img, H = self._apply_scale_correction(corrected_img)
                combined_matrix = H @ combined_matrix
                correction_info["scale_x"] = self.params["scale_x"]
                correction_info["scale_y"] = self.params["scale_y"]
                
            elif mode == "combined" or mode == "auto":
                # 组合修正：按顺序应用多种修正
                if self.params["enable_perspective"]:
                    corrected_img, H = self._apply_perspective_correction(corrected_img)
                    combined_matrix = H @ combined_matrix
                    
                if self.params["enable_rotation"]:
                    corrected_img, H = self._apply_rotation_correction(corrected_img)
                    combined_matrix = H @ combined_matrix
                    correction_info["rotation_angle"] = self._detect_main_angle(img) if self.params["auto_detect_angle"] else self.params["rotation_angle"]
                    
                if self.params["enable_translation"]:
                    corrected_img, H = self._apply_translation_correction(corrected_img)
                    combined_matrix = H @ combined_matrix
                    
                if self.params["enable_scale"]:
                    corrected_img, H = self._apply_scale_correction(corrected_img)
                    combined_matrix = H @ combined_matrix
                    
        except Exception as e:
            print(f"[PositionCorrection] 修正失败: {e}")
            corrected_img = img
            combined_matrix = np.eye(3)
            correction_info["error"] = str(e)
        
        # 保存修正信息到上下文
        ctx["position_correction"] = {
            "matrix": combined_matrix,
            "info": correction_info,
            "original_size": img.shape[:2],
            "corrected_size": corrected_img.shape[:2]
        }
        
        # 缓存修正矩阵供UI使用
        self._last_correction_matrix = combined_matrix
        self._last_correction_info = correction_info
        
        # 绘制调试信息
        if self.params["debug_draw"]:
            corrected_img = self._draw_debug_info(corrected_img, correction_info)
        
        return corrected_img, ctx

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        """打开专属UI"""
        try:
            mod = importlib.import_module('plugins.ui.geometry_tools.position_correction_ui')
            ui_cls = getattr(mod, 'PositionCorrectionUI', None)
            if ui_cls is None:
                from tkinter import messagebox
                messagebox.showerror('错误', '未找到 PositionCorrectionUI')
                return

            # 🔧 修复：正确传递预览图像和参数
            ui_instance = ui_cls(master, img=preview_img, params=params, on_change=on_change)

            # 如果有预览图像，立即更新显示
            if preview_img is not None:
                ui_instance.img_src = preview_img.copy()
                ui_instance._update_preview()

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror('错误', f'打开位置修正UI失败: {str(e)}')


# 注册插件实例
_ = PositionCorrectionPlugin()
