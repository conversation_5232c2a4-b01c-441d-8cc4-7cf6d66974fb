"""SerialCom Tkinter UI (简版)
通用串口调试工具，依赖 pyserial。
"""
from __future__ import annotations

import threading, time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

try:
    import serial, serial.tools.list_ports as list_ports
except ImportError:
    serial = None  # 运行时提示安装

class SerialComFrame(ttk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.ser: serial.Serial | None = None
        self._stop_evt = threading.Event()
        self._build_ui()

    # ---------------- UI -----------------
    def _build_ui(self):
        row = ttk.Frame(self)
        row.grid(row=0, column=0, sticky='we', pady=4)
        ttk.Label(row, text='端口:').pack(side=tk.LEFT)
        self.cb_port = ttk.Combobox(row, width=8, state='readonly')
        self.cb_port.pack(side=tk.LEFT, padx=2)
        ttk.Button(row, text='刷新', command=self._refresh_ports).pack(side=tk.LEFT)
        ttk.Label(row, text='波特率:').pack(side=tk.LEFT, padx=(10,0))
        self.cb_baud = ttk.Combobox(row, values=['9600','19200','38400','57600','115200'], width=8, state='readonly')
        self.cb_baud.set('9600'); self.cb_baud.pack(side=tk.LEFT)
        self.btn_open = ttk.Button(row, text='打开', command=self._toggle_port)
        self.btn_open.pack(side=tk.LEFT, padx=(10,0))

        # RX area
        self.txt_rx = scrolledtext.ScrolledText(self, width=80, height=20, state='disabled', font=('Consolas', 10))
        self.txt_rx.grid(row=1, column=0, padx=4, pady=4)

        # TX
        row2 = ttk.Frame(self); row2.grid(row=2, column=0, sticky='we', pady=4)
        self.ed_tx = ttk.Entry(row2, width=60)
        self.ed_tx.pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(row2, text='发送', command=self._on_send).pack(side=tk.LEFT, padx=4)

        self._refresh_ports()

    # ---------------- Serial helpers ----------------
    def _refresh_ports(self):
        if serial is None:
            messagebox.showerror('缺少依赖', '请先安装 pyserial\n pip install pyserial'); return
        ports = [p.device for p in list_ports.comports()]
        self.cb_port['values'] = ports
        if ports:
            self.cb_port.set(ports[0])

    def _toggle_port(self):
        if self.ser and self.ser.is_open:
            self._close_port(); return
        port = self.cb_port.get()
        if not port:
            messagebox.showwarning('提示', '请选择串口'); return
        try:
            self.ser = serial.Serial(port, int(self.cb_baud.get()), timeout=0.1)
        except Exception as e:
            messagebox.showerror('打开失败', str(e)); return
        self.btn_open.config(text='关闭'); self.cb_port.config(state='disabled'); self.cb_baud.config(state='disabled')
        self._stop_evt.clear(); threading.Thread(target=self._rx_loop, daemon=True).start()

    def _close_port(self):
        if self.ser:
            self._stop_evt.set(); time.sleep(0.2)
            try: self.ser.close()
            except Exception: pass
            self.ser = None
        self.btn_open.config(text='打开'); self.cb_port.config(state='readonly'); self.cb_baud.config(state='readonly')

    def _rx_loop(self):
        while not self._stop_evt.is_set():
            try:
                data = self.ser.read_all()
                if data:
                    self._append_rx(' '.join(f'{b:02X}' for b in data))
            except Exception as e:
                self._append_rx(f'[读取错误] {e}')
                break
            time.sleep(0.05)

    def _append_rx(self, text: str):
        self.txt_rx.configure(state='normal')
        self.txt_rx.insert(tk.END, text + '\n')
        self.txt_rx.configure(state='disabled')
        self.txt_rx.see(tk.END)

    def _on_send(self):
        if not (self.ser and self.ser.is_open):
            messagebox.showwarning('提示', '串口未打开'); return
        text = self.ed_tx.get().strip()
        if not text:
            return
        try:
            bts = bytes(int(b, 16) for b in text.split())
        except ValueError:
            messagebox.showwarning('格式错误', '请输入十六进制字符串，如 A0 01 01 A2'); return
        try:
            self.ser.write(bts)
            self._append_rx('[TX] ' + text.upper())
        except Exception as e:
            messagebox.showerror('发送失败', str(e))

    def destroy(self):
        try:
            self._close_port()
        finally:
            super().destroy()


if __name__ == '__main__':
    root = tk.Tk(); root.title('串口调试'); SerialComFrame(root).pack(); root.mainloop()
