def process(self, img, ctx):
    start_time = time.time()
    status = "success"
    
    try:
        # 获取参数
        roi_width = self.params["roi_width"]
        line_count = self.params["line_count"]
        threshold = self.params["threshold"]
        thickness = self.params["thickness"]
        polarity = self.params["polarity"]

        # 新增：灰度化和二值化
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)

        # 轮廓检测
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            contour = max(contours, key=cv2.contourArea)
            
            # 使用 minAreaRect 进行最小面积矩形拟合
            rect = cv2.minAreaRect(contour)
            box = cv2.boxPoints(rect)
            box = np.int0(box)

            # 绘制绿色矩形框
            cv2.drawContours(img, [box], 0, (0, 255, 0), thickness)  # 确保这里正确绘制了矩形框

            # 计算并绘制四个蓝色角点
            for point in box:
                cv2.circle(img, tuple(point), 5, (255, 0, 0), -1)  # 确保这里正确绘制了角点

            # 拟合红色椭圆（需轮廓点数≥5）
            if len(contour) >= 5:
                ellipse = cv2.fitEllipse(contour)
                cv2.ellipse(img, ellipse, (0, 0, 255), 2)  # 确保这里正确绘制了椭圆

            # 写入上下文信息
            ctx["rectangle_gauge"] = {
                "width": rect[1][0],
                "height": rect[1][1],
                "center": (int(rect[0][0]), int(rect[0][1])),
                "corners": box.tolist(),
                "ellipse": ellipse if len(contour) >= 5 else None
            }
        else:
            # 如果没有找到轮廓，设置状态为警告
            status = "warning: no contours found"
    except Exception as e:
        status = f"error: {str(e)}"
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    ctx[self.name] = {
        **ctx.get(self.name, {}),
        "status": status,
        "elapsed_time": elapsed_time,
    }
    
    return img, ctx