# -*- coding: utf-8 -*-
"""交互式圆测量 UI。

操作流程与 `arc_tool_ui.ArcToolUI` 类似：
1. 拖拽定义近似圆 ROI（中心和半径）
2. "拟合边缘" 调用 `geometry_tools.circle_tool.measure_circle` 完整拟合圆
3. "测量" 显示圆心、半径、直径

保存/加载 YAML 格式与圆弧工具保持一致，仅目录改为 `configs/circle_measure`。
"""
from __future__ import annotations

import pathlib, sys
from typing import Optional, Tuple, Any
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

import numpy as np
import yaml
from pathlib import Path

PREVIEW_SIZE = 550  # 固定显示尺寸
CFG_DIR = Path(__file__).resolve().parents[3] / 'configs' / 'circle_measure'
CFG_DIR.mkdir(parents=True, exist_ok=True)
import cv2
from PIL import Image, ImageTk

# --- 导入算法 ---
try:
    from plugins.geometry_tools.circle_tool import measure_circle
except ImportError:
    _proj_root = pathlib.Path(__file__).resolve().parents[3]
    if str(_proj_root) not in sys.path:
        sys.path.insert(0, str(_proj_root))
    from plugins.geometry_tools.circle_tool import measure_circle


try:
    from ._recipe_sync import update_mm_in_recipe
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from plugins.ui.geometry_tools._recipe_sync import update_mm_in_recipe
    except ImportError:
        # 如果都失败了，定义一个空函数
        def update_mm_in_recipe(*args, **kwargs):
            pass

class CircleToolUI(tk.Toplevel):
    _ST_IDLE = 'idle'
    _ST_ROI = 'roi_defined'
    _ST_FITTED = 'fitted'
    _ST_MEASURED = 'measured'

    def __init__(self, master: Optional[tk.Widget] = None, img: Optional[np.ndarray] = None, *, recipe_path: Optional[str] = None):
        super().__init__(master)
        self.title('圆测量工具')
        self.geometry('1000x750')
        self.protocol('WM_DELETE_WINDOW', self._on_close)

        # 图像数据
        self.img_orig: Optional[np.ndarray] = None
        self.img_display: Optional[np.ndarray] = None

        # 交互数据
        self.center_est: Optional[Tuple[float, float]] = None
        self.r_est: Optional[float] = None
        self._dragging = False

        # 多 ROI
        self.rois: list[dict[str, Any]] = []
        self.current_idx: Optional[int] = None
        self.next_id: int = 1
        self._recipe_path: Optional[Path] = Path(recipe_path) if recipe_path else None

        # 参数变量
        self.th1_var = tk.IntVar(value=50)
        self.th2_var = tk.IntVar(value=150)
        self.ransac_var = tk.DoubleVar(value=2.0)
        # 像素到毫米比例 (mm per pixel)，0 表示仅显示像素
        self.mm_var = tk.DoubleVar(value=0.0)
        # 亚像素拟合选项
        self.subpixel_var = tk.BooleanVar(value=True)

        # --- 布局 ---
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # 左侧控制面板
        # 左侧控制面板（固定宽度，风格与其它插件一致）
        p_frame = ttk.Frame(self, padding=6, width=260)
        p_frame.grid(row=0, column=0, sticky='ns')
        # 固定宽度，不随内部控件自动扩展
        p_frame.grid_propagate(False)

        ttk.Button(p_frame, text='打开图像', command=self._open_image).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='拟合边缘', command=self._fit_edges, state='disabled').pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='测量', command=self._measure_circle, state='disabled').pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='清除标记', command=self._clear_marks).pack(fill=tk.X, pady=2)
        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)

        # 参数设置
        param_fr = ttk.LabelFrame(p_frame, text='算法参数', padding=4)
        param_fr.pack(fill=tk.X, pady=4)
        row = 0
        ttk.Label(param_fr, text='Canny Th1').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.th1_var, width=6).grid(row=row, column=1)
        row += 1
        ttk.Label(param_fr, text='Canny Th2').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.th2_var, width=6).grid(row=row, column=1)
        row += 1
        ttk.Label(param_fr, text='RANSAC阈值').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.ransac_var, width=6).grid(row=row, column=1)
        # 亚像素拟合选项
        row += 1
        ttk.Checkbutton(param_fr, text='亚像素拟合', variable=self.subpixel_var).grid(row=row, column=0, columnspan=2, sticky='w')
        # mm/px 输入
        row += 1
        ttk.Label(param_fr, text='mm/px').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.mm_var, width=6).grid(row=row, column=1)

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        ttk.Label(p_frame, text='圆列表').pack(anchor='w')
        self.tree = ttk.Treeview(p_frame, columns=('state','dia','r'), show='headings', height=6)
        # 设置列宽使整体布局更紧凑
        self.tree.column('state', width=60, anchor='center')
        self.tree.column('dia', width=80, anchor='center')
        self.tree.column('r', width=80, anchor='center')
        self.tree.heading('state', text='阶段')
        self.tree.heading('dia', text='直径')
        self.tree.heading('r', text='半径')
        self.tree.pack(fill=tk.X)
        self.tree.bind('<<TreeviewSelect>>', self._on_tree_select)

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        self.result_var = tk.StringVar(value='直径: - px / - mm  半径: - px / - mm')
        ttk.Label(p_frame, textvariable=self.result_var, foreground='blue').pack(fill=tk.X)

        ttk.Button(p_frame, text='保存配置', command=self._save_yaml).pack(fill=tk.X, pady=(6,2))
        ttk.Button(p_frame, text='载入配置', command=self._load_yaml).pack(fill=tk.X, pady=2)

        # 右侧画布
        self.canvas = tk.Canvas(self, bg='#202020', width=PREVIEW_SIZE, height=PREVIEW_SIZE)
        self.canvas.grid(row=0, column=1, sticky='n')
        self.canvas.bind('<ButtonPress-1>', self._on_drag_start)
        self.canvas.bind('<B1-Motion>', self._on_drag_move)
        self.canvas.bind('<ButtonRelease-1>', self._on_drag_end)

        # 缩放/平移
        self._zoom = 1.0
        self._img_tk: Optional[ImageTk.PhotoImage] = None
        self.canvas.bind('<MouseWheel>', self._on_zoom)
        self.canvas.bind('<ButtonPress-2>', self._on_pan_start)
        self.canvas.bind('<B2-Motion>', self._on_pan_move)
        self._pan_start = (0, 0)
        self._offset = [0, 0]

        if img is not None:
            self.load_image(img)

    # ------------------- 基础工具 -------------------
    def _open_image(self):
        path = filedialog.askopenfilename(filetypes=[('Image','*.png;*.jpg;*.bmp')])
        if not path:
            return
        img = cv2.imread(path)
        if img is None:
            messagebox.showerror('错误','无法读取图像')
            return
        self.load_image(img)
        self._update_btn_state()

    def load_image(self, img: np.ndarray):
        self.img_orig = img.copy()
        self._reset_view()

    def _reset_view(self):
        self._zoom = 1.0
        self._offset = [0,0]
        if self.img_orig is None:
            return
        h, w = self.img_orig.shape[:2]
        disp = cv2.resize(self.img_orig, (int(w*self._zoom), int(h*self._zoom)))
        self.img_display = disp
        img_rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
        img_pil = Image.fromarray(img_rgb)
        self._img_tk = ImageTk.PhotoImage(img_pil)
        self.canvas.config(scrollregion=(0,0,disp.shape[1], disp.shape[0]))
        self.canvas.delete('img')
        self.canvas.create_image(self._offset[0], self._offset[1], anchor='nw', image=self._img_tk, tags='img')

    def _update_canvas_image(self):
        if self.img_orig is None:
            return
        h, w = self.img_orig.shape[:2]
        disp = cv2.resize(self.img_orig, (int(w*self._zoom), int(h*self._zoom)))
        self.img_display = disp
        img_rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
        img_pil = Image.fromarray(img_rgb)
        self._img_tk = ImageTk.PhotoImage(img_pil)
        self.canvas.delete('img')
        self.canvas.create_image(self._offset[0], self._offset[1], anchor='nw', image=self._img_tk, tags='img')

    def _canvas_to_img(self, x: float, y: float) -> Tuple[float,float]:
        return (x - self._offset[0]) / self._zoom, (y - self._offset[1]) / self._zoom

    # ------------------- 拖拽 ROI 定义 -------------------
    def _on_drag_start(self, e):
        if self.img_orig is None:
            return
        self._dragging = True
        self.center_est = self._canvas_to_img(e.x, e.y)
        self.r_est = 0.0
        self._draw_overlays()

    def _on_drag_move(self, e):
        if not self._dragging:
            return
        cx, cy = self.center_est
        px, py = self._canvas_to_img(e.x, e.y)
        self.r_est = float(np.hypot(px - cx, py - cy))
        self._draw_overlays()

    def _on_drag_end(self, e):
        if not self._dragging:
            return
        self._dragging = False
        if self.r_est < 5:
            self._draw_overlays()
            return
        roi = {
            'id': self.next_id,
            'cx': self.center_est[0],
            'cy': self.center_est[1],
            'r': self.r_est,
            'stage': self._ST_ROI,
            'result': None
        }
        self.rois.append(roi)
        self.next_id += 1
        self._refresh_tree()
        self.current_idx = len(self.rois)-1
        self._update_btn_state()
        self._draw_overlays()

    # ------------------- 按钮逻辑 -------------------
    def _update_btn_state(self):
        p_frame = self.children['!frame']
        widgets = p_frame.winfo_children()
        fit_btn = widgets[1]
        meas_btn = widgets[2]
        fit_btn['state'] = 'disabled'
        meas_btn['state'] = 'disabled'
        if self.current_idx is None:
            return
        stage = self.rois[self.current_idx]['stage']
        if stage == self._ST_ROI:
            fit_btn['state'] = 'normal'
        elif stage == self._ST_FITTED:
            meas_btn['state'] = 'normal'

    # ------------------- 计算与可视 -------------------
    def _fit_edges(self):
        if self.current_idx is None:
            return
        roi = self.rois[self.current_idx]
        res = measure_circle(
            self.img_orig,
            roi['cx'], roi['cy'], roi['r'],
            canny_th1=self.th1_var.get(),
            canny_th2=self.th2_var.get(),
            ransac_thresh=self.ransac_var.get(),
            subpixel=self.subpixel_var.get(),
            return_debug=True
        )
        roi['result'] = res
        if 'error' in res:
            messagebox.showerror('错误', res['error'])
            return
        roi['stage'] = self._ST_FITTED
        self._update_btn_state()
        self._draw_overlays()

    def _measure_circle(self):
        if self.current_idx is None:
            return
        roi = self.rois[self.current_idx]
        if roi['stage'] != self._ST_FITTED or not roi.get('result'):
            return
        res = roi['result']
        mm_per_px = self.mm_var.get()
        if mm_per_px > 0:
            dia_mm = res['diameter'] * mm_per_px
            rad_mm = res['radius'] * mm_per_px
            self.result_var.set(
                f"直径: {res['diameter']:.2f} px / {dia_mm:.2f} mm  半径:{res['radius']:.2f}px / {rad_mm:.2f} mm")
        else:
            self.result_var.set(f"直径: {res['diameter']:.2f} px  半径:{res['radius']:.2f}px")
        roi['stage'] = self._ST_MEASURED
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()

    # ------------------- 绘制 -------------------
    def _draw_point(self, x: float, y: float, color: str, radius: int = 2):
        xc = x * self._zoom + self._offset[0]
        yc = y * self._zoom + self._offset[1]
        self.canvas.create_oval(xc-radius, yc-radius, xc+radius, yc+radius, outline=color, fill=color, tags='ov')

    def _draw_circle(self, cx: float, cy: float, r: float, color: str):
        cx_c = cx * self._zoom + self._offset[0]
        cy_c = cy * self._zoom + self._offset[1]
        r_c = r * self._zoom
        self.canvas.create_oval(cx_c - r_c, cy_c - r_c, cx_c + r_c, cy_c + r_c, outline=color, width=1, tags='ov')

    def _draw_overlays(self):
        if self.img_orig is None:
            return
        self._update_canvas_image()
        self.canvas.delete('ov')
        # 所有 ROI
        for idx, roi in enumerate(self.rois):
            cx, cy, r = roi['cx'], roi['cy'], roi['r']
            cx_c = cx * self._zoom + self._offset[0]
            cy_c = cy * self._zoom + self._offset[1]
            r_c = r * self._zoom
            width = 2 if idx == self.current_idx else 1
            self.canvas.create_oval(cx_c - r_c, cy_c - r_c, cx_c + r_c, cy_c + r_c, outline='green', width=width, tags='ov')
            self.canvas.create_text(cx_c, cy_c, text=str(roi['id']), fill='green', tags='ov')

        # 临时拖拽圆
        if self._dragging and self.center_est and self.r_est > 0:
            cx, cy = self.center_est; r = self.r_est
            cx_c = cx * self._zoom + self._offset[0]
            cy_c = cy * self._zoom + self._offset[1]
            r_c = r * self._zoom
            self.canvas.create_oval(cx_c - r_c, cy_c - r_c, cx_c + r_c, cy_c + r_c, outline='cyan', dash=(4,2), width=1, tags='ov')

        # 调试 / 结果
        if self.current_idx is not None:
            roi = self.rois[self.current_idx]
            res = roi.get('result')
            if res and 'error' not in res and roi['stage'] in (self._ST_FITTED, self._ST_MEASURED):
                for pt in res.get('inlier_pts', [])[:800]:
                    self._draw_point(pt[0], pt[1], 'red')
                cx, cy = res['center']; r = res['radius']
                self._draw_circle(cx, cy, r, 'green')

    # ------------------- 树表 -------------------
    def _refresh_tree(self):
        self.tree.delete(*self.tree.get_children())
        for idx, roi in enumerate(self.rois):
            res = roi.get('result')
            dia = f"{res['diameter']:.1f}" if res and 'error' not in res else '-'
            radius = f"{res['radius']:.1f}" if res and 'error' not in res else '-'
            self.tree.insert('', 'end', iid=str(idx), values=(roi['stage'], dia, radius), text=f"#{roi['id']}")
        if self.current_idx is not None:
            self.tree.selection_set(str(self.current_idx))

    def _on_tree_select(self, evt):
        sel = self.tree.selection()
        if not sel:
            self.current_idx = None
            self.result_var.set('直径: - px / - mm  半径: - px / - mm')
            self._update_btn_state()
            self._draw_overlays()
            return
        self.current_idx = int(sel[0])
        roi = self.rois[self.current_idx]
        if roi.get('result') and 'error' not in roi['result'] and roi['stage'] != self._ST_ROI:
            res = roi['result']
            mm_per_px = self.mm_var.get()
            if mm_per_px > 0:
                dia_mm = res['diameter'] * mm_per_px
                rad_mm = res['radius'] * mm_per_px
                self.result_var.set(f"直径: {res['diameter']:.2f} px / {dia_mm:.2f} mm  半径:{res['radius']:.2f}px / {rad_mm:.2f} mm")
            else:
                self.result_var.set(f"直径: {res['diameter']:.2f} px  半径:{res['radius']:.2f}px")
        else:
            self.result_var.set('直径: - px / - mm  半径: - px / - mm')
        self._update_btn_state()
        self._draw_overlays()

    # ------------------- YAML -------------------
    def _save_yaml(self, ask: bool = True):
        if not self.rois:
            messagebox.showwarning('提示','没有 ROI 可以保存')
            return
        cfg = {
            'algo': {
                'canny_th1': self.th1_var.get(),
                'canny_th2': self.th2_var.get(),
                'ransac_thresh': self.ransac_var.get(),
                'subpixel': self.subpixel_var.get(),
            },
            'mm_per_px': self.mm_var.get(),
            'rois': [
                {'id': r['id'], 'cx': r['cx'], 'cy': r['cy'], 'r': r['r']} for r in self.rois
            ]
        }
        if ask:
            path = filedialog.asksaveasfilename(defaultextension='.yml', initialdir=str(CFG_DIR), initialfile='default.yml', filetypes=[('YAML','*.yml;*.yaml')])
            if not path:
                return
        else:
            path = CFG_DIR / 'default.yml'
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(cfg, f, allow_unicode=True)
        update_mm_in_recipe(self._recipe_path, 'circle_tool', self.mm_var.get())
        messagebox.showinfo('保存完成', f'配置已保存到 {path}')

    def _load_yaml(self):
        path = filedialog.askopenfilename(filetypes=[('YAML','*.yml;*.yaml')], initialdir=str(CFG_DIR))
        if not path:
            return
        data = yaml.safe_load(open(path, 'r', encoding='utf-8'))
        algo = data.get('algo', {})
        self.th1_var.set(algo.get('canny_th1',50))
        self.th2_var.set(algo.get('canny_th2',150))
        self.ransac_var.set(algo.get('ransac_thresh',2.0))
        self.subpixel_var.set(algo.get('subpixel', True))
        self.mm_var.set(data.get('mm_per_px', 0.0))
        self.rois.clear()
        for roi in data.get('rois', []):
            self.rois.append({
                'id': roi['id'],
                'cx': roi['cx'],
                'cy': roi['cy'],
                'r': roi['r'],
                'stage': self._ST_ROI,
                'result': None
            })
        self.next_id = max([r['id'] for r in self.rois], default=0)+1
        self.current_idx = 0 if self.rois else None
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()

    # ------------------- 平移/缩放 -------------------
    def _on_zoom(self, e):
        if self.img_orig is None:
            return
        factor = 1.1 if e.delta > 0 else 0.9
        self._zoom *= factor
        self._update_canvas_image()
        self._draw_overlays()

    def _on_pan_start(self, e):
        self._pan_start = (e.x, e.y)

    def _on_pan_move(self, e):
        dx, dy = e.x - self._pan_start[0], e.y - self._pan_start[1]
        self._pan_start = (e.x, e.y)
        self._offset[0] += dx
        self._offset[1] += dy
        self._update_canvas_image()
        self._draw_overlays()

    # ------------------- 其他 -------------------
    def _clear_marks(self):
        self.rois.clear()
        self.current_idx = None
        self.next_id = 1
        self.tree.delete(*self.tree.get_children())
        self.result_var.set('直径: - px / - mm  半径: - px / - mm')
        self._update_btn_state()
        self._draw_overlays()

    def _on_close(self):
        try:
            self.destroy()
        finally:
            if self.master and isinstance(self.master, tk.Tk):
                self.master.destroy()


if __name__ == '__main__':
    import cv2, sys
    img_path = sys.argv[1] if len(sys.argv) > 1 else None
    img = cv2.imread(img_path) if img_path else None
    root = tk.Tk()
    root.withdraw()
    CircleToolUI(root, img)
    root.mainloop()
