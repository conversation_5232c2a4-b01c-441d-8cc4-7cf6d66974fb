# -*- coding: utf-8 -*-
"""卡尺工具的用户界面。

提供一个Tkinter窗口，允许用户加载图像，交互式地定义测量线，
调整参数，并调用核心算法进行测量，最终将结果可视化。
"""
from __future__ import annotations
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import cv2
import numpy as np
from typing import Optional, Tuple, Any
from pathlib import Path
try:
    from ._recipe_sync import update_mm_in_recipe
except ImportError:
    try:
        from plugins.ui.geometry_tools._recipe_sync import update_mm_in_recipe
    except ImportError:
        def update_mm_in_recipe(*args, **kwargs):
            pass
from PIL import Image, ImageTk
import yaml

# --- 鲁棒地导入核心算法 ---
try:
    from plugins.geometry_tools.caliper_tool import measure_distance, measure_multi
except ImportError:
    # 如果直接运行此文件，需要将项目根目录添加到sys.path
    import sys, pathlib
    _proj_root = pathlib.Path(__file__).resolve().parents[3]
    if str(_proj_root) not in sys.path:
        sys.path.insert(0, str(_proj_root))
    from plugins.geometry_tools.caliper_tool import measure_distance, measure_multi


class CaliperToolUI(tk.Toplevel):
    _ST_IDLE = 'idle'
    _ST_LINE_DEFINED = 'line_defined'
    _ST_FITTED = 'fitted'
    _ST_MEASURED = 'measured'
    def __init__(self, master: Optional[tk.Widget] = None, img: Optional[np.ndarray] = None, *, recipe_path: Optional[str] = None):
        super().__init__(master)
        self.title('卡尺工具')
        self.geometry('1000x750')

        self.img_orig: Optional[np.ndarray] = None
        self.img_display: Optional[np.ndarray] = None # 用于显示和绘制的图像副本
        self.tk_img: Optional[tk.PhotoImage] = None
        self.scale = 1.0
        self.preview_max_w, self.preview_max_h = 800, 700
        self._recipe_path: Optional[Path] = Path(recipe_path) if recipe_path else None

        # --- 测量状态 ---
        self.stage = self._ST_IDLE
        self.p0: Optional[tuple[float, float]] = None
        self.p1: Optional[tuple[float, float]] = None
        self._last_result: Optional[dict] = None
        self._current_handler = None # 用于管理鼠标点击状态
        self._dragging = False

        # --- 主布局 ---
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # --- 左侧控制面板 ---
        ctrl_frame = ttk.Frame(self, padding=5)
        ctrl_frame.grid(row=0, column=0, sticky='ns')
        self._create_controls(ctrl_frame)

        # --- 右侧图像画布 ---
        self.canvas = tk.Canvas(self, bg='#202020')
        self.canvas.grid(row=0, column=1, sticky='nsew')
        # 初始绑定普通模式
        self._bind_canvas_events()
        self.canvas.bind('<Motion>', self._on_motion)
        self.canvas.bind('<MouseWheel>', self._on_zoom)

        # --- 底部信息栏 ---
        self.info_var = tk.StringVar(value='坐标:(-, -)  值:(-,-,-)')
        ttk.Label(self, textvariable=self.info_var, anchor='w').grid(row=1, column=0, columnspan=2, sticky='ew', padx=5)

        if img is not None:
            self.load_image(img)

    def _create_controls(self, parent: ttk.Frame):
        """创建所有控制组件。"""
        p_frame = ttk.LabelFrame(parent, text='控制', padding=5)
        p_frame.pack(fill=tk.X)

        ttk.Button(p_frame, text='打开图片', command=self._open_image).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='开始测量', command=self._start_measurement).pack(fill=tk.X, pady=2)
        self.btn_fit = ttk.Button(p_frame, text='拟合边缘', command=self._fit_edges, state='disabled')
        self.btn_fit.pack(fill=tk.X, pady=2)
        self.btn_measure = ttk.Button(p_frame, text='测量', command=self._measure_final, state='disabled')
        self.btn_measure.pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='清除标记', command=self._clear_markings).pack(fill=tk.X, pady=2)
        # 拖动绘线模式选择
        self.drag_mode_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(p_frame, text='拖拽绘线', variable=self.drag_mode_var, command=self._bind_canvas_events).pack(fill=tk.X, pady=(6,2))

        # --- 配置保存/载入 ---
        ttk.Button(p_frame, text='保存配置', command=self._save_cfg).pack(fill=tk.X, pady=(10, 2))
        ttk.Button(p_frame, text='加载配置', command=self._load_cfg).pack(fill=tk.X, pady=2)

        # --- 模式选择 ---
        mode_frame = ttk.LabelFrame(parent, text='测量模式', padding=5)
        mode_frame.pack(fill=tk.X, pady=5)
        self.mode_var = tk.StringVar(value='multi')
        self.mode_var.trace_add('write', lambda *a: self._on_mode_change())
        ttk.Radiobutton(mode_frame, text='单线', variable=self.mode_var, value='single').pack(anchor='w')
        ttk.Radiobutton(mode_frame, text='多线', variable=self.mode_var, value='multi').pack(anchor='w')

        # --- 参数设置 ---
        param_frame = ttk.LabelFrame(parent, text='参数', padding=5)
        param_frame.pack(fill=tk.X, pady=5)
        
        self.stride_var = tk.DoubleVar(value=0.5)
        self.sigma_var = tk.DoubleVar(value=1.0)
        self.width_var = tk.DoubleVar(value=30.0)
        self.n_lines_var = tk.IntVar(value=15)
        # 像素到毫米比例 (mm per pixel)；0 表示仅显示像素
        self.mm_var = tk.DoubleVar(value=0.0)
        # 亚像素拟合参数
        self.subpixel_var = tk.BooleanVar(value=True)
        self.roi_size_var = tk.DoubleVar(value=8.0)

        ttk.Label(param_frame, text='步距:').grid(row=0, column=0, sticky='w')
        ttk.Entry(param_frame, textvariable=self.stride_var, width=8).grid(row=0, column=1)
        ttk.Label(param_frame, text='高斯σ:').grid(row=1, column=0, sticky='w')
        ttk.Entry(param_frame, textvariable=self.sigma_var, width=8).grid(row=1, column=1)
        ttk.Label(param_frame, text='宽度:').grid(row=2, column=0, sticky='w')
        self.entry_width = ttk.Entry(param_frame, textvariable=self.width_var, width=8)
        self.entry_width.grid(row=2, column=1)
        ttk.Label(param_frame, text='线数:').grid(row=3, column=0, sticky='w')
        self.entry_n_lines = ttk.Entry(param_frame, textvariable=self.n_lines_var, width=8)
        self.entry_n_lines.grid(row=3, column=1)
        # 极性
        ttk.Label(param_frame, text='极性:').grid(row=4, column=0, sticky='w')
        self.polarity_var = tk.StringVar(value='auto')
        ttk.Combobox(param_frame, textvariable=self.polarity_var, state='readonly', width=10,
                     values=('auto', 'light_to_dark', 'dark_to_light')).grid(row=4, column=1)
        # mm/px 输入
        ttk.Label(param_frame, text='mm/px:').grid(row=5, column=0, sticky='w')
        ttk.Entry(param_frame, textvariable=self.mm_var, width=8).grid(row=5, column=1)
        # 亚像素拟合
        ttk.Checkbutton(param_frame, text='亚像素拟合', variable=self.subpixel_var).grid(row=6, column=0, columnspan=2, sticky='w')
        # ROI尺寸
        ttk.Label(param_frame, text='ROI尺寸:').grid(row=7, column=0, sticky='w')
        ttk.Entry(param_frame, textvariable=self.roi_size_var, width=8).grid(row=7, column=1)

        # --- 结果显示 ---
        res_frame = ttk.LabelFrame(parent, text='结果', padding=5)
        res_frame.pack(fill=tk.X, pady=5)
        self.result_var = tk.StringVar(value='距离: - px / - mm')
        ttk.Label(res_frame, textvariable=self.result_var).pack()

    def _bind_canvas_events(self):
        """根据 drag_mode 重新绑定画布事件"""
        # 先解绑以前的
        self.canvas.unbind('<Button-1>')
        self.canvas.unbind('<ButtonPress-1>')
        self.canvas.unbind('<B1-Motion>')
        self.canvas.unbind('<ButtonRelease-1>')
        if self.drag_mode_var.get():
            self.canvas.bind('<ButtonPress-1>', self._on_drag_start)
            self.canvas.bind('<B1-Motion>', self._on_drag_move)
            self.canvas.bind('<ButtonRelease-1>', self._on_drag_end)
        else:
            self.canvas.bind('<Button-1>', self._on_canvas_click)

    # ----- 拖线模式回调 -----
    def _on_drag_start(self, event):
        if self.img_orig is None: return
        self._dragging = True
        self.p0 = self._canvas_to_img_coords(event.x, event.y)
        self.p1 = self.p0
        self.stage = self._ST_IDLE
        self._draw_overlays()

    def _on_drag_move(self, event):
        if not self._dragging: return
        self.p1 = self._canvas_to_img_coords(event.x, event.y)
        self.stage = self._ST_LINE_DEFINED
        self._draw_overlays()

    def _on_drag_end(self, event):
        if not self._dragging: return
        self._dragging = False
        self.p1 = self._canvas_to_img_coords(event.x, event.y)
        self.stage = self._ST_LINE_DEFINED
        self._update_buttons()
        self._draw_overlays()
        self.canvas.bind('<Motion>', self._on_motion)
        self.canvas.bind('<MouseWheel>', self._on_zoom)

    def _open_image(self):
        """通过文件对话框打开图片，并交由load_image处理。"""
        fpath = filedialog.askopenfilename(
            title='选择图片文件',
            filetypes=[('Image files', '*.png *.jpg *.jpeg *.bmp *.tif *.tiff'), ('All files', '*.*')]
        )
        if not fpath:
            return

        try:
            img = cv2.imdecode(np.fromfile(fpath, dtype=np.uint8), cv2.IMREAD_COLOR)
            if img is None:
                raise ValueError("无法加载图片")
            # 调用统一的加载逻辑
            self.load_image(img)
        except Exception as e:
            messagebox.showerror('错误', f'打开图片失败：\n{e}')

    def load_image(self, img: np.ndarray):
        """加载并显示新图像。如果已有配置，则自动运行测量。"""
        self.img_orig = img
        self.img_display = self.img_orig.copy()

        # 计算初始缩放比例以适应窗口
        h, w = self.img_orig.shape[:2]
        self.scale = min(self.preview_max_w / w, self.preview_max_h / h, 1.0)

        # 检查是否存在预加载的测量点
        if self.p0 is not None and self.p1 is not None:
            # 如果点存在，直接运行测量（测量函数会负责绘制和显示）
            self._run_measurement()
            self.info_var.set('图片已加载，并根据配置自动测量。')
        else:
            # 如果没有预设点，则清除旧点并仅显示原始图像
            self.p0, self.p1 = None, None
            self._display_image()
            self.info_var.set('图片已加载，请开始测量。')

    def _display_image(self):
        """根据当前的 self.img_display 和缩放比例更新画布。"""
        if self.img_display is None:
            self.canvas.delete('all')
            return

        h, w = self.img_display.shape[:2]
        disp_w, disp_h = int(w * self.scale), int(h * self.scale)

        if disp_w <= 0 or disp_h <= 0:
            self.canvas.delete('all')
            return

        img_resized = cv2.resize(self.img_display, (disp_w, disp_h), interpolation=cv2.INTER_AREA)
        img_rgb = cv2.cvtColor(img_resized, cv2.COLOR_BGR2RGB)

        pil_img = Image.fromarray(img_rgb)
        self.tk_img = ImageTk.PhotoImage(image=pil_img)

        self.canvas.delete('all')
        self.canvas.config(scrollregion=(0, 0, disp_w, disp_h))
        self.canvas.create_image(0, 0, anchor='nw', image=self.tk_img)

    def _canvas_to_img_coords(self, cx: int, cy: int) -> tuple[float, float]:
        """将画布坐标转换为原始图像坐标。"""
        return (cx / self.scale, cy / self.scale)

    def _on_canvas_click(self, event):
        """处理画布点击事件，用于定义测量线。"""
        if self._current_handler:
            self._current_handler(event)

    def _define_p0(self, event):
        self.p0 = self._canvas_to_img_coords(event.x, event.y)
        self.result_var.set('请在图上点击终点...')
        self._current_handler = self._define_p1
        self._draw_overlays()

    def _define_p1(self, event):
        self.p1 = self._canvas_to_img_coords(event.x, event.y)
        self._current_handler = None  # 完成定义
        self.stage = self._ST_LINE_DEFINED
        self._update_buttons()
        # 仅绘制扫描 ROI，无边缘点
        self._draw_overlays()

    def _on_motion(self, event):
        """处理鼠标移动，更新信息栏。"""
        if self.img_orig is None: return
        ix, iy = self._canvas_to_img_coords(event.x, event.y)
        ix, iy = int(ix), int(iy)
        h, w = self.img_orig.shape[:2]
        if 0 <= ix < w and 0 <= iy < h:
            val = self.img_orig[iy, ix]
            try:
                b, g, r = (val[0], val[1], val[2]) if len(val) >= 3 else (val, val, val)
            except TypeError:
                # 标量
                b = g = r = val
            self.info_var.set(f'坐标:({ix}, {iy})  值:({r},{g},{b})')
        else:
            self.info_var.set('坐标:(-, -)  值:(-,-,-)')

    def _on_zoom(self, event):
        """处理鼠标滚轮缩放。"""
        if self.img_orig is None: return
        # 缩放因子
        factor = 1.1 if event.delta > 0 else 0.9
        self.scale *= factor
        # 限制缩放范围
        self.scale = max(0.1, min(self.scale, 5.0))
        self._display_image()

    # --- 状态按钮更新 ---
    def _update_buttons(self):
        """根据当前 stage 启用/禁用按钮"""
        if self.stage == self._ST_IDLE:
            self.btn_fit.config(state='disabled')
            self.btn_measure.config(state='disabled')
        elif self.stage == self._ST_LINE_DEFINED:
            self.btn_fit.config(state='normal')
            self.btn_measure.config(state='disabled')
        elif self.stage == self._ST_FITTED:
            self.btn_fit.config(state='normal')
            self.btn_measure.config(state='normal')
        elif self.stage == self._ST_MEASURED:
            self.btn_fit.config(state='normal')
            self.btn_measure.config(state='normal')

    def _start_measurement(self):
        """开始定义测量线。"""
        if self.img_orig is None:
            messagebox.showwarning('提示', '请先打开一张图片。')
            return
        self._clear_markings()
        self.result_var.set('请在图上点击起点...')
        self._current_handler = self._define_p0
        self.stage = self._ST_IDLE
        self._update_buttons()

    def _run_measurement(self):
        """根据当前参数执行测量并绘制结果。"""
        if not (self.p0 and self.p1 and self.img_orig is not None):
            return

        params = {
            'stride': self.stride_var.get(),
            'sigma': self.sigma_var.get(),
            'return_debug': True,
            'polarity': self.polarity_var.get(),
            'subpixel': self.subpixel_var.get(),
            'roi_size': self.roi_size_var.get()
        }
        mode = self.mode_var.get()

        try:
            if mode == 'single':
                result = measure_distance(self.img_orig, self.p0, self.p1, **params)
            else:  # 'multi'
                params.update({
                    'width': self.width_var.get(),
                    'n_lines': self.n_lines_var.get()
                })
                result = measure_multi(self.img_orig, self.p0, self.p1, **params)
        except Exception as e:
            messagebox.showerror('测量失败', f'执行测量时发生错误:\n{e}')
            self.result_var.set('距离: 测量失败')
            return

        if result.get('distance_px') is None:
            messagebox.showinfo('结果', '在指定区域未找到清晰的边缘。')
            self.result_var.set('距离: 未找到边缘')
            self._draw_overlays()
            return

        mm_per_px = self.mm_var.get()
        if mm_per_px > 0:
            distance_mm = result['distance_px'] * mm_per_px
            self.result_var.set(f"距离: {result['distance_px']:.3f} px / {distance_mm:.3f} mm")
        else:
            self.result_var.set(f"距离: {result['distance_px']:.3f} px")
        self._draw_overlays(result)

    # ----------------- 新增辅助/动作方法 -----------------
    def _get_scan_lines_preview(self):
        """根据当前中心线和参数生成扫描线列表，仅用于预览"""
        if not (self.p0 and self.p1):
            return []
        (x0, y0), (x1, y1) = self.p0, self.p1
        dx, dy = x1 - x0, y1 - y0
        length = np.hypot(dx, dy)
        if length < 1e-6:
            return []
        ux, uy = dx / length, dy / length  # 归一化中心线方向
        # 垂直向量
        vx, vy = -uy, ux
        half_width = self.width_var.get() / 2.0
        n_lines = max(1, int(self.n_lines_var.get()))
        positions = np.linspace(-half_width, half_width, n_lines) if n_lines > 1 else [0.0]
        lines = []
        for pos in positions:
            line_p0 = (x0 + pos * vx, y0 + pos * vy)
            line_p1 = (x1 + pos * vx, y1 + pos * vy)
            lines.append((line_p0, line_p1))
        return lines

    def _fit_edges(self):
        """执行边缘搜索与拟合，不显示距离值，只更新调试可视化"""
        if not (self.p0 and self.p1 and self.img_orig is not None):
            messagebox.showwarning('提示', '请先定义测量线并加载图片。')
            return
        params = {
            'stride': self.stride_var.get(),
            'sigma': self.sigma_var.get(),
            'polarity': self.polarity_var.get(),
            'return_debug': True,
            'subpixel': self.subpixel_var.get(),
            'roi_size': self.roi_size_var.get()
        }
        mode = self.mode_var.get()
        try:
            if mode == 'single':
                res = measure_distance(self.img_orig, self.p0, self.p1, **params)
            else:
                params.update({
                    'width': self.width_var.get(),
                    'n_lines': self.n_lines_var.get()
                })
                res = measure_multi(self.img_orig, self.p0, self.p1, **params)
        except Exception as e:
            messagebox.showerror('拟合失败', f'执行拟合时发生错误:\n{e}')
            return
        self._last_result = res
        self.stage = self._ST_FITTED
        self._update_buttons()
        self.result_var.set('距离: - (等待测量)')
        self._draw_overlays(res)

    def _measure_final(self):
        """在已拟合基础上显示距离结果"""
        if self.stage not in (self._ST_FITTED, self._ST_MEASURED):
            messagebox.showinfo('提示', '请先点击"拟合边缘"按钮进行拟合。')
            return
        if not self._last_result or self._last_result.get('distance_px') is None:
            messagebox.showinfo('结果', '无有效测量结果，请先拟合边缘。')
            return
        mm_per_px = self.mm_var.get()
        if mm_per_px > 0:
            distance_mm = self._last_result['distance_px'] * mm_per_px
            self.result_var.set(f"距离: {self._last_result['distance_px']:.3f} px / {distance_mm:.3f} mm")
        else:
            self.result_var.set(f"距离: {self._last_result['distance_px']:.3f} px")
        self.stage = self._ST_MEASURED
        self._update_buttons()
        self._draw_overlays(self._last_result)

    def _on_mode_change(self):
        """根据模式启用/禁用多线参数"""
        m = self.mode_var.get()
        state = 'normal' if m == 'multi' else 'disabled'
        self.entry_width.config(state=state)
        self.entry_n_lines.config(state=state)

    def _draw_overlays(self, result_data: Optional[dict] = None):
        """在图像上绘制所有标记（点、线、结果）。"""
        if self.img_orig is None: return

        self.img_display = self.img_orig.copy()

        if result_data and self.stage in (self._ST_FITTED, self._ST_MEASURED):
            dbg = result_data
            # 绘制多线扫描线
            if 'scan_lines' in dbg:
                for p_start, p_end in dbg['scan_lines']:
                    cv2.line(self.img_display, tuple(np.int32(p_start)), tuple(np.int32(p_end)), (0, 255, 255), 2)
            # 拟合直线
            if 'fitted_line1' in dbg:
                _draw_fitted_line(self.img_display, dbg['fitted_line1'].flatten(), (0,255,255), 1)
            if 'fitted_line2' in dbg:
                _draw_fitted_line(self.img_display, dbg['fitted_line2'].flatten(), (0,255,255), 1)
            # 边缘点
            if 'edge_pts1' in dbg:
                for pt in dbg['edge_pts1']:
                    cv2.circle(self.img_display, tuple(np.int32(pt)), 3, (0,0,255), -1)
            if 'edge_pts2' in dbg:
                for pt in dbg['edge_pts2']:
                    cv2.circle(self.img_display, tuple(np.int32(pt)), 3, (255,0,0), -1)
            # ROI框
            if 'roi_boxes' in dbg:
                for roi_box in dbg['roi_boxes']:
                    if 'corners' in roi_box:
                        corners = roi_box['corners']
                        pts = np.array(corners, dtype=np.int32).reshape((-1, 1, 2))
                        cv2.polylines(self.img_display, [pts], True, (255, 255, 0), 1)  # 青色ROI框
            # 单线模式点
            if 'p_rising' in dbg:
                cv2.circle(self.img_display, tuple(np.int32(dbg['p_rising'])), 5, (0,255,0), 2)
            if 'p_falling' in dbg:
                cv2.circle(self.img_display, tuple(np.int32(dbg['p_falling'])), 5, (0,255,0), 2)

        # 绘制中心线
        if self.p0 and self.p1:
            cv2.line(self.img_display, tuple(np.int32(self.p0)), tuple(np.int32(self.p1)), (0, 255, 0), 1)
            # 如已定义线，同步绘制 ROI 扫描线（仅 line_defined 及后续阶段）
            if self.stage in (self._ST_LINE_DEFINED, self._ST_FITTED, self._ST_MEASURED):
                scan_lines = self._get_scan_lines_preview()
                for ps, pe in scan_lines:
                    cv2.line(self.img_display, tuple(np.int32(ps)), tuple(np.int32(pe)), (0, 255, 255), 1)
        if self.p0:
            cv2.circle(self.img_display, tuple(np.int32(self.p0)), 5, (0, 255, 0), -1)
        if self.p1:
            cv2.circle(self.img_display, tuple(np.int32(self.p1)), 5, (0, 255, 0), -1)

        self._display_image()

    def _clear_markings(self):
        """清除所有测量标记和结果，并恢复到原始图像显示。"""
        self.p0, self.p1 = None, None
        self._current_handler = None
        self.result_var.set('距离: - px / - mm')
        if self.img_orig is not None:
            self.img_display = self.img_orig.copy()
            self._display_image()
        else:
            self.canvas.delete('all')

    def _save_cfg(self):
        """保存当前参数到YAML文件。"""
        fpath = filedialog.asksaveasfilename(
            defaultextension=".yaml",
            filetypes=[("YAML files", "*.yaml *.yml"), ("All files", "*.*")],
            title="保存配置"
        )
        if not fpath: return

        config = {
            'mode': self.mode_var.get(),
            'stride': self.stride_var.get(),
            'sigma': self.sigma_var.get(),
            'width': self.width_var.get(),
            'n_lines': self.n_lines_var.get(),
            'polarity': self.polarity_var.get(),
            'p0': self.p0,
            'p1': self.p1,
            'mm_per_px': self.mm_var.get()
        }

        try:
            with open(fpath, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, allow_unicode=True, default_flow_style=False)
            update_mm_in_recipe(self._recipe_path, 'caliper_tool', self.mm_var.get())
            messagebox.showinfo('成功', '配置已保存。')
        except Exception as e:
            messagebox.showerror('错误', f'保存配置失败:\n{e}')

    def _load_cfg(self):
        """从YAML文件加载参数。"""
        fpath = filedialog.askopenfilename(
            filetypes=[("YAML files", "*.yaml *.yml"), ("All files", "*.*")],
            title="加载配置"
        )
        if not fpath: return

        try:
            with open(fpath, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
        except Exception as e:
            messagebox.showerror('错误', f'加载配置失败:\n{e}')
            return

        self.mode_var.set(config.get('mode', 'multi'))
        self.polarity_var.set(config.get('polarity', 'auto'))
        self.stride_var.set(config.get('stride', 0.5))
        self.mm_var.set(config.get('mm_per_px', 0.0))
        self.sigma_var.set(config.get('sigma', 1.0))
        self.width_var.set(config.get('width', 30.0))
        self.n_lines_var.set(config.get('n_lines', 15))
        
        p0 = config.get('p0')
        p1 = config.get('p1')

        self._clear_markings()

        if p0 and p1:
            self.p0 = tuple(p0)
            self.p1 = tuple(p1)
            if self.img_orig is not None:
                self._run_measurement()
            else:
                self.info_var.set('配置已加载，请打开图片以自动测量。')
        else:
            messagebox.showwarning(
                '配置不完整',
                '配置文件中加载了参数，但缺少测量点(p0, p1)信息。\n\n'
                '请手动点击“开始测量”并定义测量线，或检查您的配置文件。'
            )
            self.info_var.set('参数已加载，但缺少测量点。')

def _draw_fitted_line(img, line, color, thickness=1):
    """在图像上绘制cv2.fitLine返回的直线。"""
    rows, cols = img.shape[:2]
    vx, vy, x, y = line
    x1 = 0
    y1 = int((-x * vy / vx) + y)
    x2 = cols
    y2 = int(((cols - x) * vy / vx) + y)
    cv2.line(img, (x1, y1), (x2, y2), color, thickness)

if __name__ == '__main__':
    root = tk.Tk()
    root.withdraw()
    app = CaliperToolUI(root)
    
    def on_closing():
        app.destroy()
        root.destroy()

    app.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()
