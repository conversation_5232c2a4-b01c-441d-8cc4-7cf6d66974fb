"""Pipeline Editor UI

允许用户通过简单界面组合一系列图像处理插件，编辑参数，
用测试图快速执行并保存运行结果，同时支持 YAML 导入/导出。

插件通过 plugins.plugin_base.get_plugin_registry() 动态注册，
返回 {"plugin_name": PluginClass, ...}。
"""
from __future__ import annotations

import sys
import json
import yaml
import logging
from pathlib import Path as _Path2
# --- logging ---
_log_dir = _Path2(__file__).resolve().parents[1]/'logs'
_log_dir.mkdir(exist_ok=True)
_logger = logging.getLogger('pipeline_editor')
if not any(isinstance(h, logging.FileHandler) for h in _logger.handlers):
    fh = logging.FileHandler(_log_dir/'pipeline_editor.log', encoding='utf-8')
    fh.setFormatter(logging.Formatter('%(asctime)s %(levelname)s %(message)s'))
    fh.setLevel(logging.INFO)
    _logger.addHandler(fh)
_logger.info('PipelineEditor module loaded')
import time
import datetime
import importlib
import pkgutil
import pathlib
from pathlib import Path
from typing import Dict, Any, Type, Optional

import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

# ---------------- YAML 序列化辅助 -----------------
def _yaml_sanitize(o):
    """递归将 tk.*Var / numpy / Path 等转换为基础类型，保证可 YAML 序列化"""
    import numpy as _np
    if isinstance(o, (tk.BooleanVar, tk.DoubleVar, tk.IntVar, tk.StringVar)):
        return o.get()
    if isinstance(o, (Path, )):
        return str(o)
    if isinstance(o, (_np.generic,)):
        return o.item()
    if isinstance(o, _np.ndarray):
        return o.tolist()
    if isinstance(o, (tuple, set)):
        return [_yaml_sanitize(v) for v in o]
    if isinstance(o, dict):
        return {k: _yaml_sanitize(v) for k, v in o.items()}
    if isinstance(o, list):
        return [_yaml_sanitize(v) for v in o]
    return o

# -------- 确保项目根目录在 sys.path --------
ROOT_DIR = pathlib.Path(__file__).resolve().parents[1]
if str(ROOT_DIR) not in sys.path:
    sys.path.insert(0, str(ROOT_DIR))

# ---------------- 尝试导入插件基类及注册表 -----------------
try:
    from plugins.plugin_base import get_plugin_registry, PluginBase
except Exception:
    # fallback stub，方便 UI 独立调试
    class PluginBase:  # type: ignore
        name = "base"
        label = "Base"
        params: Dict[str, Any] = {}

        def __init__(self, **kwargs):
            pass

    def get_plugin_registry():  # type: ignore
        return {}

# ---------------- 动态发现 core.image_process -----------------
def _import_core_image_plugins():
    base_dir = ROOT_DIR / "plugins" / "core" / "image_process"
    if not base_dir.is_dir():
        return
    for m in pkgutil.iter_modules([str(base_dir)]):
        if m.name.startswith("_") or m.name.endswith("_ui"):
            continue
        mod_name = f"plugins.core.image_process.{m.name}"
        if mod_name in sys.modules:
            continue
        try:
            importlib.import_module(mod_name)
        except Exception:
            pass


_import_core_image_plugins()

# ---------------- 动态发现 DetectIdentify -----------------

def _import_detectidentify_plugins():
    base_dir = ROOT_DIR / "plugins" / "DetectIdentify"
    if not base_dir.is_dir():
        return
    for m in pkgutil.iter_modules([str(base_dir)]):
        if m.name.startswith("_") or m.name.endswith("_ui"):
            continue
        mod_name = f"plugins.DetectIdentify.{m.name}"
        if mod_name in sys.modules:
            continue
        try:
            importlib.import_module(mod_name)
        except Exception:
            pass

_import_detectidentify_plugins()
# geometry_measure_plugin 已移除，不再导入


class PipelineEditor(tk.Toplevel):
    def __init__(self, master: tk.Misc | None = None, *, pipeline_path: str | None = None, read_only: bool = False, test_image_path = None):
        super().__init__(master)
        self.title("流程编辑器")
        self.geometry("1000x600")
        self.minsize(800, 480)

        # -------- 状态 --------
        self.available_plugins: Dict[str, Type[PluginBase]] = get_plugin_registry()
        self.pipeline_steps: list[dict[str, Any]] = []          # [{name: params}, ...]
        self._cached_test_img: Optional[np.ndarray] = None      # 首次选择后缓存
        self.test_image_path = test_image_path                  # 配方编辑器传入的测试图像
        self.read_only: bool = read_only
        self.pipeline_path: str | None = pipeline_path

        # -------- UI --------
        self._build_layout()

        # 若指定路径则加载
        if self.pipeline_path:
            try:
                self._load_from_path(self.pipeline_path)
                self.title(f"流程查看 - {Path(self.pipeline_path).name}")
            except Exception as exc:
                messagebox.showerror("加载失败", str(exc))

        # 只读模式 UI 调整
        if self.read_only:
            self._set_read_only()

    # ----------------- 工具方法 -----------------
    def _load_from_path(self, path: str):
        data = yaml.safe_load(Path(path).read_text(encoding="utf-8")) or []
        if not isinstance(data, list):
            raise ValueError("YAML 顶层应为列表")
        self.pipeline_steps = data
        self._refresh_step_list()

    def _set_read_only(self):
        """禁用所有编辑控件，显示克隆按钮。"""
        # 禁用树和列表
        self.plugin_tree.configure(state="disabled")
        self.step_list.configure(state="disabled")
        # 隐藏/禁用按钮
        self.add_btn.configure(state="disabled") if hasattr(self, "add_btn") else None
        self.save_btn.configure(state="disabled")
        self.open_btn.configure(state="disabled")
        self.test_btn.configure(state="disabled")
        # 克隆按钮显示
        self.clone_btn.pack(side=tk.LEFT, padx=4)

    def _clone_to_debug(self):
        if not self.pipeline_steps:
            messagebox.showwarning("空流程", "无内容可克隆")
            return
        ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        debug_dir = ROOT_DIR / "configs" / "pipelines" / "debug"
        debug_dir.mkdir(parents=True, exist_ok=True)
        target_path = debug_dir / f"debug_{ts}.yaml"
        clean_steps = _yaml_sanitize(self.pipeline_steps)
        yaml.safe_dump(clean_steps, target_path.open("w", encoding="utf-8"), allow_unicode=True)
        messagebox.showinfo("已克隆", f"已保存到 {target_path}\n将以可编辑模式打开")
        # 打开新窗口可编辑
        PipelineEditor(self.master or self, pipeline_path=str(target_path), read_only=False)
    def _get_test_img(self) -> Optional[np.ndarray]:
        """首次提示用户选一张测试图并缓存；取消返回 None。"""
        if self._cached_test_img is not None:
            return self._cached_test_img
        if self.test_image_path is not None:
            self._cached_test_img = cv2.imdecode(
                np.fromfile(self.test_image_path, dtype=np.uint8), cv2.IMREAD_COLOR
            )
            return self._cached_test_img
        path = filedialog.askopenfilename(
            title="选择测试图",
            filetypes=[("Image", "*.png;*.jpg;*.bmp")]
        )
        if not path:
            return None
        self._cached_test_img = cv2.imdecode(
            np.fromfile(path, dtype=np.uint8), cv2.IMREAD_COLOR
        )
        return self._cached_test_img

    # ----------------- 搭建界面 -----------------
    def _build_layout(self):
        paned = ttk.Panedwindow(self, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)

        # 1. 左侧插件树
        left = ttk.Frame(paned, padding=5)
        paned.add(left, weight=1)

        ttk.Label(left, text="可用插件").pack(anchor="w")
        self.plugin_tree = ttk.Treeview(left, show="tree")
        self.plugin_tree.pack(fill=tk.BOTH, expand=True)
        # 双击插件自动添加
        self.plugin_tree.bind("<Double-1>", lambda _e: self._add_plugin())

        # 按类别分组
        cats: dict[str, str] = {}
        cats_order = ["图像采集"]                # 先显示采集类
        for key, cls in sorted(self.available_plugins.items()):
            cat = getattr(cls, "category", "其他")
            if cat not in cats:
                cats[cats_order[0] if cat == "图像采集" else cat] = self.plugin_tree.insert(
                    "", "end", text=cat, open=True
                )
            self.plugin_tree.insert(
                cats[cat], "end",
                text=f"{key} ({getattr(cls, 'label', key)})",
                values=(key,)
            )

        self.add_btn = ttk.Button(left, text="添加 →", command=self._add_plugin)
        self.add_btn.pack(pady=5)

        # 2. 中间步骤列表
        mid = ttk.Frame(paned, padding=5)
        paned.add(mid, weight=0)
        ttk.Label(mid, text="流程步骤").pack(anchor="w")

        self.step_list = tk.Listbox(mid, height=15)
        self.step_list.pack(fill=tk.BOTH, expand=True)

        btns = ttk.Frame(mid)
        btns.pack(pady=4, fill=tk.X)
        ttk.Button(btns, text="↑", width=3, command=lambda: self._move_step(-1)).pack(side=tk.LEFT)
        ttk.Button(btns, text="↓", width=3, command=lambda: self._move_step(1)).pack(side=tk.LEFT)
        ttk.Button(btns, text="删除", command=self._remove_step).pack(side=tk.LEFT, padx=4)

        # 3. 右侧参数区
        right = ttk.Frame(paned, padding=5)
        paned.add(right, weight=2)
        ttk.Label(right, text="参数").pack(anchor="w")
        self.param_container = ttk.Frame(right)
        self.param_container.pack(fill=tk.BOTH, expand=True)

        # 列表/双击事件
        self.step_list.bind("<<ListboxSelect>>", lambda _e: self._show_params())
        self.step_list.bind("<Double-1>", lambda _e: self._open_plugin_dialog())

        # 底部按钮栏
        bar = ttk.Frame(self, padding=4)
        bar.pack(fill=tk.X)
        self.open_btn = ttk.Button(bar, text="打开", command=self._load_yaml)
        self.open_btn.pack(side=tk.LEFT, padx=4)
        self.save_btn = ttk.Button(bar, text="保存", command=self._save_yaml)
        self.save_btn.pack(side=tk.LEFT, padx=4)
        self.test_btn = ttk.Button(bar, text="测试运行", command=self._test_run)
        self.test_btn.pack(side=tk.LEFT, padx=4)
        # 只读时隐藏，稍后 _set_read_only 再显示 clone_btn
        self.clone_btn = ttk.Button(bar, text="克隆到调试", command=self._clone_to_debug)
        self.close_btn = ttk.Button(bar, text="关闭", command=self.destroy)
        self.close_btn.pack(side=tk.RIGHT, padx=4)

    # ----------------- 左侧插件树相关 -----------------
    def _add_plugin(self):
        if getattr(self, "read_only", False):
            return
        item = self.plugin_tree.focus()
        if not item or not self.plugin_tree.parent(item):  # 排除分类节点
            return
        key = self.plugin_tree.item(item, "values")[0]
        cls = self.available_plugins[key]

        params = json.loads(json.dumps(getattr(cls, "params", {})))  # 深拷贝默认参数
        self.pipeline_steps.append({key: params})
        self._refresh_step_list(select=len(self.pipeline_steps) - 1)

    # ----------------- 中间列表相关 -----------------
    def _move_step(self, offset: int):
        if getattr(self, "read_only", False):
            return
        idxs = self.step_list.curselection()
        if not idxs:
            return
        idx = idxs[0]
        new_idx = idx + offset
        if not (0 <= new_idx < len(self.pipeline_steps)):
            return
        self.pipeline_steps[idx], self.pipeline_steps[new_idx] = (
            self.pipeline_steps[new_idx],
            self.pipeline_steps[idx],
        )
        self._refresh_step_list(select=new_idx)

    def _remove_step(self):
        if getattr(self, "read_only", False):
            return
        idxs = self.step_list.curselection()
        if not idxs:
            return
        idx = idxs[0]
        self.pipeline_steps.pop(idx)
        self._refresh_step_list()
        self._clear_params()

    def _refresh_step_list(self, select: int | None = None):
        self.step_list.delete(0, tk.END)
        for item in self.pipeline_steps:
            pname = next(iter(item))
            cls = self.available_plugins.get(pname)
            display = getattr(cls, "label", pname) if cls else pname
            self.step_list.insert(tk.END, display)
        if select is not None and 0 <= select < self.step_list.size():
            self.step_list.select_set(select)
        self._show_params()

    # ----------------- 右侧参数展示 -----------------
    def _clear_params(self):
        for w in self.param_container.winfo_children():
            w.destroy()

    def _show_params(self):
        self._clear_params()
        idxs = self.step_list.curselection()
        if not idxs:
            return
        idx = idxs[0]
        step = self.pipeline_steps[idx]
        plugin_name, params = next(iter(step.items()))
        cls = self.available_plugins.get(plugin_name)

        row = 0
        for key, value in params.items():
            label_cn = getattr(cls, "param_labels", {}).get(key, key)
            ttk.Label(self.param_container, text=label_cn).grid(row=row, column=0, sticky="w", pady=2)
            var = tk.StringVar(value=str(value))
            ent = ttk.Entry(self.param_container, textvariable=var, width=12)
            ent.grid(row=row, column=1, sticky="ew", pady=2)
            ent.bind("<FocusOut>", lambda _e, k=key, v=var: self._update_param(idx, k, v))
            row += 1
        self.param_container.columnconfigure(1, weight=1)

    def _update_param(self, step_idx: int, key: str, var: tk.StringVar):
        if getattr(self, "read_only", False):
            return
        val_str = var.get()
        for cast in (int, float, str):
            try:
                val = cast(val_str)
                break
            except Exception:
                continue
        self.pipeline_steps[step_idx][next(iter(self.pipeline_steps[step_idx]))][key] = val

    # ----------------- 参数对话框 -----------------
    def _open_plugin_dialog(self):
        idxs = self.step_list.curselection()
        if not idxs:
            return
        idx = idxs[0]
        step = self.pipeline_steps[idx]
        plugin_name, params = next(iter(step.items()))
        cls = self.available_plugins.get(plugin_name)
        if cls is None:
            messagebox.showerror("错误", f"未找到插件 {plugin_name}")
            return

        if hasattr(cls, "open_param_dialog"):

            def _on_change(new_params: Dict[str, Any]):
                step[plugin_name] = _yaml_sanitize(new_params)
                self._show_params()

            # 计算上一步输出做预览
            preview_img: Optional[np.ndarray] = None
            ctx: dict[str, Any] = {}
            if idx > 0:
                try:
                    # 使用配方编辑器的测试图像作为起始图像
                    import numpy as np
                    if self.test_image_path:
                        img = cv2.imread(str(self.test_image_path))
                        print(f"[DEBUG] 使用配方测试图像: {self.test_image_path}")
                    else:
                        img = np.full((480, 640, 3), 128, dtype=np.uint8)
                        print("[DEBUG] 使用虚拟灰色图像")
                    
                    ctx: dict[str, Any] = {}
                    for j in range(idx):
                        name_j, params_j = next(iter(self.pipeline_steps[j].items()))
                        inst_j = self.available_plugins[name_j](**params_j)
                        img, ctx = inst_j.process(img, ctx)  # type: ignore
                    preview_img = img
                    # 将上游 pose 传递给插件 UI
                    if ctx is not None:
                        pose_info = ctx.get('pose')
                        params['pose'] = pose_info
                        print(f"[DEBUG] 传递pose信息给插件: {pose_info}")
                except Exception as err:
                    import traceback

                    traceback.print_exc()
                    messagebox.showwarning("预览计算失败", str(err))
            else:
                # 第一个插件也提供测试图像
                if self.test_image_path:
                    preview_img = cv2.imread(str(self.test_image_path))
                    print(f"[DEBUG] 第一个插件使用配方测试图像: {self.test_image_path}")
                else:
                    import numpy as np
                    preview_img = np.full((480, 640, 3), 128, dtype=np.uint8)
                    print("[DEBUG] 第一个插件使用虚拟灰色图像")
            try:
                if preview_img is not None:
                    # 将图像传递给插件的参数对话框
                    cls.open_param_dialog(
                        self,
                        params,
                        _on_change,
                        preview_img=preview_img
                    )
                else:
                    cls.open_param_dialog(self, params, _on_change)
            except Exception as e:
                import traceback

                traceback.print_exc()
                messagebox.showerror("打开失败", str(e))
        else:
            messagebox.showwarning("无专属界面", f"{plugin_name} 未实现 open_param_dialog")

    # ----------------- YAML 读写 -----------------
    def _load_yaml(self):
        path = filedialog.askopenfilename(filetypes=[("YAML", "*.yaml;*.yml")])
        if not path:
            return
        try:
            data = yaml.safe_load(Path(path).read_text(encoding="utf-8")) or []
            if not isinstance(data, list):
                raise ValueError("YAML 顶层应为列表")
            self.pipeline_steps = data
            self._refresh_step_list()
        except Exception as e:
            messagebox.showerror("加载失败", str(e))

    def _save_yaml(self):
        # 默认目录 config/pipelines
        base_dir = Path(__file__).resolve().parents[1] / "config" / "pipelines"
        base_dir.mkdir(parents=True, exist_ok=True)
        # 根据当前窗口标题或其他途径推断工位 ID
        ws_id = getattr(self, "workstation_id", None) or "pipeline"
        default_name = f"{ws_id}.yaml"
        path = filedialog.asksaveasfilename(
            initialdir=base_dir,
            initialfile=default_name,
            defaultextension=".yaml",
            filetypes=[("YAML", "*.yaml;*.yml")],
        )
        if not path:
            return
        try:
            yaml.safe_dump(
                _yaml_sanitize(self.pipeline_steps),
                Path(path).open("w", encoding="utf-8"),
                allow_unicode=True,
            )
        except Exception as e:
            _logger.error('save_yaml failed: %s', e, exc_info=True)
            # 诊断不可序列化路径
            def _walk(obj, path='root'):
                import numpy as _np, tkinter as _tk
                BASIC = (str, int, float, bool, type(None))
                if isinstance(obj, BASIC):
                    return
                if isinstance(obj, (list, tuple, set)):
                    for i, v in enumerate(obj):
                        _walk(v, f"{path}[{i}]")
                elif isinstance(obj, dict):
                    for k, v in obj.items():
                        _walk(v, f"{path}.{k}")
                else:
                    _logger.warning('非基本类型 %s at %s: %s', type(obj), path, obj)
            _walk(self.pipeline_steps)
            messagebox.showerror("保存失败", str(e))
        else:
            messagebox.showinfo("已保存", path)

    # ----------------- 测试运行 -----------------
    def _test_run(self):
        if not self.pipeline_steps:
            messagebox.showwarning("空流程", "请先添加步骤")
            return

        img: Optional[np.ndarray] = None
        ctx: dict[str, Any] = {}

        # 若首步不是采集插件 -> 让用户选图
        first_name, _ = next(iter(self.pipeline_steps[0].items()))
        first_cls = self.available_plugins.get(first_name)
        if first_cls is None:
            messagebox.showerror("错误", f"未找到插件 {first_name}")
            return
        if getattr(first_cls, "category", "") != "图像采集":
            img = self._get_test_img()
            if img is None:
                return

        try:
            # 设置测试环境变量，供插件使用
            import os
            if self.pipeline_path and self.pipeline_path != Path("<in-memory>"):
                # 如果有pipeline文件路径，使用其父目录
                os.environ['CURRENT_WORKSTATION_PATH'] = str(self.pipeline_path)
                print(f"[DEBUG] 流程编辑器测试设置环境变量: {self.pipeline_path}")

            for step in self.pipeline_steps:
                name, params = next(iter(step.items()))
                cls = self.available_plugins.get(name)
                if cls is None:
                    raise RuntimeError(f"未找到插件 {name}")
                inst = cls(**params)
                img, ctx = inst.process(img, ctx)  # type: ignore

            # -------- 保存结果 --------
            out_dir = Path(ROOT_DIR) / "test_outputs"
            out_dir.mkdir(parents=True, exist_ok=True)
            ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            paths: list[str] = []

            if img is not None:
                img_path = out_dir / f"result_{ts}.png"
                cv2.imencode(".png", img)[1].tofile(str(img_path))
                paths.append(str(img_path))

            # 新增：提取关键检测结果
            result_info = {}
            if ctx:
                if "rectangle_gauge" in ctx:
                    rect_info = ctx["rectangle_gauge"]
                    result_info["rectangle"] = {
                        "width": rect_info["width"],
                        "height": rect_info["height"],
                        "center": rect_info["center"],
                        "corners": rect_info["corners"]
                    }
                    # 新增：条件写入椭圆信息
                    if rect_info["ellipse"] is not None:
                        result_info["ellipse"] = {
                            "center": rect_info["ellipse"][0],
                            "axes": rect_info["ellipse"][1],
                            "angle": rect_info["ellipse"][2]
                        }

                data_path = out_dir / f"result_{ts}.json"
                def _np_default(o):
                    import numpy as _np
                    if isinstance(o, _np.generic):
                        return o.item()
                    if isinstance(o, _np.ndarray):
                        return o.tolist()
                    # fallback: 转成字符串
                    return str(o)
                
                # 修改：仅输出结构化检测结果（替代原始 ctx 全量输出）
                if result_info:
                    json.dump(
                        result_info,
                        data_path.open("w", encoding="utf-8"),
                        ensure_ascii=False,
                        indent=2,
                        default=_np_default
                    )
                    paths.append(str(data_path))

    
            messagebox.showinfo("已完成", "结果已保存:\n" + "\n".join(paths))
        except Exception as e:
            import traceback

            traceback.print_exc()
            messagebox.showerror("执行失败", str(e))


    # ----------------- 退出清理 -----------------
    def destroy(self):
        """关闭窗口；仅在独立调试（主窗口 withdraw 状态）时退出主循环。"""
        # 如果有指定的pipeline_path且不是只读模式，自动保存
        if (self.pipeline_path and
            not getattr(self, 'read_only', False) and
            self.pipeline_steps):
            try:
                pipeline_path = Path(self.pipeline_path)
                print(f"[DEBUG] 流程编辑器准备保存: {pipeline_path}")
                print(f"[DEBUG] 父目录: {pipeline_path.parent}")
                print(f"[DEBUG] 父目录存在: {pipeline_path.parent.exists()}")
                print(f"[DEBUG] 流程步骤数量: {len(self.pipeline_steps)}")

                # 确保父目录存在
                pipeline_path.parent.mkdir(parents=True, exist_ok=True)

                yaml.safe_dump(
                    _yaml_sanitize(self.pipeline_steps),
                    pipeline_path.open("w", encoding="utf-8"),
                    allow_unicode=True,
                )
                print(f"[DEBUG] 流程编辑器自动保存成功: {pipeline_path}")
                print(f"[DEBUG] 保存后文件存在: {pipeline_path.exists()}")
            except Exception as e:
                print(f"[ERROR] 流程编辑器自动保存失败: {e}")
                import traceback
                traceback.print_exc()

        super().destroy()
        # 只有在独立运行模式下（master是根窗口且被隐藏）才退出主循环
        # 从配方编辑器调用时，master是配方编辑器窗口，不应该退出主循环
        if (isinstance(self.master, tk.Tk) and
            hasattr(self.master, 'winfo_viewable') and
            not self.master.winfo_viewable() and
            self.master.winfo_exists()):
            # 独立运行时，root 被 withdraw；此时安全退出主循环
            self.master.quit()


# ----------------- 独立调试 -----------------
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # 隐藏空白主窗口
    PipelineEditor(root)
    root.mainloop()
