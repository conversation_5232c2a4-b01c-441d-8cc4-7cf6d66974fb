"""Pipeline plugin for acquiring images from various sources."""
from __future__ import annotations
from typing import Dict, Any, Optional
import cv2

from plugins.plugin_base import PluginBase
from plugins.core.image_process.image_source import create_source, ImageSource

__all__ = ["ImageAcquire"]

class ImageAcquire(PluginBase):
    """图像采集插件（相机/文件夹/单张）"""

    name = "image_acquire"
    label = "图像采集"
    category = "图像采集"

    params: Dict[str, Any] = {
        "mode": "folder",   # camera|folder|file
        "path": "samples/",  # folder path or file path or camera index
    }

    param_labels = {
        "mode": "模式",
        "path": "路径/索引",
    }

    def setup(self, params: Dict[str, Any]):
        mode = params.get("mode", "folder")
        path = str(params.get("path", ""))
        self.source: ImageSource = create_source(mode, path)
        self.opened = self.source.open()

    def process(self, img, ctx):
        if not self.opened:
            return None, ctx
        frame = self.source.next_frame()
        if frame is None:
            return img, ctx  # no new frame, pass-through
        ctx[self.name] = {}
        return frame, ctx

    def __del__(self):
        if hasattr(self, 'source') and self.opened:
            self.source.close()
            self.opened = False

    # ---------------- UI -----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, **_extra):
        """Use full ImageSourceUI for editing parameters."""
        import importlib, tkinter as tk
        mod = importlib.import_module('plugins.ui.image_process.image_process_ui')
        if not hasattr(mod, 'ImageSourceUI'):
            from tkinter import messagebox
            messagebox.showerror('错误', '未找到 ImageSourceUI')
            return
        ui: 'mod.ImageSourceUI' = mod.ImageSourceUI(master)
        # 填充初始参数
        mode_map_rev = {'camera':'相机','folder':'文件夹','file':'单图'}
        ui.mode_var.set(mode_map_rev.get(params.get('mode','folder'), '文件夹'))
        ui.path_var.set(str(params.get('path','')))
        ui._mode_changed()

        def _on_confirm():
            # 读取参数
            m_cn = ui.mode_var.get()
            mode = ui._mode_map[m_cn]
            path = ui.path_var.get().strip()
            on_change({'mode': mode, 'path': path})
            ui.destroy()
        tk.Button(ui, text='确定', command=_on_confirm).pack(pady=4)



# register
_ = ImageAcquire()
