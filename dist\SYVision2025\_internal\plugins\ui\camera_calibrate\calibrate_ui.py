from __future__ import annotations

"""Camera Calibration UI.
Allows collecting images, running calibration, and saving/loading results.
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
import sys, cv2
from datetime import datetime
from PIL import Image, ImageTk
import numpy as np

BASE_DIR = Path(__file__).resolve().parents[3]
if str(BASE_DIR) not in sys.path:
    sys.path.append(str(BASE_DIR))

from plugins.camera_calibrate import CalibrateEngine, load_calibration

PREVIEW_MAX = 550

# ---- config dir ----
CFG_DIR = BASE_DIR / 'configs' / 'calib'
CFG_DIR.mkdir(parents=True, exist_ok=True)

class CameraCalibFrame(ttk.Frame):
    def __init__(self, master, calib_path: str | None = None, **_extra):
        super().__init__(master)
        # --- chessboard parameters ---
        self.cols_var = tk.IntVar(value=9)
        self.rows_var = tk.IntVar(value=6)
        self.square_var = tk.DoubleVar(value=25.0)

        self.engine = self._make_engine()
        self.imgs: list[np.ndarray] = []
        self._build_ui()

    def _build_ui(self):
        left = ttk.Frame(self)
        left.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        right = ttk.Frame(self)
        right.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # chessboard param inputs
        ttk.Label(left, text='内角点列 x 行').pack(anchor=tk.W)
        frm = ttk.Frame(left); frm.pack(anchor=tk.W)
        ttk.Entry(frm, textvariable=self.cols_var, width=4).pack(side=tk.LEFT)
        ttk.Label(frm, text='x').pack(side=tk.LEFT)
        ttk.Entry(frm, textvariable=self.rows_var, width=4).pack(side=tk.LEFT)
        ttk.Label(left, text='方格(mm)').pack(anchor=tk.W)
        ttk.Entry(left, textvariable=self.square_var, width=6).pack(anchor=tk.W)
        ttk.Button(left, text='更新参数', command=self._update_params).pack(anchor=tk.W, pady=(2,6))

        ttk.Button(left, text='添加图片', command=self._add_img).pack(anchor=tk.W, pady=2)
        ttk.Button(left, text='清空', command=self._clear).pack(anchor=tk.W)
        ttk.Button(left, text='执行标定', command=self._run).pack(anchor=tk.W, pady=10)
        ttk.Button(left, text='保存结果', command=self._save).pack(anchor=tk.W, pady=2)
        ttk.Button(left, text='加载结果', command=self._load).pack(anchor=tk.W)
        ttk.Button(left, text='查看去畸变', command=self._preview_undist).pack(anchor=tk.W, pady=2)
        ttk.Button(left, text='保存去畸变', command=self._save_undist).pack(anchor=tk.W, pady=2)
        ttk.Button(left, text='批量去畸变', command=self._batch_undist).pack(anchor=tk.W)

        self.listbox = tk.Listbox(left, height=15, width=25)
        self.listbox.pack(anchor=tk.W, pady=5)

        ttk.Label(left, text='RMS:').pack(anchor=tk.W)
        self.rms_var = tk.StringVar(value='-')
        ttk.Label(left, textvariable=self.rms_var).pack(anchor=tk.W)

        # preview canvas
        self.canvas = tk.Canvas(right, width=PREVIEW_MAX, height=PREVIEW_MAX, bg='gray')
        self.canvas.pack(fill=tk.BOTH, expand=True)

    # ---------- handlers ----------
    def _add_img(self):
        paths = filedialog.askopenfilenames(title='选择棋盘格图片', filetypes=[('Image','*.png;*.jpg;*.bmp')])
        for p in paths:
            img = cv2.imread(p)
            if img is None:
                continue
            ok = self.engine.add_view(img, vis=False)
            if ok:
                self.imgs.append(img)
                self.listbox.insert(tk.END, p)
                self._show_img(img)
            else:
                messagebox.showinfo('提示','未检测到棋盘格')

    def _clear(self):
        self.engine = CalibrateEngine()
        self.imgs.clear()
        self.listbox.delete(0, tk.END)
        self.rms_var.set('-')
        self.canvas.delete('all')

    def _run(self):
        try:
            _, rms = self.engine.calibrate()
        except RuntimeError as e:
            messagebox.showerror('错误', str(e))
            return
        self.rms_var.set(f'{rms:.4f}')
        messagebox.showinfo('完成', f'标定完成。RMS={rms:.4f}')

    def _save(self):
        if self.engine.result is None:
            messagebox.showwarning('提示','请先执行标定')
            return
        now = datetime.now().strftime('%Y%m%d_%H%M')
        default_name = f'intrinsics_{now}.yml'
        path = filedialog.asksaveasfilename(initialdir=CFG_DIR,
                                            initialfile=default_name,title='保存标定结果', defaultextension='.yml', filetypes=[('YAML','*.yml')])
        if not path:
            return
        self.engine.save(path)
        messagebox.showinfo('提示', f'已保存到 {path}')

    def _load(self):
        path = filedialog.askopenfilename(title='加载标定结果', initialdir=CFG_DIR,
                                           filetypes=[('YAML','*.yml')])
        if not path:
            return
        try:
            res = load_calibration(path)
        except Exception as e:
            messagebox.showerror('错误', str(e))
            return
        self.engine = CalibrateEngine()
        self.engine.result = res
        self.rms_var.set(f'{res.rms:.4f}')
        messagebox.showinfo('提示','加载成功')

        # ---- undistort helpers ----
    def _preview_undist(self):
        sel = self.listbox.curselection()
        if not sel:
            messagebox.showwarning('提示','请先在列表选择一张图片')
            return
        if self.engine.result is None:
            messagebox.showwarning('提示','请先完成标定')
            return
        idx = sel[0]
        img = self.imgs[idx]
        und = self.engine.undistort(img)

        # --- new toplevel window with side-by-side ---
        win = tk.Toplevel(self)
        win.title('畸变校正对比')
        frm = ttk.Frame(win); frm.pack(padx=5, pady=5)
        canv_w = PREVIEW_MAX; canv_h = PREVIEW_MAX
        can_orig = tk.Canvas(frm, width=canv_w, height=canv_h, bg='gray'); can_orig.pack(side=tk.LEFT)
        can_und = tk.Canvas(frm, width=canv_w, height=canv_h, bg='gray'); can_und.pack(side=tk.RIGHT)

        def _photo(img_np):
            h, w = img_np.shape[:2]
            scale = min(PREVIEW_MAX/w, PREVIEW_MAX/h, 1.0)
            disp = cv2.resize(img_np, (int(w*scale), int(h*scale)))
            disp_rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
            return ImageTk.PhotoImage(Image.fromarray(disp_rgb))

        win.photo_orig = _photo(img)  # keep reference
        win.photo_und = _photo(und)
        can_orig.create_image(PREVIEW_MAX//2, PREVIEW_MAX//2, image=win.photo_orig)
        can_und.create_image(PREVIEW_MAX//2, PREVIEW_MAX//2, image=win.photo_und)

        ttk.Label(frm, text='原图').place(x=PREVIEW_MAX//2-20, y=5)
        ttk.Label(frm, text='去畸变').place(x=PREVIEW_MAX+PREVIEW_MAX//2-30, y=5)

    def _save_undist(self):
        """保存当前选中图片的去畸变结果"""
        if self.engine.result is None:
            messagebox.showwarning('提示','请先完成标定')
            return
        sel = self.listbox.curselection()
        if not sel:
            messagebox.showwarning('提示','请先在列表选择一张图片')
            return
        idx = sel[0]
        img = self.imgs[idx]
        und = self.engine.undistort(img)
        default_name = Path(self.listbox.get(idx)).stem + '_undist.png'
        path = filedialog.asksaveasfilename(title='保存去畸变图像', initialfile=default_name,
                                            defaultextension='.png', filetypes=[('PNG','*.png')])
        if not path:
            return
        cv2.imwrite(path, und)
        messagebox.showinfo('提示', f'已保存到 {path}')

    def _batch_undist(self):
        if self.engine.result is None:
            messagebox.showwarning('提示','请先完成标定')
            return
        if not self.imgs:
            messagebox.showwarning('提示','请先添加图片')
            return
        out_dir = filedialog.askdirectory(title='选择输出文件夹')
        if not out_dir:
            return
        for i,(p,img) in enumerate(zip(self.listbox.get(0, tk.END), self.imgs)):
            und = self.engine.undistort(img)
            name = Path(p).stem + '_undist.png'
            cv2.imwrite(str(Path(out_dir)/name), und)
        messagebox.showinfo('提示', '已保存至 '+out_dir)

    # ---- preview helper ----
    def _update_params(self):
        # recreate engine with new parameters and clear collected views
        self.engine = self._make_engine()
        self.imgs.clear()
        self.listbox.delete(0, tk.END)
        self.rms_var.set('-')
        messagebox.showinfo('提示','参数已更新，已清空已采集视图')

    def _make_engine(self):
        return CalibrateEngine((self.cols_var.get(), self.rows_var.get()), self.square_var.get())

    def _show_img(self, img: np.ndarray):
        h, w = img.shape[:2]
        scale = min(PREVIEW_MAX/w, PREVIEW_MAX/h, 1.0)
        disp = cv2.resize(img, (int(w*scale), int(h*scale)))
        disp_rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
        pil = Image.fromarray(disp_rgb)
        self._photo = ImageTk.PhotoImage(pil)
        self.canvas.delete('all')
        self.canvas.create_image(PREVIEW_MAX//2, PREVIEW_MAX//2, image=self._photo)

# ------------- run standalone -------------
if __name__ == '__main__':
    root = tk.Tk()
    root.title('Camera Calibration')
    CameraCalibFrame(root).pack(fill=tk.BOTH, expand=True)
    root.mainloop()
