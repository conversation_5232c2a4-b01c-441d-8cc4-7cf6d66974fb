"""Core image smoothing/softening processor.
Supports Gaussian blur, bilateral filter, and morphological operations for creating soft, pleasant visual effects.
"""
from __future__ import annotations
import cv2
import numpy as np
from typing import Dict, Any

__all__ = ['SmoothProcessor']

class SmoothProcessor:
    name = 'ImageSmooth'

    def __init__(self, *, method: str = 'gaussian', kernel_size: int = 15,
                 sigma_x: float = 0, sigma_y: float = 0,
                 bilateral_d: int = 9, bilateral_sigma_color: float = 75, bilateral_sigma_space: float = 75,
                 contrast_alpha: float = 0.8, brightness_beta: int = 20,
                 morph_kernel_size: int = 3, enable_contrast_adjust: bool = True, enable_morph: bool = False,
                 gamma: float = 1.0, saturation: float = 1.0, hue_shift: int = 0,
                 enable_gamma: bool = False, enable_color_adjust: bool = False,
                 noise_reduction: float = 0.0, enable_noise_reduction: bool = False,
                 edge_preserve: float = 0.5, enable_edge_preserve: bool = False):
        """Parameters
        method: 'gaussian', 'bilateral', 'box', 'median'
        kernel_size: blur kernel size (must be odd and >=3)
        sigma_x: Gaussian kernel standard deviation in X direction (0 = auto)
        sigma_y: Gaussian kernel standard deviation in Y direction (0 = auto)
        bilateral_d: diameter for bilateral filter
        bilateral_sigma_color: filter sigma in the color space
        bilateral_sigma_space: filter sigma in the coordinate space
        contrast_alpha: contrast adjustment factor (0.0-2.0, <1.0 reduces contrast)
        brightness_beta: brightness adjustment offset (-100 to 100)
        morph_kernel_size: morphological operation kernel size
        enable_contrast_adjust: whether to apply contrast/brightness adjustment
        enable_morph: whether to apply morphological closing
        gamma: gamma correction value (0.1-3.0, 1.0=no change)
        saturation: color saturation multiplier (0.0-2.0, 1.0=no change)
        hue_shift: hue shift in degrees (-180 to 180)
        enable_gamma: whether to apply gamma correction
        enable_color_adjust: whether to apply color adjustments
        noise_reduction: noise reduction strength (0.0-1.0)
        enable_noise_reduction: whether to apply noise reduction
        edge_preserve: edge preservation strength (0.0-1.0)
        enable_edge_preserve: whether to apply edge preservation
        """
        self.method = method
        self.kernel_size = int(kernel_size) | 1  # ensure odd
        self.sigma_x = float(sigma_x)
        self.sigma_y = float(sigma_y)
        self.bilateral_d = int(bilateral_d)
        self.bilateral_sigma_color = float(bilateral_sigma_color)
        self.bilateral_sigma_space = float(bilateral_sigma_space)
        self.contrast_alpha = float(contrast_alpha)
        self.brightness_beta = int(brightness_beta)
        self.morph_kernel_size = int(morph_kernel_size) | 1  # ensure odd
        self.enable_contrast_adjust = bool(enable_contrast_adjust)
        self.enable_morph = bool(enable_morph)
        # 新增参数
        self.gamma = float(gamma)
        self.saturation = float(saturation)
        self.hue_shift = int(hue_shift)
        self.enable_gamma = bool(enable_gamma)
        self.enable_color_adjust = bool(enable_color_adjust)
        self.noise_reduction = float(noise_reduction)
        self.enable_noise_reduction = bool(enable_noise_reduction)
        self.edge_preserve = float(edge_preserve)
        self.enable_edge_preserve = bool(enable_edge_preserve)

    def process(self, img: np.ndarray, **override) -> Dict[str, Any]:
        """Process image and return dictionary with 'output' key."""
        if override:
            for k, v in override.items():
                if hasattr(self, k):
                    setattr(self, k, v)
        
        if img is None:
            return {"output": None}
        
        # Work with copy to avoid modifying original
        result = img.copy()
        
        # Apply smoothing based on method
        method = self.method.lower()
        if method == 'gaussian':
            result = cv2.GaussianBlur(result, (self.kernel_size, self.kernel_size), 
                                    self.sigma_x, sigmaY=self.sigma_y)
        elif method == 'bilateral':
            result = cv2.bilateralFilter(result, self.bilateral_d, 
                                       self.bilateral_sigma_color, self.bilateral_sigma_space)
        elif method == 'box':
            result = cv2.boxFilter(result, -1, (self.kernel_size, self.kernel_size))
        elif method == 'median':
            result = cv2.medianBlur(result, self.kernel_size)
        else:
            # Default to Gaussian
            result = cv2.GaussianBlur(result, (self.kernel_size, self.kernel_size), 
                                    self.sigma_x, sigmaY=self.sigma_y)
        
        # Apply contrast and brightness adjustment if enabled
        if self.enable_contrast_adjust:
            result = cv2.convertScaleAbs(result, alpha=self.contrast_alpha, beta=self.brightness_beta)
        
        # Apply morphological closing if enabled
        if self.enable_morph:
            kernel = np.ones((self.morph_kernel_size, self.morph_kernel_size), np.uint8)
            result = cv2.morphologyEx(result, cv2.MORPH_CLOSE, kernel)

        # Apply gamma correction if enabled
        if self.enable_gamma and self.gamma != 1.0:
            gamma_table = np.array([((i / 255.0) ** (1.0 / self.gamma)) * 255 for i in np.arange(0, 256)]).astype("uint8")
            result = cv2.LUT(result, gamma_table)

        # Apply color adjustments if enabled
        if self.enable_color_adjust and len(result.shape) == 3:
            result = self._apply_color_adjustments(result)

        # Apply noise reduction if enabled
        if self.enable_noise_reduction and self.noise_reduction > 0:
            result = self._apply_noise_reduction(result)

        # Apply edge preservation if enabled
        if self.enable_edge_preserve and self.edge_preserve > 0:
            result = self._apply_edge_preservation(result)

        return {"output": result}

    def _apply_color_adjustments(self, img: np.ndarray) -> np.ndarray:
        """应用颜色调整（饱和度和色相）"""
        if len(img.shape) != 3:
            return img

        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV).astype(np.float32)

        # 调整饱和度
        if self.saturation != 1.0:
            hsv[:, :, 1] = hsv[:, :, 1] * self.saturation
            hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)

        # 调整色相
        if self.hue_shift != 0:
            hsv[:, :, 0] = hsv[:, :, 0] + self.hue_shift
            hsv[:, :, 0] = hsv[:, :, 0] % 180  # 色相环绕

        # 转换回BGR
        result = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)
        return result

    def _apply_noise_reduction(self, img: np.ndarray) -> np.ndarray:
        """应用噪声减少"""
        strength = int(self.noise_reduction * 10) + 1
        if len(img.shape) == 3:
            return cv2.fastNlMeansDenoisingColored(img, None, strength, strength, 7, 21)
        else:
            return cv2.fastNlMeansDenoising(img, None, strength, 7, 21)

    def _apply_edge_preservation(self, img: np.ndarray) -> np.ndarray:
        """应用边缘保持滤波"""
        # 使用边缘保持滤波器
        sigma_s = 50 + self.edge_preserve * 100  # 空间窗口大小
        sigma_r = 0.1 + self.edge_preserve * 0.4  # 颜色窗口大小
        return cv2.edgePreservingFilter(img, flags=1, sigma_s=sigma_s, sigma_r=sigma_r)

    def get_info(self) -> Dict[str, Any]:
        """Return processor information."""
        return {
            "name": self.name,
            "method": self.method,
            "kernel_size": self.kernel_size,
            "sigma_x": self.sigma_x,
            "sigma_y": self.sigma_y,
            "contrast_alpha": self.contrast_alpha,
            "brightness_beta": self.brightness_beta,
            "enable_contrast_adjust": self.enable_contrast_adjust,
            "enable_morph": self.enable_morph
        }
