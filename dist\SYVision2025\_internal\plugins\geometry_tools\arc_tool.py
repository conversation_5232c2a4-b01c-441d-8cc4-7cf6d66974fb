# -*- coding: utf-8 -*-
"""Arc measurement tool.

Given an approximate circular ROI (defined by centre and estimated radius) this module
extracts edge points, fits a circle robustly (RANSAC), and determines the
visible arc section (start / end angle, arc length, central angle).
"""
from __future__ import annotations

from typing import Any, Dict, <PERSON><PERSON>, Optional, List
import numpy as np
import cv2
import random

__all__ = [
    'measure_arc',
]

def _circle_from_3pts(p1: np.ndarray, p2: np.ndarray, p3: np.ndarray) -> Tuple[np.ndarray, float]:
    """Return (center, radius) of the circle going through 3 points.
    Raise ValueError if points are colinear.
    """
    x1, y1 = p1
    x2, y2 = p2
    x3, y3 = p3
    det = 2 * (x1*(y2 - y3) - y1*(x2 - x3) + x2*y3 - y2*x3)
    if abs(det) < 1e-6:
        raise ValueError('Colinear')
    ux = ((x1**2 + y1**2)*(y2 - y3) + (x2**2 + y2**2)*(y3 - y1) + (x3**2 + y3**2)*(y1 - y2)) / det
    uy = ((x1**2 + y1**2)*(x3 - x2) + (x2**2 + y2**2)*(x1 - x3) + (x3**2 + y3**2)*(x2 - x1)) / det
    center = np.array([ux, uy], dtype=np.float32)
    radius = float(np.linalg.norm(center - p1))
    return center, radius

def _simple_adaptive_roi(image: np.ndarray, cx: float, cy: float, r_est: float,
                        canny_th1: int, canny_th2: int, base_factor: float) -> Dict[str, Any]:
    """简化的自适应ROI：基于边缘点密度选择最佳ROI大小"""
    h, w = image.shape[:2]

    # 尝试3个不同的ROI尺寸
    scale_factors = [base_factor * 0.8, base_factor, base_factor * 1.3]
    best_result = None
    best_score = -1

    for scale in scale_factors:
        # 计算ROI边界
        r_pad = int(r_est * scale) + 5
        x0, y0 = int(cx - r_pad), int(cy - r_pad)
        x1, y1 = int(cx + r_pad), int(cy + r_pad)
        x0, y0 = max(x0, 0), max(y0, 0)
        x1, y1 = min(x1, w-1), min(y1, h-1)

        if x1 - x0 < 10 or y1 - y0 < 10:
            continue

        roi = image[y0:y1, x0:x1]
        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, canny_th1, canny_th2)
        edge_count = np.sum(edges > 0)

        # 计算边缘点密度和分布质量
        if edge_count > 0:
            pts = np.column_stack(np.nonzero(edges))
            pts_img = np.stack((pts[:,1] + x0, pts[:,0] + y0), axis=1).astype(np.float32)

            # 计算点到估计中心的距离
            distances = np.sqrt((pts_img[:, 0] - cx)**2 + (pts_img[:, 1] - cy)**2)

            # 评估质量：边缘点数量 + 距离分布的一致性
            density_score = min(edge_count / 200.0, 1.0)  # 边缘点密度

            # 距离一致性：计算距离标准差
            if len(distances) > 5:
                dist_std = np.std(distances)
                consistency_score = max(0, 1.0 - dist_std / r_est)
            else:
                consistency_score = 0

            total_score = density_score * 0.6 + consistency_score * 0.4

            if total_score > best_score:
                best_score = total_score
                best_result = {
                    'roi': roi,
                    'x0': x0,
                    'y0': y0,
                    'scale': scale,
                    'score': total_score
                }

    if best_result is None:
        return {'error': 'simple adaptive ROI failed'}

    return best_result


def _adaptive_roi_detection(image: np.ndarray, cx: float, cy: float, r_est: float,
                           canny_th1: int, canny_th2: int, base_factor: float,
                           subpixel: bool, subpixel_window: int) -> Dict[str, Any]:
    """自适应ROI检测：尝试多个ROI尺寸，选择检测效果最好的"""
    h, w = image.shape[:2]

    # 尝试多个ROI扩展因子
    scale_factors = [base_factor * 0.8, base_factor, base_factor * 1.2, base_factor * 1.5]
    best_result = None
    best_score = -1

    for scale in scale_factors:
        try:
            # 计算ROI边界
            r_pad = int(r_est * scale) + 5
            x0, y0 = int(cx - r_pad), int(cy - r_pad)
            x1, y1 = int(cx + r_pad), int(cy + r_pad)
            x0, y0 = max(x0, 0), max(y0, 0)
            x1, y1 = min(x1, w-1), min(y1, h-1)

            if x1 - x0 < 10 or y1 - y0 < 10:
                continue

            roi = image[y0:y1, x0:x1]
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, canny_th1, canny_th2)
            pts = np.column_stack(np.nonzero(edges))

            if pts.size == 0:
                continue

            # 亚像素精化
            if subpixel and len(pts) > 0:
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.1)
                corners = np.array([(pt[1], pt[0]) for pt in pts], dtype=np.float32).reshape(-1, 1, 2)
                cv2.cornerSubPix(gray, corners, (subpixel_window, subpixel_window), (-1, -1), criteria)
                pts_img = corners.reshape(-1, 2) + np.array([x0, y0], dtype=np.float32)
            else:
                pts_img = np.stack((pts[:,1] + x0, pts[:,0] + y0), axis=1).astype(np.float32)

            # 评估检测质量
            score = _evaluate_detection_quality(pts_img, cx, cy, r_est)

            if score > best_score:
                best_score = score
                best_result = {
                    'roi': roi,
                    'x0': x0,
                    'y0': y0,
                    'scale': scale,
                    'pts_img': pts_img,
                    'score': score
                }

        except Exception:
            continue

    if best_result is None:
        return {'error': 'adaptive ROI detection failed'}

    return best_result


def _evaluate_detection_quality(pts: np.ndarray, cx: float, cy: float, r_est: float) -> float:
    """评估边缘检测质量，返回质量分数"""
    if len(pts) < 10:
        return 0.0

    # 计算点到估计圆心的距离
    distances = np.sqrt((pts[:, 0] - cx)**2 + (pts[:, 1] - cy)**2)

    # 计算距离的标准差（越小越好，说明点更接近圆形）
    std_dev = np.std(distances)

    # 计算平均距离与估计半径的差异
    mean_dist = np.mean(distances)
    radius_error = abs(mean_dist - r_est) / r_est

    # 点数量奖励（更多点通常更好，但有上限）
    point_bonus = min(len(pts) / 100.0, 1.0)

    # 综合评分：距离一致性 + 半径准确性 + 点数量
    consistency_score = max(0, 1.0 - std_dev / r_est)  # 标准差越小分数越高
    accuracy_score = max(0, 1.0 - radius_error)        # 半径误差越小分数越高

    total_score = consistency_score * 0.5 + accuracy_score * 0.3 + point_bonus * 0.2

    return total_score


def _ransac_circle(points: np.ndarray, thresh: float = 2.0, iterations: int = 500) -> Tuple[np.ndarray, float, np.ndarray]:
    """RANSAC circle fit. Returns center, radius, inlier mask (bool array)."""
    best_inliers: List[int] = []
    best_center = np.array([0.0, 0.0])
    best_r = 0.0
    n = len(points)
    if n < 3:
        raise ValueError('Not enough points')
    idxs = list(range(n))
    for _ in range(iterations):
        sample = random.sample(idxs, 3)
        try:
            c, r = _circle_from_3pts(points[sample[0]], points[sample[1]], points[sample[2]])
        except ValueError:
            continue
        dists = np.linalg.norm(points - c, axis=1)
        inliers = np.where(np.abs(dists - r) < thresh)[0]
        if inliers.size > len(best_inliers):
            best_inliers = inliers.tolist()
            best_center = c
            best_r = r
    if len(best_inliers) < 3:
        raise ValueError('RANSAC failed')
    inlier_mask = np.zeros(n, dtype=bool)
    inlier_mask[best_inliers] = True
    # refine using least squares on inliers
    pts_in = points[inlier_mask]
    A = np.hstack((2*pts_in, np.ones((pts_in.shape[0],1))))
    b = (pts_in**2).sum(axis=1)
    x = np.linalg.lstsq(A, b, rcond=None)[0]
    center = x[:2]
    r = float(np.sqrt((center**2).sum() + x[2]))
    dists = np.linalg.norm(points - center, axis=1)
    inlier_mask = np.abs(dists - r) < thresh
    return center, r, inlier_mask

def _arc_angles(center: np.ndarray, pts: np.ndarray) -> Tuple[float, float, float]:
    """Return start_angle, end_angle, central_angle (rad) of arc covered by pts."""
    vecs = pts - center
    angles = np.arctan2(vecs[:,1], vecs[:,0])
    angles = np.mod(angles, 2*np.pi)
    angles_sorted = np.sort(angles)
    gaps = np.diff(np.concatenate([angles_sorted, angles_sorted[:1] + 2*np.pi]))
    max_gap_idx = np.argmax(gaps)
    start_idx = (max_gap_idx + 1) % len(angles_sorted)
    end_idx = max_gap_idx
    start_angle = angles_sorted[start_idx]
    end_angle = angles_sorted[end_idx]
    if end_angle < start_angle:
        end_angle += 2*np.pi
    central = end_angle - start_angle
    return float(start_angle), float(end_angle), float(central)

def measure_arc(
    image: np.ndarray,
    cx: float, cy: float, r_est: float,
    *,
    canny_th1: int = 50,
    canny_th2: int = 150,
    ransac_thresh: float = 2.0,
    ransac_iter: int = 500,
    subpixel: bool = True,
    subpixel_window: int = 5,
    adaptive_roi: bool = True,
    roi_expand_factor: float = 1.5,
    roi_size: int = 80,
    return_debug: bool = False
) -> Dict[str, Any]:
    """Measure an arc given an approximate circle ROI.

    Parameters
    ----------
    image : np.ndarray
        Source BGR image.
    cx, cy, r_est : float
        Approximate center (image coords) and radius.
    canny_th1, canny_th2 : int
        Canny thresholds.
    ransac_thresh : float
        Distance threshold for RANSAC inliers (pixels).
    return_debug : bool
        If True, include extra debug info in the result.
    """
    h, w = image.shape[:2]

    if adaptive_roi:
        # 简化的自适应ROI：基于边缘点密度调整
        roi_result = _simple_adaptive_roi(image, cx, cy, r_est, canny_th1, canny_th2, roi_expand_factor)
        if 'error' in roi_result:
            return roi_result
        roi, x0, y0, best_scale = roi_result['roi'], roi_result['x0'], roi_result['y0'], roi_result['scale']

        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, canny_th1, canny_th2)
        pts = np.column_stack(np.nonzero(edges))
        if pts.size == 0:
            return {'error': 'no edges in adaptive ROI'}

        # 亚像素边缘精化
        if subpixel and len(pts) > 0:
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.1)
            corners = np.array([(pt[1], pt[0]) for pt in pts], dtype=np.float32).reshape(-1, 1, 2)
            cv2.cornerSubPix(gray, corners, (subpixel_window, subpixel_window), (-1, -1), criteria)
            pts_img = corners.reshape(-1, 2) + np.array([x0, y0], dtype=np.float32)
        else:
            pts_img = np.stack((pts[:,1] + x0, pts[:,0] + y0), axis=1).astype(np.float32)
    else:
        # 固定大小ROI - 用户可控制的ROI大小
        half_size = roi_size // 2
        x0, y0 = int(cx - half_size), int(cy - half_size)
        x1, y1 = int(cx + half_size), int(cy + half_size)
        x0, y0 = max(x0, 0), max(y0, 0)
        x1, y1 = min(x1, w-1), min(y1, h-1)
        if x1 - x0 < 5 or y1 - y0 < 5:
            return {'error': 'ROI too small'}
        roi = image[y0:y1, x0:x1]
        best_scale = roi_expand_factor

        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

        # 使用稳定的边缘检测方法
        edges = cv2.Canny(gray, canny_th1, canny_th2)
        pts = np.column_stack(np.nonzero(edges))  # (row, col)
        if pts.size == 0:
            return {'error': 'no edges found'}

        # 亚像素边缘精化
        if subpixel and len(pts) > 0:
            try:
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.1)
                # 注意坐标顺序：cornerSubPix需要(x,y)格式
                corners = np.array([(pt[1], pt[0]) for pt in pts], dtype=np.float32).reshape(-1, 1, 2)
                cv2.cornerSubPix(gray, corners, (subpixel_window, subpixel_window), (-1, -1), criteria)
                # 转换回图像坐标：corners是(x,y)格式，需要加上ROI偏移
                pts_img = corners.reshape(-1, 2) + np.array([x0, y0], dtype=np.float32)
            except Exception as e:
                # 如果亚像素失败，使用原始边缘点
                pts_img = np.stack((pts[:,1] + x0, pts[:,0] + y0), axis=1).astype(np.float32)
        else:
            # 转换到图像坐标：pts是(row,col)格式，转换为(x,y)
            pts_img = np.stack((pts[:,1] + x0, pts[:,0] + y0), axis=1).astype(np.float32)

    # 检查边缘点数量
    if len(pts_img) < 10:
        return {'error': 'insufficient edge points'}

    # 第一步：严格的距离过滤 - 只保留接近估计半径的点
    distances_to_est = np.sqrt((pts_img[:, 0] - cx)**2 + (pts_img[:, 1] - cy)**2)
    # 非常严格的距离过滤，专注于真正的圆角
    distance_mask = (distances_to_est >= r_est * 0.7) & (distances_to_est <= r_est * 1.3)

    if np.sum(distance_mask) < 6:
        # 如果严格过滤后点太少，稍微放宽
        distance_mask = (distances_to_est >= r_est * 0.5) & (distances_to_est <= r_est * 1.5)
        if np.sum(distance_mask) < 6:
            return {'error': 'insufficient points near estimated radius'}

    pts_for_ransac = pts_img[distance_mask]

    # 第二步：RANSAC拟合圆
    try:
        center, radius, inliers_mask = _ransac_circle(pts_for_ransac, ransac_thresh, ransac_iter)
    except ValueError as e:
        return {'error': f'RANSAC circle fitting failed: {str(e)}'}

    # 第三步：验证拟合结果的合理性
    # 检查圆心是否在合理范围内（距离估计中心不超过估计半径的2倍）
    center_dist = np.sqrt((center[0] - cx)**2 + (center[1] - cy)**2)
    if center_dist > r_est * 2.0:
        return {'error': f'fitted center too far from estimate: {center_dist:.1f} > {r_est * 2.0:.1f}'}

    # 检查半径是否在合理范围内
    if radius < r_est * 0.3 or radius > r_est * 3.0:
        return {'error': f'fitted radius unreasonable: {radius:.1f} (estimate: {r_est:.1f})'}

    # 映射inliers回原始点集
    if len(pts_for_ransac) < len(pts_img):
        # 如果进行了过滤，需要映射索引
        if np.sum(distance_mask) == len(pts_for_ransac):
            # 使用距离过滤的索引
            filtered_indices = np.where(distance_mask)[0]
            inliers = filtered_indices[inliers_mask]
        else:
            # 没有过滤，直接使用
            inliers = inliers_mask
    else:
        inliers = inliers_mask
    inlier_pts = pts_img[inliers]
    start_a, end_a, central_a = _arc_angles(center, inlier_pts)
    arc_len = radius * central_a
    result: Dict[str, Any] = {
        'center': tuple(center.tolist()),
        'radius': radius,
        'start_angle_rad': start_a,
        'end_angle_rad': end_a,
        'central_angle_rad': central_a,
        'arc_length_px': arc_len,
    }
    if return_debug:
        result['all_edge_pts'] = pts_img
        result['inlier_pts'] = inlier_pts
    return result


if __name__ == '__main__':
    import argparse, os
    parser = argparse.ArgumentParser(description='Arc measurement quick test')
    parser.add_argument('image', help='image path')
    parser.add_argument('--cx', type=float, required=True)
    parser.add_argument('--cy', type=float, required=True)
    parser.add_argument('--r', type=float, required=True, help='estimated radius')
    args = parser.parse_args()
    img = cv2.imread(args.image)
    res = measure_arc(img, args.cx, args.cy, args.r, return_debug=True)
    print(res)
    if 'error' not in res:
        c = tuple(map(int, res['center']))
        r = int(res['radius'])
        cv2.circle(img, c, r, (0,255,0), 1)
        for pt in res['inlier_pts']:
            cv2.circle(img, tuple(pt.astype(int)), 1, (0,0,255), -1)
        cv2.imshow('arc', img)
        cv2.waitKey(0)
