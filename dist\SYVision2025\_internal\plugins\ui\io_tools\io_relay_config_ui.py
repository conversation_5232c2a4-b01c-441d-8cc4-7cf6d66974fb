"""IO Relay Configuration UI for Pipeline
继电器插件管道配置界面，支持条件触发模式配置
"""
from __future__ import annotations

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Callable

try:
    import serial.tools.list_ports as list_ports
except ImportError:
    list_ports = None


class IORelayConfigFrame(ttk.Frame):
    def __init__(self, master, params: Dict[str, Any], on_change: Callable):
        super().__init__(master)
        self.params = params.copy()
        self.on_change = on_change
        
        # 参数变量
        self.port_var = tk.StringVar(value=params.get("port", ""))
        self.baud_var = tk.IntVar(value=params.get("baud", 9600))
        self.relay_index_var = tk.IntVar(value=params.get("relay_index", 1))
        self.action_var = tk.StringVar(value=params.get("action", "CONDITIONAL"))
        self.trigger_on_ng_var = tk.BooleanVar(value=params.get("trigger_on_ng", True))
        self.trigger_on_ok_var = tk.BooleanVar(value=params.get("trigger_on_ok", False))
        self.pulse_duration_var = tk.IntVar(value=params.get("pulse_duration", 100))
        self.enable_delay_var = tk.BooleanVar(value=params.get("enable_delay", False))
        self.trigger_delay_var = tk.IntVar(value=params.get("trigger_delay", 0))
        
        self._build_ui()
        self._update_ui_state()
        
        # 绑定变量变化事件
        self.port_var.trace('w', self._on_param_change)
        self.baud_var.trace('w', self._on_param_change)
        self.relay_index_var.trace('w', self._on_param_change)
        self.action_var.trace('w', self._on_param_change)
        self.trigger_on_ng_var.trace('w', self._on_param_change)
        self.trigger_on_ok_var.trace('w', self._on_param_change)
        self.pulse_duration_var.trace('w', self._on_param_change)
        self.enable_delay_var.trace('w', self._on_param_change)
        self.trigger_delay_var.trace('w', self._on_param_change)
        
    def _build_ui(self):
        # 主框架
        main_frame = ttk.LabelFrame(self, text='继电器控制配置')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 基本配置
        basic_frame = ttk.LabelFrame(main_frame, text='基本设置')
        basic_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 串口配置
        ttk.Label(basic_frame, text='串口:').grid(row=0, column=0, sticky='e', padx=5, pady=2)
        self.port_combo = ttk.Combobox(basic_frame, textvariable=self.port_var, width=15)
        self.port_combo.grid(row=0, column=1, sticky='w', padx=5, pady=2)
        ttk.Button(basic_frame, text='刷新', command=self._refresh_ports).grid(row=0, column=2, padx=5, pady=2)
        
        ttk.Label(basic_frame, text='波特率:').grid(row=1, column=0, sticky='e', padx=5, pady=2)
        baud_combo = ttk.Combobox(basic_frame, textvariable=self.baud_var, values=[9600, 19200, 38400, 57600, 115200], width=15)
        baud_combo.grid(row=1, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(basic_frame, text='继电器通道:').grid(row=2, column=0, sticky='e', padx=5, pady=2)
        ttk.Spinbox(basic_frame, from_=1, to=16, textvariable=self.relay_index_var, width=15).grid(row=2, column=1, sticky='w', padx=5, pady=2)
        
        # 触发模式配置
        trigger_frame = ttk.LabelFrame(main_frame, text='触发模式')
        trigger_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(trigger_frame, text='动作模式:').grid(row=0, column=0, sticky='e', padx=5, pady=2)
        action_combo = ttk.Combobox(trigger_frame, textvariable=self.action_var, 
                                   values=["ON", "OFF", "TOG", "QRY", "CONDITIONAL"], 
                                   width=15, state="readonly")
        action_combo.grid(row=0, column=1, sticky='w', padx=5, pady=2)
        action_combo.bind('<<ComboboxSelected>>', lambda e: self._update_ui_state())
        
        # 条件触发设置
        self.conditional_frame = ttk.LabelFrame(trigger_frame, text='条件触发设置')
        self.conditional_frame.grid(row=1, column=0, columnspan=3, sticky='ew', padx=5, pady=5)
        
        ttk.Checkbutton(self.conditional_frame, text='NG时触发', 
                       variable=self.trigger_on_ng_var).grid(row=0, column=0, sticky='w', padx=5, pady=2)
        ttk.Checkbutton(self.conditional_frame, text='OK时触发', 
                       variable=self.trigger_on_ok_var).grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(self.conditional_frame, text='脉冲持续时间(ms):').grid(row=1, column=0, sticky='e', padx=5, pady=2)
        ttk.Spinbox(self.conditional_frame, from_=10, to=5000, increment=10,
                   textvariable=self.pulse_duration_var, width=10).grid(row=1, column=1, sticky='w', padx=5, pady=2)

        # 延时触发设置
        ttk.Checkbutton(self.conditional_frame, text='启用延时触发',
                       variable=self.enable_delay_var, command=self._update_ui_state).grid(row=2, column=0, sticky='w', padx=5, pady=2)

        ttk.Label(self.conditional_frame, text='触发延时(ms):').grid(row=3, column=0, sticky='e', padx=5, pady=2)
        self.delay_spinbox = ttk.Spinbox(self.conditional_frame, from_=0, to=10000, increment=50,
                   textvariable=self.trigger_delay_var, width=10)
        self.delay_spinbox.grid(row=3, column=1, sticky='w', padx=5, pady=2)
        
        # 说明文本
        info_frame = ttk.LabelFrame(main_frame, text='说明')
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        info_text = tk.Text(info_frame, height=4, wrap=tk.WORD, state=tk.DISABLED)
        info_text.pack(fill=tk.X, padx=5, pady=5)
        
        info_content = """条件触发模式说明：
• NG时触发：当YOLO检测到缺陷时，触发继电器输出脉冲信号
• OK时触发：当YOLO检测结果为合格时，触发继电器输出脉冲信号
• 脉冲持续时间：继电器开启后保持的时间，然后自动关闭
• 延时触发：检测到条件后延时指定时间再触发（用于补偿传送带位置偏移）
• 直接模式：立即执行指定动作（ON/OFF/TOG/QRY），不依赖检测结果"""
        
        info_text.config(state=tk.NORMAL)
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)

        # 按钮区域 - 确保在所有模式下都显示
        self.button_frame = ttk.LabelFrame(main_frame, text='操作')  # 使用LabelFrame使其更明显
        self.button_frame.pack(fill=tk.X, padx=5, pady=10)

        print("[DEBUG] 创建按钮区域")  # 调试信息

        # 保存按钮
        save_btn = ttk.Button(self.button_frame, text='保存配置', command=self._save_config)
        save_btn.pack(side=tk.LEFT, padx=(5, 10), pady=5)

        # 重置按钮
        reset_btn = ttk.Button(self.button_frame, text='重置', command=self._reset_config)
        reset_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)

        # 测试按钮
        test_btn = ttk.Button(self.button_frame, text='测试触发', command=self._test_trigger)
        test_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)

        # 状态标签
        self.status_label = ttk.Label(self.button_frame, text='配置就绪', foreground='green')
        self.status_label.pack(side=tk.RIGHT, padx=5, pady=5)

        # 初始化串口列表
        self._refresh_ports()
        
    def _refresh_ports(self):
        """刷新串口列表"""
        if list_ports is None:
            self.port_combo['values'] = ["COM1", "COM2", "COM3", "COM4", "COM5"]
            return
            
        try:
            ports = [port.device for port in list_ports.comports()]
            self.port_combo['values'] = ports
            if not self.port_var.get() and ports:
                self.port_var.set(ports[0])
        except Exception:
            self.port_combo['values'] = ["COM1", "COM2", "COM3", "COM4", "COM5"]
    
    def _update_ui_state(self):
        """根据动作模式更新UI状态"""
        is_conditional = self.action_var.get() == "CONDITIONAL"

        print(f"[DEBUG] 更新UI状态: 模式={self.action_var.get()}, 条件模式={is_conditional}")

        # 显示/隐藏条件触发设置
        if is_conditional:
            self.conditional_frame.grid()
            print("[DEBUG] 显示条件触发设置")
            # 根据延时触发开关状态更新延时设置的可用性
            enable_delay = self.enable_delay_var.get()
            if hasattr(self, 'delay_spinbox'):
                if enable_delay:
                    self.delay_spinbox.config(state='normal')
                else:
                    self.delay_spinbox.config(state='disabled')
        else:
            self.conditional_frame.grid_remove()
            print("[DEBUG] 隐藏条件触发设置")

        # 确保按钮区域始终显示
        if hasattr(self, 'button_frame'):
            self.button_frame.pack(fill=tk.X, padx=5, pady=10)
            print("[DEBUG] 确保按钮区域显示")
    
    def _on_param_change(self, *args):
        """参数变化回调（实时更新，不保存）"""
        try:
            # 安全获取数字参数，避免空字符串错误
            def safe_get_int(var, default=0):
                try:
                    value = var.get()
                    return int(value) if value != "" else default
                except (ValueError, tk.TclError):
                    return default

            # 更新参数字典
            self.params.update({
                "port": self.port_var.get(),
                "baud": safe_get_int(self.baud_var, 9600),
                "relay_index": safe_get_int(self.relay_index_var, 1),
                "action": self.action_var.get(),
                "trigger_on_ng": self.trigger_on_ng_var.get(),
                "trigger_on_ok": self.trigger_on_ok_var.get(),
                "pulse_duration": safe_get_int(self.pulse_duration_var, 100),
                "enable_delay": self.enable_delay_var.get(),
                "trigger_delay": safe_get_int(self.trigger_delay_var, 0),
            })

            # 清除状态信息
            self.status_label.config(text='', foreground='black')

        except Exception as e:
            print(f"参数更新失败: {e}")

    def _save_config(self):
        """保存配置"""
        try:
            # 更新参数
            self._on_param_change()

            # 通知参数变化并保存
            if self.on_change:
                self.on_change(self.params)

            # 同时保存到插件默认配置目录
            try:
                from plugins.io_tools.io_relay_plugin import IORelayPlugin
                plugin_instance = IORelayPlugin()
                plugin_instance.params = self.params.copy()
                plugin_instance.save_config()
            except Exception as save_error:
                print(f"保存插件默认配置失败: {save_error}")

            # 显示保存成功状态
            self.status_label.config(text='✓ 配置已保存', foreground='green')

            # 3秒后清除状态
            self.after(3000, lambda: self.status_label.config(text=''))

        except Exception as e:
            self.status_label.config(text=f'✗ 保存失败: {e}', foreground='red')
            print(f"保存配置失败: {e}")

    def _reset_config(self):
        """重置配置到默认值"""
        try:
            # 重置到默认参数
            default_params = {
                "port": "",
                "baud": 9600,
                "relay_index": 1,
                "action": "CONDITIONAL",
                "trigger_on_ng": True,
                "trigger_on_ok": False,
                "pulse_duration": 100,
                "enable_delay": False,
                "trigger_delay": 0,
            }

            # 更新UI变量
            self.port_var.set(default_params["port"])
            self.baud_var.set(default_params["baud"])
            self.relay_index_var.set(default_params["relay_index"])
            self.action_var.set(default_params["action"])
            self.trigger_on_ng_var.set(default_params["trigger_on_ng"])
            self.trigger_on_ok_var.set(default_params["trigger_on_ok"])
            self.pulse_duration_var.set(default_params["pulse_duration"])
            self.enable_delay_var.set(default_params["enable_delay"])
            self.trigger_delay_var.set(default_params["trigger_delay"])

            # 更新UI状态
            self._update_ui_state()

            # 显示重置状态
            self.status_label.config(text='✓ 已重置到默认值', foreground='blue')

            # 3秒后清除状态
            self.after(3000, lambda: self.status_label.config(text=''))

        except Exception as e:
            self.status_label.config(text=f'✗ 重置失败: {e}', foreground='red')
            print(f"重置配置失败: {e}")

    def _test_trigger(self):
        """测试触发功能"""
        try:
            # 导入继电器插件进行测试
            import sys
            from pathlib import Path
            sys.path.append(str(Path(__file__).parent.parent.parent))

            from plugins.io_tools.io_relay_plugin import IORelayPlugin

            # 创建插件实例
            relay_plugin = IORelayPlugin()

            # 使用当前参数初始化（测试脉冲模式）
            current_params = {
                "port": self.port_var.get(),
                "baud": self.baud_var.get(),
                "relay_index": self.relay_index_var.get(),
                "action": "CONDITIONAL",  # 使用条件触发模式进行测试
                "trigger_on_ng": True,    # 模拟NG触发
                "trigger_on_ok": False,
                "pulse_duration": self.pulse_duration_var.get(),
                "enable_delay": self.enable_delay_var.get(),
                "trigger_delay": self.trigger_delay_var.get(),
            }

            # 初始化插件
            relay_plugin.setup(current_params)

            # 执行测试
            import numpy as np
            test_img = np.zeros((100, 100, 3), dtype=np.uint8)

            # 模拟NG检测结果，触发继电器
            test_ctx = {
                'yolo_detect': {
                    'detections': [{'label': 'test_defect', 'confidence': 0.9}],
                    'total_count': 1,
                    'ng_count': 1,  # 模拟检测到NG
                    'ok_count': 0,
                    'total_area_mm2': 5.0,
                    'roi_used': True,
                    'roi_matched': True,
                    'roi_shape_ok': False,  # 模拟形状匹配NG，确保触发
                }
            }

            # 显示测试状态
            if self.enable_delay_var.get():
                delay_ms = self.trigger_delay_var.get()
                self.status_label.config(text=f'🔄 测试中...延时{delay_ms}ms', foreground='orange')
            else:
                self.status_label.config(text='🔄 测试中...立即触发', foreground='orange')

            # 执行测试
            result_img, result_ctx = relay_plugin.process(test_img, test_ctx)

            # 显示测试结果
            self.status_label.config(text='✓ 测试触发成功', foreground='green')

            # 5秒后清除状态
            self.after(5000, lambda: self.status_label.config(text=''))

        except Exception as e:
            self.status_label.config(text=f'✗ 测试失败: {e}', foreground='red')
            print(f"测试触发失败: {e}")


def create_config_dialog(master, params: Dict[str, Any], on_change: Callable):
    """创建配置对话框"""
    dialog = tk.Toplevel(master)
    dialog.title('继电器控制配置')
    dialog.geometry('600x650')  # 增加窗口高度，确保按钮区域可见
    dialog.resizable(True, True)  # 允许调整大小
    
    # 创建配置框架
    config_frame = IORelayConfigFrame(dialog, params, on_change)
    config_frame.pack(fill=tk.BOTH, expand=True)
    
    # 按钮框架
    btn_frame = ttk.Frame(dialog)
    btn_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Button(btn_frame, text='确定', command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
    ttk.Button(btn_frame, text='取消', command=dialog.destroy).pack(side=tk.RIGHT)
    
    # 设置对话框属性
    dialog.transient(master)
    dialog.grab_set()
    
    return dialog


if __name__ == '__main__':
    # 测试代码
    def on_change(params):
        print("参数变化:", params)
    
    root = tk.Tk()
    root.title('继电器配置测试')
    
    test_params = {
        "port": "COM3",
        "baud": 9600,
        "relay_index": 1,
        "action": "CONDITIONAL",
        "trigger_on_ng": True,
        "trigger_on_ok": False,
        "pulse_duration": 100,
    }
    
    frame = IORelayConfigFrame(root, test_params, on_change)
    frame.pack(fill=tk.BOTH, expand=True)
    
    root.mainloop()
