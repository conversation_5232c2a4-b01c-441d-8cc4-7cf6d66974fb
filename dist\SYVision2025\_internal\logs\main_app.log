2025-07-11 13:13:09,225 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:09,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,422 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:09,423 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,476 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,477 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:09,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,573 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,573 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,620 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:09,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,673 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:09,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,821 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:09,821 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,921 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:09,922 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:09,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:09,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,073 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,074 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,321 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,321 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,420 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,421 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,573 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,620 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,673 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,873 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,924 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:10,924 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:10,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:10,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,021 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,121 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,121 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,174 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,175 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,373 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,421 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,421 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,521 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,521 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,621 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,675 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,676 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,774 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,775 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,923 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:11,923 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:11,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:11,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,022 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,174 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,175 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,273 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,274 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,423 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,424 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,524 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,524 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,621 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,621 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,676 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,676 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,826 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,827 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:12,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:12,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:12,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,373 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,373 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,420 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,473 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,473 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,620 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,722 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,722 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,774 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:13,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:13,973 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:13,974 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,121 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,373 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,473 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,473 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:14,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:14,976 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:14,977 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,574 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,575 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,821 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,821 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:15,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:15,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:15,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,079 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,079 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,121 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,121 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,321 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,321 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,421 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,421 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,573 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,573 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,675 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,675 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,922 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:16,923 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:16,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:16,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,074 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,075 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,123 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,428 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,429 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,822 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,823 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:17,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:17,973 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:17,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,022 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,420 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,620 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:18,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:18,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:18,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,623 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:19,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:19,973 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:19,974 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,223 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,223 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,276 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,276 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,376 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,376 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,475 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,521 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,521 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,620 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,821 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:20,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:20,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:20,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,224 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,420 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,478 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,478 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,574 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,574 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,621 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,621 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,679 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,679 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:21,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:21,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:21,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,173 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,173 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,273 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,325 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,326 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,423 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,423 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,573 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,574 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,821 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,821 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,926 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:22,926 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:22,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:22,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,021 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,076 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,077 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,274 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,275 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,322 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,322 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,423 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,423 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,524 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,524 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,620 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,673 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,674 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,774 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,927 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:23,928 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:23,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:23,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,121 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,121 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,177 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,177 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,274 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,275 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,523 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,524 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,775 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,775 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,874 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,875 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:24,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:24,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:24,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,025 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,025 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,124 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,124 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,277 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,277 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,373 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,421 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,421 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,473 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,473 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,523 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,523 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,621 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,621 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,674 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,674 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,875 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,875 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,921 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:25,922 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:25,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:25,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,178 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,178 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,276 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,276 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,373 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,523 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,523 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,621 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,677 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,677 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,777 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,923 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:26,923 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:26,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:26,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,022 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,023 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,121 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,276 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,276 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,521 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,676 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,676 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,873 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,925 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:27,925 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:27,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:27,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,021 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,176 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,176 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,278 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,279 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,373 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,373 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,421 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,422 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,524 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,524 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,677 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,677 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,777 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,777 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,922 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:28,922 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:28,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:28,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,177 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,177 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,274 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,274 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,373 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,625 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,625 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,723 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,724 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,775 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,822 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,822 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:29,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:29,975 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:29,976 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,125 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,125 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,221 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,221 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,276 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,276 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,377 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,377 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,623 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,623 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,723 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,724 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,876 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,877 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:30,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:30,974 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:30,974 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,125 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,126 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,223 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,323 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,323 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,376 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,376 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,478 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,478 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,573 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,574 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:31,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:31,976 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:31,976 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,073 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,125 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,125 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,226 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,227 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,321 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,376 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,376 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,574 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,574 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,873 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:32,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:32,973 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:32,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,073 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,125 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,125 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,173 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,174 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,273 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,375 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,475 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,475 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,526 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,526 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,625 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,625 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,673 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,778 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,779 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:33,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:33,975 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:33,976 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,473 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,474 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,574 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,574 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,723 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,723 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,823 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,823 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:34,921 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:34,979 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:34,979 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,075 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,075 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,221 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,221 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,322 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,322 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,376 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,376 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,476 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,476 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,621 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,621 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,722 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,823 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,824 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:35,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:35,976 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:35,976 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,075 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,076 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,118 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,223 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,223 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,324 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,324 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,420 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,474 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,575 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,575 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,726 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,726 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:36,921 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:36,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:36,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,074 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,075 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,123 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,224 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,321 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,321 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,376 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,377 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,476 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,477 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,574 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,574 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,821 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,821 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,876 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,876 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:37,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:37,975 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:37,976 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,021 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,021 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,224 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,322 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,323 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,474 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,575 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,576 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,826 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,827 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:38,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:38,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:38,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,077 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,078 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,118 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,221 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,221 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,678 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,679 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,775 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,775 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,824 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,824 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,924 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:39,925 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:39,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:39,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,022 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,076 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,076 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,177 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,177 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,273 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,324 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,324 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,424 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,424 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,521 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,522 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,575 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,576 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,676 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,676 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,927 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:40,927 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:40,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:40,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,023 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,023 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,073 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,174 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,175 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,274 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,275 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,326 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,327 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,424 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,424 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,577 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,578 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,676 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,676 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,821 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,822 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,923 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:41,923 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:41,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:41,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,022 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,176 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,177 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,276 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,276 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,421 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,422 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,526 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,526 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,623 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,673 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:42,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:42,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:42,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,025 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,279 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,280 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,521 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,621 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,621 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,774 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,875 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,875 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,923 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:43,923 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:43,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:43,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,178 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,178 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,521 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,521 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,775 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,775 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:44,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:44,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:44,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,221 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,222 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,318 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,426 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,427 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,722 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,722 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,795 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,795 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,873 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:45,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:45,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:45,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,073 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,074 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,125 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,125 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,223 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,223 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,321 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,321 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,379 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,379 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,474 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,573 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,724 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,724 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,822 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,822 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,877 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,877 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:46,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:46,977 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:46,977 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,072 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,124 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,124 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,223 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,223 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,375 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,376 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,418 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,418 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,573 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,625 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,626 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,873 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:47,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:47,975 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:47,975 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,073 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,074 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,124 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,124 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,173 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,173 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,222 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,223 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,270 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,321 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,322 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,379 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,379 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,474 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,574 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,574 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,724 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,724 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,818 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:48,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:48,976 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:48,977 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,018 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,073 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,073 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,224 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,322 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,322 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,375 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,476 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,476 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,574 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,575 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,673 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,823 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,823 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:49,921 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:49,977 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:49,977 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,074 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,074 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,121 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,121 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,224 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,421 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,421 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,573 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,573 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,675 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,675 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,828 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,829 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,877 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,878 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:50,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:50,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:50,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,273 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,274 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,422 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,423 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,523 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,523 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,677 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,677 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,775 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,775 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,826 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,827 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,921 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:51,922 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:51,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:51,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,021 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,073 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,073 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,174 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,174 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,218 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,320 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,321 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,370 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,423 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,423 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,483 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,483 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,518 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,518 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,573 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,573 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,618 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,676 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,676 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,820 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,924 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:52,924 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:52,976 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:52,976 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,020 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,020 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,176 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,176 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,274 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,274 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,324 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,324 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,424 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,424 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,521 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,521 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,620 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,620 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,677 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,677 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,777 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,872 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:53,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:53,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:53,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,178 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,179 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,218 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,218 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,274 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,373 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,524 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,525 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,623 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,775 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,775 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,818 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,818 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,918 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:54,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:54,970 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:54,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,022 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,070 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,070 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,123 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,220 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,272 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,375 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,473 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,474 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,525 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,525 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,623 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,722 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,818 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,818 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,878 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,878 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:55,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:55,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:55,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,123 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,172 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,475 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,475 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,521 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,521 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,623 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,623 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,722 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,723 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,778 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,778 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,874 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,875 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:56,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:56,974 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:56,974 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,121 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,223 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,223 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,272 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,376 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,376 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,418 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,475 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,475 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,876 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,876 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:57,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:57,973 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:57,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,026 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,026 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,070 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,170 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,170 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,223 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,278 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,278 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,376 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,376 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,474 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,572 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,572 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,723 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,723 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,874 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,874 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:58,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:58,974 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:58,975 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,124 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,124 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,221 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,221 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,418 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,418 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,475 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,518 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,670 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,771 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,820 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,920 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:13:59,921 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:13:59,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:13:59,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,073 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,074 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,119 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,119 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,172 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,173 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,224 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,224 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,320 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,372 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,420 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,420 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,576 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,577 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,673 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,673 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,771 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,772 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,822 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,822 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,870 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,921 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:00,922 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:00,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:00,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,018 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,075 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,076 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,118 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,118 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,190 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,190 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,324 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,324 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,371 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,371 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,424 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,424 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,577 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,577 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,672 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,718 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,818 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,873 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,874 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,924 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:01,924 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:01,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:01,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,273 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,273 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,375 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,519 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,519 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,619 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,619 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,829 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,829 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,872 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:02,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:02,974 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:02,975 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,026 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,027 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,070 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,070 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,123 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,170 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,221 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,275 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,275 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,318 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,318 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,474 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,475 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,518 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,518 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,623 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,623 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,672 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,721 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,721 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,875 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,875 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,918 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:03,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:03,973 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:03,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,019 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,019 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,123 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,124 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,170 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,218 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,418 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,418 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,473 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,473 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,520 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,520 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,720 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,871 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,871 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:04,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:04,975 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:04,976 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,023 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,072 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,220 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,221 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,278 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,278 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,473 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,473 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,523 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,523 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,624 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,624 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,720 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,776 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,776 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,818 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,818 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,875 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,875 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:05,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:05,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:05,973 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,123 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,218 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,276 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,277 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,374 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,374 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,470 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,524 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,524 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,671 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,671 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,718 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,718 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,774 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,818 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,818 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,875 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,875 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:06,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:06,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:06,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,070 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,118 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,118 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,170 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,219 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,219 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,276 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,276 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,318 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,377 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,377 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,419 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,472 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,472 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,523 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,523 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,570 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,622 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,622 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,670 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,670 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,772 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,773 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,875 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,876 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:07,919 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:07,972 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:07,972 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,022 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,022 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,122 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,122 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,173 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,174 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,218 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,218 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,275 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,275 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,318 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,318 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,375 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,375 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,424 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,425 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,471 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,471 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,523 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,523 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,571 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,571 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,618 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,618 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,676 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,676 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,719 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,719 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,773 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,774 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,819 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,819 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,873 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,873 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,919 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:08,920 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:08,971 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:08,971 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,024 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:09,024 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,071 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:09,071 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,120 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:09,120 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,171 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:09,171 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,218 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:09,218 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,271 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:09,271 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,319 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:09,319 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,372 - MainWindow.on_frame - DEBUG - Frame received from cam01, is None? False
2025-07-11 13:14:09,373 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
2025-07-11 13:14:09,418 - MainWindow.on_frame - DEBUG - Frame received from cam02, is None? False
2025-07-11 13:14:09,419 - MainWindow.on_frame - DEBUG - process_allowed=False, _pl_mode=stop, _pl_single=False
