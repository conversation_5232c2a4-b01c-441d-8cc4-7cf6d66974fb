#!/usr/bin/env python3
"""
独立的相机设置程序
用于编译成独立的exe文件，避免与主程序的Python版本冲突
"""

import os
import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
if BASE_DIR not in sys.path:
    sys.path.insert(0, BASE_DIR)

def main():
    """主函数"""
    try:
        # 设置环境变量，确保使用正确的工作目录
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller环境
            os.chdir(os.path.dirname(sys.executable))
        
        # 导入相机管理工具
        from CommTools.camera_manager_tk import CameraManagerTk
        
        # 创建主窗口
        root = CameraManagerTk()
        
        # 设置窗口属性
        root.title("海康相机设置工具 (独立版)")
        
        # 运行主循环
        root.mainloop()
        
    except Exception as e:
        # 如果出错，显示错误信息
        import tkinter.messagebox as messagebox
        messagebox.showerror("启动失败", f"相机设置工具启动失败:\n{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
