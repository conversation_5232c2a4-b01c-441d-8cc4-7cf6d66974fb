"""Tkinter UI panel for MorphologyProcessor.
Allows loading an image, adjusting parameters, and previewing result."""

from __future__ import annotations
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import numpy as np
import yaml

# 兼容直接脚本运行
import sys, pathlib, os
ROOT_DIR = pathlib.Path(__file__).resolve().parents[3]  # SY_VISION2025 目录
if str(ROOT_DIR) not in sys.path:
    sys.path.insert(0, str(ROOT_DIR))

# --------- 配置目录 ---------
CONFIG_DIR = ROOT_DIR / 'configs' / 'morphology'
CONFIG_DIR.mkdir(parents=True, exist_ok=True)

# 现在可绝对导入
from plugins.core.image_process.morphology import MorphologyProcessor


class MorphologyFrame(tk.Frame):
    """UI wrapper for MorphologyProcessor"""

    OPS_MAP = {
        "腐蚀": "erode",
        "膨胀": "dilate",
        "开运算": "open",
        "闭运算": "close",
        "梯度": "gradient",
        "顶帽": "tophat",
        "黑帽": "blackhat",
        "中值": "median",
        "高斯": "gauss",
    }

    from typing import Optional
    def __init__(self, master: tk.Widget | None = None, preview: Optional[dict]=None):
        super().__init__(master)
        # 设定默认窗口大小
        if isinstance(master, (tk.Tk, tk.Toplevel)):
            master.geometry('1200x600')
        self.proc = MorphologyProcessor()
        # 当前算子序列（英文代码）
        self.ops_seq: list[str] = [self.proc.op] if self.proc.op else []
        self.img_src: np.ndarray | None = preview['img'] if preview else None
        # DEBUG: log preview image reception
        if self.img_src is not None:
            print(f"[DEBUG] MorphologyFrame received preview with shape: {self.img_src.shape}")
        else:
            print("[DEBUG] MorphologyFrame received no preview image")
        self.preview_max = 450 # 最大显示尺寸
        self._build_ui()
        if self.img_src is not None:
            self._show_image(self.canvas_src, self.img_src, is_src=True)

    # ---------------- UI -----------------
    def _build_ui(self):
        param_frm = tk.LabelFrame(self, text="参数", padx=4, pady=4)
        param_frm.grid(row=0, column=0, sticky="nsw")

        # ------------- 算子选择与序列 -------------
        ttk.Label(param_frm, text="选择算子").grid(row=0, column=0, sticky="e")
        default_cn = next((k for k, v in self.OPS_MAP.items() if v == self.proc.op), list(self.OPS_MAP.keys())[0])
        self.sel_op_var = tk.StringVar(value=default_cn)
        ttk.OptionMenu(param_frm, self.sel_op_var, default_cn, *self.OPS_MAP.keys()).grid(row=0, column=1)

        # 序列 Listbox
        seq_frm = tk.LabelFrame(param_frm, text="当前序列", padx=2, pady=2)
        seq_frm.grid(row=1, column=0, columnspan=3, pady=(4,2), sticky='we')
        self.list_ops = tk.Listbox(seq_frm, height=5)
        self.list_ops.grid(row=0, column=0, rowspan=4, sticky='ns')
        # 控制按钮
        ttk.Button(seq_frm, text="添加→", command=self._add_op).grid(row=0, column=1, sticky='we', padx=2)
        ttk.Button(seq_frm, text="删除", command=self._del_op).grid(row=1, column=1, sticky='we', padx=2)
        ttk.Button(seq_frm, text="上移", command=lambda: self._move_op(-1)).grid(row=2, column=1, sticky='we', padx=2)
        ttk.Button(seq_frm, text="下移", command=lambda: self._move_op(1)).grid(row=3, column=1, sticky='we', padx=2)
        self._refresh_ops_listbox()

        # -------- 形态参数 --------
        ttk.Label(param_frm, text="核尺寸").grid(row=2, column=0, sticky="e")
        self.ks_var = tk.IntVar(value=self.proc.kernel_size)
        ttk.Spinbox(param_frm, from_=1, to=15, textvariable=self.ks_var, width=5,
                    command=self._auto_run).grid(row=2, column=1)

        ttk.Label(param_frm, text="迭代").grid(row=3, column=0, sticky="e")
        self.it_var = tk.IntVar(value=self.proc.iter)
        ttk.Spinbox(param_frm, from_=1, to=10, textvariable=self.it_var, width=5,
                    command=self._auto_run).grid(row=3, column=1)

        ttk.Label(param_frm, text="阈值").grid(row=4, column=0, sticky="e")
        self.th_val = tk.IntVar(value=self.proc.thresh_val)
        ttk.Spinbox(param_frm, from_=0, to=255, textvariable=self.th_val, width=5,
                    command=self._auto_run).grid(row=4, column=1)

        self.inv_var = tk.BooleanVar(value=self.proc.thresh_type.startswith("inv"))
        ttk.Checkbutton(param_frm, text="反向", variable=self.inv_var, command=self._auto_run).grid(row=4, column=2)

        ttk.Button(param_frm, text="载入图片", command=self._load_image).grid(row=5, column=0, pady=4)
        ttk.Button(param_frm, text="执行", command=self._run).grid(row=5, column=1, pady=4)

        # 配置保存/载入
        ttk.Button(param_frm, text="保存配置", command=self._save_cfg).grid(row=7, column=0, pady=4)
        ttk.Button(param_frm, text="加载配置", command=self._load_cfg).grid(row=7, column=1, pady=4)
        ttk.Button(param_frm, text="保存结果", command=self._save_result).grid(row=9, column=0, columnspan=2, sticky='we', pady=(0,4))

        # zoom slider
        self.zoom_var = tk.DoubleVar(value=1.0)
        ttk.Label(param_frm, text='缩放').grid(row=6, column=0, sticky='e')
        ttk.Scale(param_frm, from_=0.25, to=2.0, orient='horizontal',
                  variable=self.zoom_var, command=lambda e: self._refresh_images(), length=120
                  ).grid(row=6, column=1, columnspan=2, pady=4, sticky='w')

        # ROI 模式切换
        self.roi_mode = tk.StringVar(value='rect')
        ttk.Label(param_frm, text='ROI模式').grid(row=8, column=0, sticky='e')
        ttk.OptionMenu(param_frm, self.roi_mode, 'rect', 'rect', 'poly').grid(row=8, column=1, columnspan=2, sticky='ew', pady=4)

        # Canvases to show images
        self.canvas_src = tk.Canvas(self, width=self.preview_max, height=self.preview_max, bg='#333')
        self.canvas_src.grid(row=0, column=1, padx=6)
        self.canvas_res = tk.Canvas(self, width=self.preview_max, height=self.preview_max, bg='#333')
        self.canvas_res.grid(row=0, column=2, padx=6)

        # 绑定滚轮缩放
        for c in (self.canvas_src, self.canvas_res):
            c.bind('<MouseWheel>', self._on_wheel)          # Windows
            c.bind('<Button-4>', lambda e: self._on_wheel(e, delta=120))  # Linux scroll up
            c.bind('<Button-5>', lambda e: self._on_wheel(e, delta=-120)) # Linux scroll down

        # status line
        self.info_var = tk.StringVar(value='坐标:(-, -)  值:-,-,-  分辨率:-x-')
        tk.Label(self, textvariable=self.info_var).grid(row=1, column=1, columnspan=2, sticky='w', pady=(4,0))

        # ROI 选择
        self.roi_rect = None  # (x,y,w,h) in image coords
        self._roi_canvas_id = None
        self.poly_pts = []  # list of (x_img,y_img)
        self._poly_canvas_id = None
        self.canvas_src.bind('<ButtonPress-1>', self._roi_start)
        self.canvas_src.bind('<B1-Motion>', self._roi_drag)
        self.canvas_src.bind('<ButtonRelease-1>', self._roi_end)
        self.canvas_src.bind('<Double-Button-1>', self._poly_finish)

    # --------------- handlers -------------
    def _load_image(self):
        path = filedialog.askopenfilename(filetypes=[("Images", "*.png;*.jpg;*.bmp;*.tif")])
        if not path:
            return
        img = cv2.imdecode(np.fromfile(path, dtype=np.uint8), cv2.IMREAD_COLOR)
        if img is None:
            messagebox.showerror("错误", "无法读取图像")
            return
        self.img_src = img
        self._show_image(self.canvas_src, img, is_src=True)

        # ---------- 算子序列相关 ----------
    def _refresh_ops_listbox(self):
        self.list_ops.delete(0, tk.END)
        rev_map = {v: k for k, v in self.OPS_MAP.items()}
        for op in self.ops_seq:
            self.list_ops.insert(tk.END, rev_map.get(op, op))

    def _update_proc_ops(self, autorun: bool = True):
        """同步处理器算子序列，可选择是否立即运行，避免递归调用"""
        self.proc.op = '+'.join(self.ops_seq)
        if autorun:
            self._auto_run()

    def _add_op(self):
        op_en = self.OPS_MAP[self.sel_op_var.get()]
        self.ops_seq.append(op_en)
        self._refresh_ops_listbox()
        self._update_proc_ops(autorun=False)

    def _del_op(self):
        sel = self.list_ops.curselection()
        if not sel:
            return
        idx = sel[0]
        if 0 <= idx < len(self.ops_seq):
            self.ops_seq.pop(idx)
            self._refresh_ops_listbox()
            self._update_proc_ops(autorun=False)

    def _move_op(self, offset:int):
        sel = self.list_ops.curselection()
        if not sel:
            return
        idx = sel[0]
        new_idx = idx + offset
        if 0 <= new_idx < len(self.ops_seq):
            self.ops_seq[idx], self.ops_seq[new_idx] = self.ops_seq[new_idx], self.ops_seq[idx]
            self._refresh_ops_listbox()
            self.list_ops.selection_set(new_idx)
            self._update_proc_ops(autorun=False)


    # -----------------------------------
    def _run(self):

        # 若序列为空，默认使用当前下拉选择的算子一次
        if not self.ops_seq:
            sel_cn = self.sel_op_var.get()
            self.ops_seq = [self.OPS_MAP.get(sel_cn, 'open')]
            self._refresh_ops_listbox()
        if self.img_src is None:
            messagebox.showinfo("提示", "请先加载图像")
            return
        # update processor params (op 序列已在 _update_proc_ops 设置)
        self.proc.op = '+'.join(self.ops_seq)
        self.proc.kernel_size = self.ks_var.get()
        self.proc.iter = self.it_var.get()
        self.proc.thresh_val = self.th_val.get()
        self.proc.thresh_type = "inv" if self.inv_var.get() else "binary"

        # apply roi if any
        img_in = self.img_src
        if self.roi_mode.get() == 'poly' and self.poly_pts:
            mask = np.zeros(img_in.shape[:2], np.uint8)
            pts = np.array([self.poly_pts], np.int32)
            cv2.fillPoly(mask, pts, 255)
            proc_in = cv2.bitwise_and(img_in, img_in, mask=mask)
            out = self.proc.process(proc_in)["output"]
            out = cv2.bitwise_and(out, out, mask=mask)

        elif self.roi_rect:
            x, y, w, h = self.roi_rect
            # ROI 尺寸合法性检查，防止裁剪得到空图
            if w <= 0 or h <= 0:
                messagebox.showwarning("警告", "ROI 尺寸无效，请重新选择")
                return
            img_crop = img_in[y:y+h, x:x+w]
            if img_crop.size == 0:
                messagebox.showwarning("警告", "ROI 裁剪结果为空，请检查坐标")
                return
            out = self.proc.process(img_crop)["output"]
        else:
            out = self.proc.process(img_in)["output"]
        self.img_out = out

        # 统计面积
        white = int(np.sum(out > 0))
        self.info_var.set(self.info_var.get().split('|')[0] + f' | 面积:{white}')
        # 保存最后一次运行信息，供 YAML 导出
        self.last_info = {
            'area': white,
            'ops_seq': self.ops_seq.copy(),
            'kernel_size': self.ks_var.get(),
            'iter': self.it_var.get(),
        }
        # if ROI, show crop, else full
        self._show_image(self.canvas_res, cv2.cvtColor(out, cv2.COLOR_GRAY2BGR))
        self._show_image(self.canvas_src, self.img_src, is_src=True)

    # 自动执行(参数变化时)
    def _auto_run(self):
        if self.img_src is not None:
            self._run()

    def _refresh_images(self):
        if self.img_src is not None:
            self._show_image(self.canvas_src, self.img_src, is_src=True)
            if hasattr(self.canvas_res, 'image'):
                # 重新运行以更新结果显示
                self._run()

    # 滚轮事件: delta 正数放大, 负数缩小
    def _on_wheel(self, event, *, delta=None):
        d = delta if delta is not None else event.delta
        step = 0.1 if d > 0 else -0.1
        new_val = max(0.25, min(2.0, self.zoom_var.get() + step))
        self.zoom_var.set(round(new_val, 2))
        self._refresh_images()

    # ---------------- ROI handlers -----------------
    def _roi_start(self, event):
        if self.roi_mode.get() == 'rect':
            self._roi_start_pt = (event.x, event.y)
            if self._roi_canvas_id:
                self.canvas_src.delete(self._roi_canvas_id)
                self._roi_canvas_id = None
        else:  # poly
            zoom_display = getattr(self, '_src_zoom', self.zoom_var.get())
            self.poly_pts.append((int(event.x/zoom_display), int(event.y/zoom_display)))
            self._refresh_images()

    def _roi_drag(self, event):
        if self.roi_mode.get() == 'rect':
            if not hasattr(self, '_roi_start_pt'):
                return
            x0, y0 = self._roi_start_pt
            if self._roi_canvas_id:
                self.canvas_src.coords(self._roi_canvas_id, x0, y0, event.x, event.y)
            else:
                self._roi_canvas_id = self.canvas_src.create_rectangle(
                    x0, y0, event.x, event.y,
                    outline='lime', width=2, dash=(3, 2)
                )
            self.canvas_src.update_idletasks()

    def _roi_end(self, event):
        if self.roi_mode.get() == 'rect':
            if not hasattr(self, '_roi_start_pt'):
                return
            x0, y0 = self._roi_start_pt
            x1, y1 = event.x, event.y
            del self._roi_start_pt
            # convert to image coords
            zoom_display = getattr(self, '_src_zoom', self.zoom_var.get())
            x_img0, y_img0 = int(x0 / zoom_display), int(y0 / zoom_display)
            x_img1, y_img1 = int(x1 / zoom_display), int(y1 / zoom_display)
            x_img0, x_img1 = sorted((x_img0, x_img1))
            y_img0, y_img1 = sorted((y_img0, y_img1))
            self.roi_rect = (x_img0, y_img0, x_img1 - x_img0, y_img1 - y_img0)
            self.poly_pts.clear()
            self._run()

    def _poly_finish(self, event):
        if self.roi_mode.get() == 'poly' and len(self.poly_pts) >2:
            # finish polygon on double click
            self.roi_rect = None
            self._run()

    # ------------- util ------------------
    def _show_image(self, canvas: tk.Canvas, img_bgr: np.ndarray, *, is_src=False):
        if img_bgr is None:
            return
        # 对源图像始终使用最新的 zoom_var, 结果图像沿用源图像的实际显示比例(_src_zoom)
        if is_src:
            zoom_display = self.zoom_var.get()
        else:
            zoom_display = getattr(self, '_src_zoom', self.zoom_var.get())
        h, w = img_bgr.shape[:2]
        disp_w, disp_h = int(w * zoom_display), int(h * zoom_display)
        if max(disp_w, disp_h) > self.preview_max:
            scale = self.preview_max / max(disp_w, disp_h)
            disp_w, disp_h = int(disp_w * scale), int(disp_h * scale)
            zoom_display *= scale
        img_disp = cv2.resize(img_bgr, (disp_w, disp_h)) if zoom_display != 1 else img_bgr.copy()

        img_rgb = cv2.cvtColor(img_disp, cv2.COLOR_BGR2RGB)
        pil = Image.fromarray(img_rgb)
        photo = ImageTk.PhotoImage(pil)
        canvas.delete('all')
        canvas.create_image(0, 0, anchor='nw', image=photo)
        canvas.image = photo  # keep ref

        if is_src:
            # 记录当前源图显示的缩放比例，供 ROI 坐标、鼠标信息等转换使用
            self._src_zoom = zoom_display
            # bind motion to show pixel info
            canvas.bind('<Motion>', lambda e, z=zoom_display: self._update_info(e, z))
            self.info_var.set(f'坐标:(-, -)  值:-,-,-  分辨率:{w}x{h}')
        # draw ROI rectangle on source canvas after refresh
        if is_src and self.roi_rect:
            x, y, w, h = self.roi_rect
            x0, y0 = x * zoom_display, y * zoom_display
            x1, y1 = (x + w) * zoom_display, (y + h) * zoom_display
            canvas.create_rectangle(x0, y0, x1, y1, outline='lime', width=2, dash=(3, 2))
        # draw polygon
        if is_src and self.poly_pts:
            zoom_display = getattr(self, '_src_zoom', self.zoom_var.get())
            disp_pts = [(px*zoom_display, py*zoom_display) for px,py in self.poly_pts]
            if len(disp_pts) >1:
                canvas.create_line(*sum(disp_pts, ()), fill='lime', width=2)
            for px,py in disp_pts:
                canvas.create_oval(px-3,py-3,px+3,py+3, fill='lime')

    def _update_info(self, event, zoom_display):
        if self.img_src is None:
            return
        x = int(event.x / zoom_display)
        y = int(event.y / zoom_display)
        h, w = self.img_src.shape[:2]
        if 0 <= x < w and 0 <= y < h:
            b, g, r = self.img_src[y, x]
            self.info_var.set(f'坐标:({x}, {y})  值:{r},{g},{b}  分辨率:{w}x{h}')

    # ---------------- 参数保存/加载 -----------------
    def get_params(self) -> dict:
        data = {
            'ops_seq': self.ops_seq,
            'kernel_size': self.ks_var.get(),
            'iter': self.it_var.get(),
            'thresh_val': self.th_val.get(),
            'thresh_inv': self.inv_var.get(),
            'roi': self.roi_rect,
        }
        # 附加检测信息，与模板匹配保持一致
        if hasattr(self, 'last_info'):
            data['info'] = self.last_info
        return data

    def set_params(self, params: dict):
        """恢复参数到 UI 并刷新"""
        self.ops_seq = params.get('ops_seq', self.ops_seq)
        # 更新序列列表框
        self._refresh_ops_listbox()
        # 其他标量参数
        self.ks_var.set(params.get('kernel_size', self.ks_var.get()))
        self.it_var.set(params.get('iter', self.it_var.get()))
        self.th_val.set(params.get('thresh_val', self.th_val.get()))
        self.inv_var.set(params.get('thresh_inv', self.inv_var.get()))
        self.roi_rect = params.get('roi', self.roi_rect)
        # 同步处理器并刷新
        self._update_proc_ops(autorun=False)
        self._refresh_images()


    # ---------------- 配置保存/加载 -----------------
    def _save_cfg(self):
        init_file = CONFIG_DIR / 'default.yml'
        path = filedialog.asksaveasfilename(
            initialdir=CONFIG_DIR,
            initialfile=init_file.name,
            defaultextension='.yml',
            filetypes=[('YAML','*.yml;*.yaml')]
        )
        if not path:
            return
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(self.get_params(), f, allow_unicode=True)

    def _load_cfg(self):
        path = filedialog.askopenfilename(
            initialdir=CONFIG_DIR,
            filetypes=[('YAML','*.yml;*.yaml')]
        )
        if not path:
            return
        try:
            with open(path, 'r', encoding='utf-8') as f:
                params = yaml.safe_load(f)
            self.set_params(params or {})
        except Exception as e:
            messagebox.showerror('错误', f'配置加载失败: {e}')

    def get_processor(self):
        return self.proc

    # ---------------- 保存结果 -----------------
    def _save_result(self):
        if getattr(self,'img_out', None) is None:
            messagebox.showinfo('提示','请先执行处理');return
        path = filedialog.asksaveasfilename(defaultextension='.png', filetypes=[('PNG','*.png'),('JPG','*.jpg')])
        if not path: return
        img = self.img_out
        if img.ndim==2:
            img_bgr=cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        else:
            img_bgr=img
        ext = path.split('.')[-1].lower()
        ext = '.jpg' if ext=='jpg' else '.png'
        ok, buf = cv2.imencode(ext, img_bgr)
        if ok:
            buf.tofile(path)
        else:
            messagebox.showerror('错误','保存失败')


if __name__ == "__main__":
    root = tk.Tk()
    root.title("形态学测试")
    MorphologyFrame(root).pack()
    root.mainloop()
