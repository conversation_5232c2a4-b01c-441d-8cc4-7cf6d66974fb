# -*- coding: utf-8 -*-
"""位置修正工具UI界面

提供交互式位置修正功能：
1. 透视变换修正
2. 旋转修正  
3. 平移修正
4. 缩放修正
5. 组合修正

操作流程：
1. 加载图像
2. 选择修正模式
3. 调整参数
4. 实时预览修正效果
5. 保存配置
"""
from __future__ import annotations

import pathlib, sys
from typing import Optional, Dict, Any, Callable
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

import numpy as np
import yaml
from pathlib import Path

PREVIEW_SIZE = 550  # 固定显示尺寸
CFG_DIR = Path(__file__).resolve().parents[3] / 'configs' / 'position_correction'
CFG_DIR.mkdir(parents=True, exist_ok=True)

import cv2
from PIL import Image, ImageTk

# --- 导入算法 ---
try:
    from plugins.geometry_tools.position_correction_plugin import PositionCorrectionPlugin
except ImportError:
    _proj_root = pathlib.Path(__file__).resolve().parents[3]
    if str(_proj_root) not in sys.path:
        sys.path.insert(0, str(_proj_root))
    from plugins.geometry_tools.position_correction_plugin import PositionCorrectionPlugin

try:
    from ._recipe_sync import update_mm_in_recipe
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from plugins.ui.geometry_tools._recipe_sync import update_mm_in_recipe
    except ImportError:
        # 如果都失败了，定义一个空函数
        def update_mm_in_recipe(*args, **kwargs):
            pass


class PositionCorrectionUI(tk.Toplevel):
    def __init__(self, master=None, *, img: Optional[np.ndarray] = None, 
                 params: Optional[Dict[str, Any]] = None, 
                 on_change: Optional[Callable] = None):
        super().__init__(master)
        
        self.title("位置修正工具")
        self.geometry("1200x800")
        
        # 数据
        self.img_src = img
        self.img_corrected = None
        self.params = params or {}
        self.on_change = on_change
        
        # 插件实例
        self.plugin = PositionCorrectionPlugin()
        if self.params:
            self.plugin.params.update(self.params)
        
        # UI变量
        self.param_vars = {}
        
        # 构建UI
        self._build_ui()
        
        # 如果有图像，延迟显示（等待UI完全初始化）
        if self.img_src is not None:
            self.after(100, self._update_preview)

    def _build_ui(self):
        """构建UI界面"""
        # 主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧参数面板
        left_frame = ttk.Frame(main_frame, width=300)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        left_frame.pack_propagate(False)
        
        # 右侧图像显示区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self._build_param_panel(left_frame)
        self._build_image_panel(right_frame)

    def _build_param_panel(self, parent):
        """构建参数面板"""
        # 标题
        title_label = ttk.Label(parent, text="位置修正参数", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 创建滚动框架
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 修正模式选择
        mode_frame = ttk.LabelFrame(scrollable_frame, text="修正模式", padding=5)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.param_vars["correction_mode"] = tk.StringVar(value=self.plugin.params["correction_mode"])
        modes = [("自动", "auto"), ("手动", "manual"), ("透视", "perspective"), 
                ("旋转", "rotation"), ("平移", "translation"), ("缩放", "scale"), ("组合", "combined")]
        
        for i, (text, value) in enumerate(modes):
            rb = ttk.Radiobutton(mode_frame, text=text, variable=self.param_vars["correction_mode"], 
                               value=value, command=self._on_param_change)
            rb.grid(row=i//2, column=i%2, sticky=tk.W, padx=5, pady=2)
        
        # 透视修正参数
        perspective_frame = ttk.LabelFrame(scrollable_frame, text="透视修正", padding=5)
        perspective_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.param_vars["enable_perspective"] = tk.BooleanVar(value=self.plugin.params["enable_perspective"])
        ttk.Checkbutton(perspective_frame, text="启用透视修正", 
                       variable=self.param_vars["enable_perspective"], 
                       command=self._on_param_change).pack(anchor=tk.W)
        
        # 目标尺寸
        size_frame = ttk.Frame(perspective_frame)
        size_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(size_frame, text="目标宽度:").grid(row=0, column=0, sticky=tk.W)
        self.param_vars["dst_width"] = tk.IntVar(value=self.plugin.params["dst_width"])
        ttk.Entry(size_frame, textvariable=self.param_vars["dst_width"], width=8).grid(row=0, column=1, padx=5)
        
        ttk.Label(size_frame, text="目标高度:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.param_vars["dst_height"] = tk.IntVar(value=self.plugin.params["dst_height"])
        ttk.Entry(size_frame, textvariable=self.param_vars["dst_height"], width=8).grid(row=0, column=3, padx=5)
        
        # 旋转修正参数
        rotation_frame = ttk.LabelFrame(scrollable_frame, text="旋转修正", padding=5)
        rotation_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.param_vars["enable_rotation"] = tk.BooleanVar(value=self.plugin.params["enable_rotation"])
        ttk.Checkbutton(rotation_frame, text="启用旋转修正", 
                       variable=self.param_vars["enable_rotation"], 
                       command=self._on_param_change).pack(anchor=tk.W)
        
        self.param_vars["auto_detect_angle"] = tk.BooleanVar(value=self.plugin.params["auto_detect_angle"])
        ttk.Checkbutton(rotation_frame, text="自动检测角度", 
                       variable=self.param_vars["auto_detect_angle"], 
                       command=self._on_param_change).pack(anchor=tk.W)
        
        # 旋转角度
        angle_frame = ttk.Frame(rotation_frame)
        angle_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(angle_frame, text="旋转角度:").pack(side=tk.LEFT)
        self.param_vars["rotation_angle"] = tk.DoubleVar(value=self.plugin.params["rotation_angle"])
        angle_scale = ttk.Scale(angle_frame, from_=-180, to=180, 
                               variable=self.param_vars["rotation_angle"], 
                               orient=tk.HORIZONTAL, command=self._on_scale_change)
        angle_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        angle_entry = ttk.Entry(angle_frame, textvariable=self.param_vars["rotation_angle"], width=8)
        angle_entry.pack(side=tk.RIGHT)
        angle_entry.bind('<Return>', lambda e: self._on_param_change())
        
        # 旋转中心
        center_frame = ttk.Frame(rotation_frame)
        center_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(center_frame, text="中心X:").grid(row=0, column=0, sticky=tk.W)
        self.param_vars["rotation_center_x"] = tk.DoubleVar(value=self.plugin.params["rotation_center_x"])
        ttk.Entry(center_frame, textvariable=self.param_vars["rotation_center_x"], width=8).grid(row=0, column=1, padx=5)
        
        ttk.Label(center_frame, text="中心Y:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.param_vars["rotation_center_y"] = tk.DoubleVar(value=self.plugin.params["rotation_center_y"])
        ttk.Entry(center_frame, textvariable=self.param_vars["rotation_center_y"], width=8).grid(row=0, column=3, padx=5)
        
        # 平移修正参数
        translation_frame = ttk.LabelFrame(scrollable_frame, text="平移修正", padding=5)
        translation_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.param_vars["enable_translation"] = tk.BooleanVar(value=self.plugin.params["enable_translation"])
        ttk.Checkbutton(translation_frame, text="启用平移修正", 
                       variable=self.param_vars["enable_translation"], 
                       command=self._on_param_change).pack(anchor=tk.W)
        
        # 偏移量
        offset_frame = ttk.Frame(translation_frame)
        offset_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(offset_frame, text="X偏移:").pack(side=tk.LEFT)
        self.param_vars["offset_x"] = tk.DoubleVar(value=self.plugin.params["offset_x"])
        offset_x_scale = ttk.Scale(offset_frame, from_=-200, to=200, 
                                  variable=self.param_vars["offset_x"], 
                                  orient=tk.HORIZONTAL, command=self._on_scale_change)
        offset_x_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        ttk.Entry(offset_frame, textvariable=self.param_vars["offset_x"], width=8).pack(side=tk.RIGHT)
        
        offset_y_frame = ttk.Frame(translation_frame)
        offset_y_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(offset_y_frame, text="Y偏移:").pack(side=tk.LEFT)
        self.param_vars["offset_y"] = tk.DoubleVar(value=self.plugin.params["offset_y"])
        offset_y_scale = ttk.Scale(offset_y_frame, from_=-200, to=200, 
                                  variable=self.param_vars["offset_y"], 
                                  orient=tk.HORIZONTAL, command=self._on_scale_change)
        offset_y_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        ttk.Entry(offset_y_frame, textvariable=self.param_vars["offset_y"], width=8).pack(side=tk.RIGHT)
        
        # 缩放修正参数
        scale_frame = ttk.LabelFrame(scrollable_frame, text="缩放修正", padding=5)
        scale_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.param_vars["enable_scale"] = tk.BooleanVar(value=self.plugin.params["enable_scale"])
        ttk.Checkbutton(scale_frame, text="启用缩放修正", 
                       variable=self.param_vars["enable_scale"], 
                       command=self._on_param_change).pack(anchor=tk.W)
        
        # 缩放比例
        scale_x_frame = ttk.Frame(scale_frame)
        scale_x_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(scale_x_frame, text="X缩放:").pack(side=tk.LEFT)
        self.param_vars["scale_x"] = tk.DoubleVar(value=self.plugin.params["scale_x"])
        scale_x_scale = ttk.Scale(scale_x_frame, from_=0.1, to=3.0, 
                                 variable=self.param_vars["scale_x"], 
                                 orient=tk.HORIZONTAL, command=self._on_scale_change)
        scale_x_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        ttk.Entry(scale_x_frame, textvariable=self.param_vars["scale_x"], width=8).pack(side=tk.RIGHT)
        
        scale_y_frame = ttk.Frame(scale_frame)
        scale_y_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(scale_y_frame, text="Y缩放:").pack(side=tk.LEFT)
        self.param_vars["scale_y"] = tk.DoubleVar(value=self.plugin.params["scale_y"])
        scale_y_scale = ttk.Scale(scale_y_frame, from_=0.1, to=3.0, 
                                 variable=self.param_vars["scale_y"], 
                                 orient=tk.HORIZONTAL, command=self._on_scale_change)
        scale_y_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        ttk.Entry(scale_y_frame, textvariable=self.param_vars["scale_y"], width=8).pack(side=tk.RIGHT)
        
        # 调试参数
        debug_frame = ttk.LabelFrame(scrollable_frame, text="调试选项", padding=5)
        debug_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.param_vars["debug_draw"] = tk.BooleanVar(value=self.plugin.params["debug_draw"])
        ttk.Checkbutton(debug_frame, text="显示调试信息", 
                       variable=self.param_vars["debug_draw"], 
                       command=self._on_param_change).pack(anchor=tk.W)
        
        self.param_vars["show_grid"] = tk.BooleanVar(value=self.plugin.params["show_grid"])
        ttk.Checkbutton(debug_frame, text="显示网格", 
                       variable=self.param_vars["show_grid"], 
                       command=self._on_param_change).pack(anchor=tk.W)
        
        # 按钮区域
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="加载图像", command=self._load_image).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="重置参数", command=self._reset_params).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="保存配置", command=self._save_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="加载配置", command=self._load_config).pack(side=tk.LEFT, padx=2)

    def _build_image_panel(self, parent):
        """构建图像显示面板"""
        # 图像显示区域
        self.image_frame = ttk.Frame(parent)
        self.image_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Canvas用于显示图像
        self.canvas = tk.Canvas(self.image_frame, bg='gray')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def _on_param_change(self):
        """参数变化回调"""
        # 更新插件参数
        for key, var in self.param_vars.items():
            if key in self.plugin.params:
                self.plugin.params[key] = var.get()
        
        # 通知外部参数变化
        if self.on_change:
            self.on_change(self.plugin.params)
        
        # 更新预览
        self._update_preview()

    def _on_scale_change(self, value=None):
        """滑块变化回调"""
        self._on_param_change()

    def _update_preview(self):
        """更新预览图像"""
        if self.img_src is None:
            return
        
        try:
            # 应用位置修正
            self.img_corrected, ctx = self.plugin.process(self.img_src.copy(), {})
            
            # 显示图像
            self._display_image(self.img_corrected)
            
            # 更新状态
            correction_info = ctx.get("position_correction", {}).get("info", {})
            status_text = f"修正模式: {correction_info.get('mode', 'unknown')}"
            if 'rotation_angle' in correction_info:
                status_text += f" | 旋转角度: {correction_info['rotation_angle']:.1f}°"
            self.status_var.set(status_text)
            
        except Exception as e:
            messagebox.showerror("错误", f"预览更新失败: {str(e)}")

    def _display_image(self, img: np.ndarray):
        """显示图像到Canvas"""
        if img is None:
            return
        
        # 转换为RGB
        if img.ndim == 3:
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        else:
            img_rgb = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
        
        # 计算显示尺寸
        h, w = img_rgb.shape[:2]

        # 强制更新Canvas尺寸
        self.canvas.update_idletasks()
        canvas_w = self.canvas.winfo_width()
        canvas_h = self.canvas.winfo_height()

        # 如果Canvas尺寸还没有初始化，使用默认尺寸
        if canvas_w <= 1 or canvas_h <= 1:
            canvas_w, canvas_h = 800, 600

        if canvas_w > 1 and canvas_h > 1:  # Canvas已初始化
            scale = min(canvas_w / w, canvas_h / h, 1.0)
            new_w, new_h = int(w * scale), int(h * scale)
            
            # 调整图像尺寸
            img_resized = cv2.resize(img_rgb, (new_w, new_h))
            
            # 转换为PIL图像
            pil_img = Image.fromarray(img_resized)
            self.photo = ImageTk.PhotoImage(pil_img)
            
            # 清除Canvas并显示图像
            self.canvas.delete("all")
            x = (canvas_w - new_w) // 2
            y = (canvas_h - new_h) // 2
            self.canvas.create_image(x, y, anchor=tk.NW, image=self.photo)

    def _load_image(self):
        """加载图像"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                self.img_src = cv2.imread(file_path)
                if self.img_src is None:
                    raise ValueError("无法读取图像文件")
                
                # 更新旋转中心为图像中心
                h, w = self.img_src.shape[:2]
                self.param_vars["rotation_center_x"].set(w // 2)
                self.param_vars["rotation_center_y"].set(h // 2)
                self.param_vars["dst_width"].set(w)
                self.param_vars["dst_height"].set(h)
                
                self._update_preview()
                self.status_var.set(f"已加载图像: {w}x{h}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载图像失败: {str(e)}")

    def _reset_params(self):
        """重置参数"""
        default_plugin = PositionCorrectionPlugin()
        for key, var in self.param_vars.items():
            if key in default_plugin.params:
                var.set(default_plugin.params[key])
        self._on_param_change()

    def _save_config(self):
        """保存配置"""
        file_path = filedialog.asksaveasfilename(
            title="保存配置",
            defaultextension=".yml",
            filetypes=[("YAML文件", "*.yml"), ("所有文件", "*.*")],
            initialdir=str(CFG_DIR)
        )
        
        if file_path:
            try:
                config = {key: var.get() for key, var in self.param_vars.items()}
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def _load_config(self):
        """加载配置"""
        file_path = filedialog.askopenfilename(
            title="加载配置",
            filetypes=[("YAML文件", "*.yml"), ("所有文件", "*.*")],
            initialdir=str(CFG_DIR)
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                for key, value in config.items():
                    if key in self.param_vars:
                        self.param_vars[key].set(value)
                
                self._on_param_change()
                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建测试UI
    ui = PositionCorrectionUI(root)
    ui.mainloop()
