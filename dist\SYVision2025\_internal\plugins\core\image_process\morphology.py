"""Image morphology processor: binarization + morphological ops

Core-only implementation (no GUI).
"""
from __future__ import annotations
import cv2
import numpy as np
from typing import Dict, Any

__all__ = ["MorphologyProcessor", "Morphology"]

from plugins.plugin_base import PluginBase

class MorphologyProcessor:
    name = "ImageMorphology"
    def __init__(self, *, op="", kernel_size=3, iter=1,
                 thresh_type="binary", thresh_val=128, max_val=255):
        self.op = op.lower()
        self.kernel_size = int(kernel_size)
        self.iter = int(iter)
        self.thresh_type = thresh_type.lower()
        self.thresh_val = int(thresh_val)
        self.max_val = int(max_val)

    def process(self, img: np.ndarray, **override) -> Dict[str, Any]:
        if override:
            for k, v in override.items():
                if hasattr(self, k):
                    setattr(self, k, v)
        bin_img = self._binarize(img)
        out = self._morph_op(bin_img)
        return {"output": out}

    def _binarize(self, img: np.ndarray) -> np.ndarray:
        if img.ndim == 3:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        flag = cv2.THRESH_BINARY_INV if self.thresh_type.startswith("inv") else cv2.THRESH_BINARY
        _, res = cv2.threshold(img, self.thresh_val, self.max_val, flag)
        return res

    def _morph_op(self, bin_img):
        """Apply one or multiple morphology operations.

        If ``self.op`` is:
        1. a single op name (str) – behave as before;
        2. a string joined by ``'+'`` – execute sequentially in order;
        3. a list/tuple of op names – also sequential.
        """
        ksize = max(1, self.kernel_size)
        k = np.ones((ksize,) * 2, np.uint8)

        # 将 op 解析为序列
        if isinstance(self.op, (list, tuple)):
            ops_seq = list(self.op)
        else:
            ops_seq = str(self.op).split('+') if self.op else []
        if not ops_seq:
            return bin_img  # no morphology
        img = bin_img.copy()
        for op in ops_seq:
            op = op.strip().lower()
            if not op:
                continue
            if op == "erode":
                img = cv2.erode(img, k, iterations=self.iter)
            elif op == "median":
                ksize = max(1, self.kernel_size)
                if ksize % 2 == 0:
                    ksize += 1
                img = cv2.medianBlur(img, ksize)
            elif op == "gauss":
                ksize = max(1, self.kernel_size)
                if ksize % 2 == 0:
                    ksize += 1
                blurred = cv2.GaussianBlur(img, (ksize, ksize), 0)
                img = cv2.threshold(blurred, 127, 255, cv2.THRESH_BINARY)[1]
            elif op == "dilate":
                img = cv2.dilate(img, k, iterations=self.iter)
            else:
                code = {
                    "open": cv2.MORPH_OPEN,
                    "close": cv2.MORPH_CLOSE,
                    "gradient": cv2.MORPH_GRADIENT,
                    "tophat": cv2.MORPH_TOPHAT,
                    "blackhat": cv2.MORPH_BLACKHAT,
                }.get(op, cv2.MORPH_OPEN)
                img = cv2.morphologyEx(img, code, k, iterations=self.iter)
        return img

# ---------------------------------------------------------------------------
# Pipeline plugin wrapper
# ---------------------------------------------------------------------------
class Morphology(PluginBase):
    """形态学流水线插件 – 包装 MorphologyProcessor 供 Pipeline 使用"""

    name = "morphology"
    label = "形态学"
    params = {
        "ops": "",              # 运算序列字符串，如 'open+erode'
        "kernel_size": 3,
        "iter": 1,
        "thresh_type": "binary",
        "thresh_val": 128,
        "max_val": 255,
    }

    param_labels = {
        "ops": "算子序列",
        "kernel_size": "核尺寸",
        "iter": "迭代",
        "thresh_type": "阈值类型",
        "thresh_val": "阈值",
        "max_val": "最大值",
    }

    def __init__(self, **params):
        # 创建处理器必须在调用父类 __init__ 前，确保 setup 调用时可用
        self._proc = MorphologyProcessor()
        super().__init__(**params)  # PluginBase 会调用 setup()
        # setup 已同步参数，此处无需再次调用

    # ------------------------------------------------------------------
    def _sync_params(self):
        p = self.params
        self._proc.op = p["ops"]
        self._proc.kernel_size = int(p["kernel_size"])
        self._proc.iter = int(p["iter"])
        self._proc.thresh_type = p["thresh_type"]
        self._proc.thresh_val = int(p["thresh_val"])
        self._proc.max_val = int(p["max_val"])

    def setup(self, params):
        self._sync_params()

    # ------------------------------------------------------------------
    # ------------------------------------------------------------------
    from typing import Optional, Dict, Any

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, *, preview_img: 'np.ndarray | None' = None, **extra):
        """调用专属 morphology_ui 界面"""
        import importlib, tkinter as tk
        from tkinter import ttk
        mod = importlib.import_module('plugins.ui.image_process.morphology_ui')
        if hasattr(mod, 'MorphologyFrame'):
            win = tk.Toplevel(master)
            # 构造预览数据
            preview_data = {'img': preview_img} if preview_img is not None else extra.get('preview')
            frame = mod.MorphologyFrame(win, preview=preview_data)
            frame.pack(fill='both', expand=True)
            frame.pack(fill='both', expand=True)
            # 将已有参数恢复到 UI
            frame.set_params(params)
            def _on_close():
                # 提取参数并回写
                new_params = {
                    'ops': '+'.join(frame.ops_seq),
                    'kernel_size': frame.ks_var.get(),
                    'iter': frame.it_var.get(),
                    'thresh_type': 'inv_binary' if frame.inv_var.get() else 'binary',
                    'thresh_val': frame.th_val.get(),
                    'max_val': 255,
                }
                on_change(new_params)
                win.destroy()
            ttk.Button(win, text='确定', command=_on_close).pack(pady=2)
        else:
            from tkinter import messagebox
            messagebox.showerror('错误','未找到 MorphologyFrame')

    def process(self, img, ctx):
        self._sync_params()  # in case params changed at runtime
        res = self._proc.process(img)
        ctx[self.name] = res

        # 将处理后的图像传递给标定插件
        try:
            from plugins.mm_per_px_plugin import MmPerPxPlugin
            # 转换为彩色图像供标定使用
            processed_img = res["output"]
            if len(processed_img.shape) == 2:
                processed_img = cv2.cvtColor(processed_img, cv2.COLOR_GRAY2BGR)
            MmPerPxPlugin.set_pipeline_image(processed_img)
        except Exception as e:
            print(f"[MORPHOLOGY] 传递图像到标定插件失败: {e}")

        # 返回处理后二值图，同步 ctx
        return res["output"], ctx
