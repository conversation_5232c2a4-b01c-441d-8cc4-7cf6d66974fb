"""集成几何测量工具UI界面

这个模块提供集成几何测量工具的用户界面，包括：
- 参数设置界面
- 图像预览
- 测量结果显示
- 配置文件管理
"""
from __future__ import annotations

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
from pathlib import Path
from typing import Dict, Any, Optional, List

# 新增参数面板基类与子类 (兼容脚本直接运行)
try:
    from .base_param_frame import BaseParamFrame
    from .fillet_param_frame import FilletParamFrame
except ImportError:  # 当作为脚本直接运行时使用绝对路径导入
    import sys, os, importlib
    current_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    BaseParamFrame = importlib.import_module('plugins.ui.geometry_tools.base_param_frame').BaseParamFrame
    FilletParamFrame = importlib.import_module('plugins.ui.geometry_tools.fillet_param_frame').FilletParamFrame


class IntegratedGeometryToolUI:
    """集成几何测量工具UI界面"""
    
    def __init__(self, plugin_instance):
        self.plugin = plugin_instance
        self.window = None
        self.img_src = None
        self.img_display = None
        self.img_original = None  # 保存原始图像
        self.photo = None

        # 模板匹配相关
        self.template_img = None
        self.template_roi = None
        self.match_result = None

        # ROI拖拽相关
        self.roi_start = None
        self.roi_end = None
        self.roi_rect_id = None
        self.is_drawing_roi = False

        # UI组件
        self.canvas = None
        self.param_vars = {}
        # 任务数据结构
        self.tasks: List[Dict[str, Any]] = []  # 每项 {'type': str, 'params': dict}
        self.current_param_frame: Optional[BaseParamFrame] = None
        # 画布到原图的映射参数
        self.display_scale: float = 1.0
        self.display_offset: tuple[int, int] = (0, 0)
        
    def show(self):
        """显示UI界面"""
        if self.window is None:
            self._create_window()

        self.window.deiconify()
        self.window.lift()

        # 如果有图像，显示图像
        if self.img_src is not None:
            # 延迟显示图像，确保窗口完全创建
            self.window.after(100, lambda: self._display_image(self.img_src))
        
    def close(self):
        """关闭UI界面"""
        if self.window:
            self.window.destroy()
            self.window = None
            # 新增：如果有Tk主窗口，关闭主窗口，确保程序退出
            try:
                if hasattr(self, 'root') and self.root:
                    self.root.quit()
                    self.root.destroy()
            except Exception:
                pass
            
    def _create_window(self):
        """创建主窗口"""
        self.window = tk.Toplevel()
        self.window.title("集成几何测量工具")
        self.window.geometry("1000x700")
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧参数面板
        self._create_param_panel(main_frame)
        
        # 右侧图像预览
        self._create_image_panel(main_frame)
        
        # 底部按钮
        self._create_button_panel(main_frame)
        
    def _create_param_panel(self, parent):
        """创建参数设置面板"""
        # 左侧框架
        left_frame = ttk.Frame(parent)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        # ---------------- 任务列表相关逻辑 ↓
        def _add_task(self):
            tool_type_cn = self.add_tool_var.get()  # 获取中文名称
            tool_type_en = self.tool_name_map[tool_type_cn]  # 转换为英文标识符
            new_task = {"type": tool_type_en, "params": {}}
            self.tasks.append(new_task)
            self._refresh_task_tree()
            self.task_tree.selection_set(str(len(self.tasks)-1))
            self._on_task_select()

        def _delete_task(self):
            if not self.tasks:
                return
            sel = self.task_tree.selection()
            if not sel:
                return
            idx = int(sel[0])
            self.tasks.pop(idx)
            self._refresh_task_tree()
            if self.tasks:
                self.task_tree.selection_set(str(min(idx, len(self.tasks)-1)))
                self._on_task_select()

        def _move_task(self, direction:int):
            if not self.tasks:
                return
            sel = self.task_tree.selection()
            if not sel:
                return
            idx = int(sel[0])
            new_idx = idx + direction
            if new_idx < 0 or new_idx >= len(self.tasks):
                return
            # 交换
            self.tasks[idx], self.tasks[new_idx] = self.tasks[new_idx], self.tasks[idx]
            self._refresh_task_tree()
            self.task_tree.selection_set(str(new_idx))
            self._on_task_select()

        def _refresh_task_tree(self):
            self.task_tree.delete(*self.task_tree.get_children())
            for i, t in enumerate(self.tasks):
                # 显示中文名称
                tool_type_cn = self.tool_name_map_reverse.get(t["type"], t["type"])
                self.task_tree.insert("", "end", iid=str(i), values=(tool_type_cn,))

        def _on_task_select(self, event=None):
            # 销毁旧面板
            if self.current_param_frame is not None:
                self.current_param_frame.destroy()
                self.current_param_frame = None
            sel = self.task_tree.selection()
            if not sel:
                return
            idx = int(sel[0])
            task = self.tasks[idx]
            frame_cls = {
                "fillet_radius": FilletParamFrame,
            }.get(task["type"])

            # 只有特定工具类型才有专用参数框架
            if frame_cls is not None:
                self.current_param_frame = frame_cls(self.param_container, params=task.get("params", {}))
                self.current_param_frame.pack(fill=tk.X, padx=5, pady=5)
            else:
                # 其他工具类型使用通用参数设置
                self.current_param_frame = None

        # 将局部函数绑定为实例方法，供外部调用
        self._add_task = _add_task.__get__(self)
        self._delete_task = _delete_task.__get__(self)
        self._move_task = _move_task.__get__(self)
        self._refresh_task_tree = _refresh_task_tree.__get__(self)
        self._on_task_select = _on_task_select.__get__(self)

        # ---------------- 模板匹配相关 ↓----------
        task_frame = ttk.LabelFrame(left_frame, text="任务列表")
        task_frame.pack(fill=tk.X, pady=(0, 10))
        # 工具选择下拉 - 中英文映射
        self.tool_types_en = ("fillet_radius", "cross_caliper", "circle", "arc", "angle")
        self.tool_types_cn = ("圆角半径", "十字卡尺", "圆形测量", "弧形测量", "角度测量")
        self.tool_name_map = dict(zip(self.tool_types_cn, self.tool_types_en))  # 中文->英文
        self.tool_name_map_reverse = dict(zip(self.tool_types_en, self.tool_types_cn))  # 英文->中文

        self.add_tool_var = tk.StringVar(value=self.tool_types_cn[0])
        ttk.Combobox(task_frame, textvariable=self.add_tool_var, values=self.tool_types_cn, width=14, state="readonly").pack(fill=tk.X, padx=5, pady=2)

        # Treeview
        self.task_tree = ttk.Treeview(task_frame, columns=("type",), show="headings", height=6)
        self.task_tree.heading("type", text="工具类型")
        self.task_tree.pack(fill=tk.X, padx=5, pady=3)
        self.task_tree.bind("<<TreeviewSelect>>", self._on_task_select)
        # 按钮条
        btn_bar = ttk.Frame(task_frame)
        btn_bar.pack(fill=tk.X, pady=2)
        ttk.Button(btn_bar, text="➕添加", width=4, command=self._add_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_bar, text="✂删除", width=4, command=self._delete_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_bar, text="⬆", width=3, command=lambda:self._move_task(-1)).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_bar, text="⬇", width=3, command=lambda:self._move_task(1)).pack(side=tk.LEFT, padx=2)

        # 参数设置标题
        ttk.Label(left_frame, text="参数设置", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        # 可滚动Canvas
        param_canvas = tk.Canvas(left_frame, width=300, height=400)
        param_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        param_scrollbar = tk.Scrollbar(left_frame)
        param_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 将滚动条关联到Canvas
        param_canvas.config(yscrollcommand=param_scrollbar.set)
        param_scrollbar.config(command=param_canvas.yview)

        # 内部框架
        self.param_container = ttk.Frame(param_canvas)
        self.param_container.pack(fill=tk.BOTH, expand=True)

        # 将内部框架添加到Canvas
        param_canvas.create_window((0, 0), window=self.param_container, anchor='nw')

        # 更新Canvas滚动范围
        self.param_container.update_idletasks()
        param_canvas.config(scrollregion=param_canvas.bbox("all"))

        # 扫描参数
        scan_frame = ttk.LabelFrame(self.param_container, text="扫描参数")
        scan_frame.pack(fill=tk.X, pady=(0, 10))
        # 保存为全局参数面板引用
        self.global_param_frame = scan_frame

        # 扫描线数量
        ttk.Label(scan_frame, text="扫描线数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.param_vars["scan_lines"] = tk.IntVar(value=self.plugin.params["scan_lines"])
        scan_lines_spin = ttk.Spinbox(scan_frame, from_=4, to=50, width=10,
                                     textvariable=self.param_vars["scan_lines"])
        scan_lines_spin.grid(row=0, column=1, padx=5, pady=2)
        
        # 线宽
        ttk.Label(scan_frame, text="线宽:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.param_vars["line_width"] = tk.IntVar(value=self.plugin.params["line_width"])
        line_width_spin = ttk.Spinbox(scan_frame, from_=1, to=50, width=10,
                                     textvariable=self.param_vars["line_width"])
        line_width_spin.grid(row=1, column=1, padx=5, pady=2)
        
        # 边缘阈值
        ttk.Label(scan_frame, text="边缘阈值:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.param_vars["edge_threshold"] = tk.IntVar(value=self.plugin.params["edge_threshold"])
        threshold_spin = ttk.Spinbox(scan_frame, from_=1, to=100, width=10,
                                    textvariable=self.param_vars["edge_threshold"])
        threshold_spin.grid(row=2, column=1, padx=5, pady=2)
        
        # 边缘极性
        ttk.Label(scan_frame, text="边缘极性:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.param_vars["polarity"] = tk.StringVar(value=self.plugin.params["polarity"])
        polarity_combo = ttk.Combobox(scan_frame, textvariable=self.param_vars["polarity"],
                                     values=["BOTH", "POSITIVE", "NEGATIVE"], state="readonly", width=8)
        polarity_combo.grid(row=3, column=1, padx=5, pady=2)
        
        # ROI扩张比例
        ttk.Label(scan_frame, text="ROI扩张:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        self.param_vars["roi_expand_ratio"] = tk.DoubleVar(value=self.plugin.params["roi_expand_ratio"])
        expand_spin = ttk.Spinbox(scan_frame, from_=0.5, to=2.0, increment=0.1, width=10,
                                 textvariable=self.param_vars["roi_expand_ratio"])
        expand_spin.grid(row=4, column=1, padx=5, pady=2)

        # 圆角ROI半径 (仅fillet测量使用)
        ttk.Label(scan_frame, text="圆角ROI半径:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=2)
        default_fillet_r = self.plugin.params.get("fillet_roi_radius", 50)
        self.param_vars["fillet_roi_radius"] = tk.DoubleVar(value=default_fillet_r)
        fillet_r_spin = ttk.Spinbox(scan_frame, from_=5, to=500, increment=5, width=10,
                                   textvariable=self.param_vars["fillet_roi_radius"])
        fillet_r_spin.grid(row=5, column=1, padx=5, pady=2)
        
        # 调试选项
        debug_frame = ttk.LabelFrame(left_frame, text="调试选项")
        debug_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.param_vars["debug_draw"] = tk.BooleanVar(value=self.plugin.params.get("debug_draw", True))
        debug_draw_cb = ttk.Checkbutton(debug_frame, text="调试绘制", variable=self.param_vars["debug_draw"])
        debug_draw_cb.pack(anchor=tk.W, padx=5, pady=2)

        self.param_vars["debug_log"] = tk.BooleanVar(value=self.plugin.params.get("debug_log", False))
        self.param_vars["debug_log"].trace_add('write', lambda *a: self.plugin.params.__setitem__('debug_log', self.param_vars['debug_log'].get()))
        debug_log_cb = ttk.Checkbutton(debug_frame, text="输出调试日志", variable=self.param_vars["debug_log"])
        debug_log_cb.pack(anchor=tk.W, padx=5, pady=2)

        self.param_vars["show_roi_box"] = tk.BooleanVar(value=self.plugin.params.get("show_roi_box", True))
        self.param_vars["show_roi_box"].trace_add('write', lambda *a: self.plugin.params.__setitem__('show_roi_box', self.param_vars['show_roi_box'].get()))
        show_roi_cb = ttk.Checkbutton(debug_frame, text="显示ROI框", variable=self.param_vars["show_roi_box"])
        show_roi_cb.pack(anchor=tk.W, padx=5, pady=2)
        
        # 模板管理
        template_frame = ttk.LabelFrame(left_frame, text="模板管理")
        template_frame.pack(fill=tk.X, pady=(0, 10))

        # 模板文件路径参数
        self.param_vars["template_file"] = tk.StringVar(value=self.plugin.params.get("template_file", ""))

        # 模板文件路径显示
        path_frame = ttk.Frame(template_frame)
        path_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(path_frame, text="模板文件:").pack(anchor=tk.W)
        self.template_path_label = ttk.Label(path_frame, text="未选择", foreground="gray", wraplength=200)
        self.template_path_label.pack(anchor=tk.W, padx=(10, 0))

        # 模板操作按钮
        btn_frame1 = ttk.Frame(template_frame)
        btn_frame1.pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(btn_frame1, text="选择模板文件", command=self._load_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame1, text="从当前图像创建", command=self._create_template).pack(side=tk.LEFT)

        btn_frame2 = ttk.Frame(template_frame)
        btn_frame2.pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(btn_frame2, text="测试匹配", command=self._execute_matching).pack(fill=tk.X)

        # 匹配方式选择
        method_frame = ttk.Frame(template_frame)
        method_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(method_frame, text="匹配方式:").pack(side=tk.LEFT)
        self.param_vars["match_method"] = tk.StringVar(value=self.plugin.params.get("match_method", "template"))
        method_combo = ttk.Combobox(method_frame, textvariable=self.param_vars["match_method"],
                                   values=["template", "shape", "feature"], state="readonly", width=10)
        method_combo.pack(side=tk.RIGHT)

        # 匹配参数 - 使用pack布局
        threshold_frame = ttk.Frame(template_frame)
        threshold_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(threshold_frame, text="匹配阈值:").pack(side=tk.LEFT)
        self.param_vars["match_threshold"] = tk.DoubleVar(value=self.plugin.params.get("match_threshold", 0.8))
        threshold_spin = ttk.Spinbox(threshold_frame, from_=0.1, to=1.0, increment=0.1, width=8,
                                    textvariable=self.param_vars["match_threshold"])
        threshold_spin.pack(side=tk.RIGHT)

        # 角度测量参数
        angle_frame = ttk.LabelFrame(self.param_container, text="角度测量参数")
        angle_frame.pack(fill=tk.X, pady=(0, 10))

        # 角度测量方法
        method_frame = ttk.Frame(angle_frame)
        method_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(method_frame, text="测量方法:").pack(side=tk.LEFT)
        self.param_vars["angle_method"] = tk.StringVar(value=self.plugin.params.get("angle_method", "shape_match"))
        angle_method_combo = ttk.Combobox(method_frame, textvariable=self.param_vars["angle_method"],
                                         values=["shape_match", "edge_fitting", "contour_analysis"],
                                         state="readonly", width=15)
        angle_method_combo.pack(side=tk.RIGHT)

        # 添加方法说明
        help_frame = ttk.Frame(angle_frame)
        help_frame.pack(fill=tk.X, padx=5, pady=2)

        help_text = tk.Text(help_frame, height=3, wrap=tk.WORD, font=("Arial", 8))
        help_text.pack(fill=tk.X)
        help_text.insert(tk.END, "shape_match: 使用形状匹配角度(快速)\n")
        help_text.insert(tk.END, "edge_fitting: 基于边缘直线拟合(精度高)\n")
        help_text.insert(tk.END, "contour_analysis: 基于轮廓主方向(中等精度)")
        help_text.config(state=tk.DISABLED)

        # 配置管理
        config_frame = ttk.LabelFrame(left_frame, text="配置管理")
        config_frame.pack(fill=tk.X, pady=(0, 10))

        config_btn_frame = ttk.Frame(config_frame)
        config_btn_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(config_btn_frame, text="保存配置", command=self._save_default_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_btn_frame, text="重置参数", command=self._reset_params).pack(side=tk.LEFT)
        
    def _create_image_panel(self, parent):
        """创建图像预览面板"""
        # 右侧框架
        right_frame = ttk.Frame(parent)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 图像预览标题
        ttk.Label(right_frame, text="图像预览", font=("Arial", 12, "bold")).pack(pady=(0, 10))
        
        # 图像加载按钮
        load_frame = ttk.Frame(right_frame)
        load_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(load_frame, text="加载图像", command=self._load_image).pack(side=tk.LEFT)
        ttk.Button(load_frame, text="测试单个", command=self._test_measurement).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(load_frame, text="批量执行", command=self._test_all_tasks).pack(side=tk.LEFT, padx=(5, 0))
        
        # 图像画布
        canvas_frame = ttk.Frame(right_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.canvas = tk.Canvas(canvas_frame, bg="gray")
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self._on_mouse_down)
        self.canvas.bind("<B1-Motion>", self._on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_mouse_up)
        
        # 结果显示
        result_frame = ttk.LabelFrame(right_frame, text="测量结果")
        result_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.result_text = tk.Text(result_frame, height=6, wrap=tk.WORD)
        self.result_text.pack(fill=tk.X, padx=5, pady=5)
        
    def _create_button_panel(self, parent):
        """创建底部按钮面板"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="应用参数", command=self._apply_params).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="重置参数", command=self._reset_params).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(button_frame, text="关闭", command=self.close).pack(side=tk.RIGHT)
        
    def _load_image(self):
        """加载图像"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择图像文件",
                filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"), ("所有文件", "*.*")]
            )

            if file_path:
                self.img_src = cv2.imread(file_path)
                if self.img_src is None:
                    messagebox.showerror("错误", "无法加载图像文件")
                    return

                # 保存原始图像副本
                self.img_original = self.img_src.copy()

                # 显示图像
                self._display_image(self.img_src)

                self.result_text.delete(1.0, tk.END)
                self.result_text.insert(tk.END, f"✅ 图像已加载: {Path(file_path).name}\n")
                self.result_text.insert(tk.END, f"尺寸: {self.img_src.shape[1]} x {self.img_src.shape[0]}\n")

        except Exception as e:
            messagebox.showerror("错误", f"加载图像失败: {str(e)}")

    def _display_image(self, img):
        """显示图像"""
        if img is None:
            return

        try:
            # 转换为RGB
            if len(img.shape) == 3:
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            else:
                img_rgb = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)

            # 获取画布尺寸
            self.canvas.update()  # 确保画布尺寸已更新
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                h, w = img_rgb.shape[:2]

                # 计算缩放比例
                scale_w = canvas_width / w
                scale_h = canvas_height / h
                scale = min(scale_w, scale_h)
                # 如果图像本来就能完整显示，不要放大，避免scale>1导致ROI换算变小
                if scale > 1.0:
                    scale = 1.0  # 只做缩放，不做放大

                new_w, new_h = int(w * scale), int(h * scale)

                # 记录映射关系（打印调试）
                self.display_scale = scale
                self.display_offset = (canvas_width//2 - new_w//2, canvas_height//2 - new_h//2)
                print(f"[DEBUG] display_scale={scale:.4f}, offset={self.display_offset}")

                # 缩放图像
                img_resized = cv2.resize(img_rgb, (new_w, new_h))

                # 转换为PIL图像
                pil_img = Image.fromarray(img_resized)
                self.photo = ImageTk.PhotoImage(pil_img)

                # 清除画布并显示图像
                self.canvas.delete("all")
                self.canvas.create_image(canvas_width//2, canvas_height//2,
                                       image=self.photo, anchor=tk.CENTER)

        except Exception as e:
            print(f"显示图像错误: {e}")
            messagebox.showerror("错误", f"显示图像失败: {str(e)}")
                
    def _test_measurement(self):
        """测试测量功能"""
        if self.img_src is None:
            messagebox.showwarning("警告", "请先加载图像")
            return

        try:
            # 应用当前参数
            self._apply_params()

            # 根据当前选中的任务设置测量类型
            sel = self.task_tree.selection()
            if not sel:
                messagebox.showwarning("警告", "请先在任务列表中选择需测试的任务")
                return
            task_idx = int(sel[0])
            task_type = self.tasks[task_idx]["type"]
            self.plugin.params["measurement_type"] = task_type  # 告诉插件测量类型

            # 优先使用形状匹配ROI
            if self.match_result and self.match_result.get("method") == "shape":
                roi_info = self.match_result.copy()
                self.result_text.insert(tk.END, "使用形状匹配ROI进行测量\n")
            else:
                self.result_text.insert(tk.END, "❌ 请先进行形状匹配以获取准确的ROI\n")
                self.result_text.insert(tk.END, "步骤：1.选择模板区域 2.选择'形状匹配' 3.点击'匹配' 4.再测量\n")
                return

            # 创建上下文，包含ROI和UI参数
            ctx = {
                "shape_match": roi_info,
                "ui_params": {
                    "scan_lines": self.param_vars["scan_lines"].get(),
                    "line_width": self.param_vars["line_width"].get(),
                    "edge_threshold": self.param_vars["edge_threshold"].get(),
                    "polarity": self.param_vars["polarity"].get(),
                    "roi_expand_ratio": self.param_vars["roi_expand_ratio"].get(),
                    "debug_draw": self.param_vars["debug_draw"].get(),

                }
            }
            
            # 执行测量
            result_img, result_ctx = self.plugin.process(self.img_src.copy(), ctx)

            # 检查是否有可视化结果
            measurement_result = result_ctx.get("measurement_result", {})
            if 'visualization' in measurement_result and 'result_image' in measurement_result['visualization']:
                # 显示带有十字卡尺线的可视化图像
                vis_img = measurement_result['visualization']['result_image']
                self._display_image(vis_img)
                print("🎨 显示十字卡尺可视化图像")
            else:
                # 显示原始结果图像
                self._display_image(result_img)
            
            # 显示测量结果
            measurement_result = result_ctx.get("measurement_result", {})
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "测量结果:\n")
            
            if measurement_result.get("ok"):
                if "width" in measurement_result and "height" in measurement_result:
                    self.result_text.insert(tk.END, f"宽度: {measurement_result['width']:.2f}\n")
                    self.result_text.insert(tk.END, f"高度: {measurement_result['height']:.2f}\n")
                self.result_text.insert(tk.END, "✅ 测量成功\n")
            else:
                error = measurement_result.get("error", "未知错误")
                self.result_text.insert(tk.END, f"❌ 测量失败: {error}\n")
                
        except Exception as e:
            messagebox.showerror("错误", f"测试测量失败: {str(e)}")

    def _test_all_tasks(self):
        """批量执行所有任务"""
        if self.img_src is None:
            messagebox.showwarning("警告", "请先加载图像")
            return

        if not self.tasks:
            messagebox.showwarning("警告", "任务列表为空，请先添加测量任务")
            return

        try:
            # 应用当前参数
            self._apply_params()

            # 检查是否有形状匹配ROI
            if not (self.match_result and self.match_result.get("method") == "shape"):
                self.result_text.insert(tk.END, "❌ 请先进行形状匹配以获取准确的ROI\n")
                self.result_text.insert(tk.END, "步骤：1.选择模板区域 2.选择'形状匹配' 3.点击'匹配' 4.再批量执行\n")
                return

            roi_info = self.match_result.copy()

            # 清空结果显示
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"🚀 开始批量执行 {len(self.tasks)} 个测量任务\n")
            self.result_text.insert(tk.END, "=" * 50 + "\n")

            # 准备组合结果
            combined_results = {}
            combined_img = self.img_src.copy()

            # 逐个执行任务
            for i, task in enumerate(self.tasks):
                task_type = task["type"]
                task_type_cn = self.tool_name_map_reverse.get(task_type, task_type)
                self.result_text.insert(tk.END, f"\n📋 任务 {i+1}/{len(self.tasks)}: {task_type_cn}\n")

                # 设置当前任务类型
                self.plugin.params["measurement_type"] = task_type

                # 创建上下文
                ctx = {
                    "shape_match": roi_info,
                    "ui_params": {
                        "scan_lines": self.param_vars["scan_lines"].get(),
                        "line_width": self.param_vars["line_width"].get(),
                        "edge_threshold": self.param_vars["edge_threshold"].get(),
                        "polarity": self.param_vars["polarity"].get(),
                        "roi_expand_ratio": self.param_vars["roi_expand_ratio"].get(),
                        "debug_draw": self.param_vars["debug_draw"].get(),
                    }
                }

                # 🔧 修复：每次都基于原始图像执行，避免重复绘制
                result_img, result_ctx = self.plugin.process(self.img_src.copy(), ctx)
                measurement_result = result_ctx.get("measurement_result", {})

                # 记录结果
                combined_results[task_type] = measurement_result

                # 显示结果
                if measurement_result.get("ok"):
                    self.result_text.insert(tk.END, f"✅ {task_type_cn} 测量成功\n")
                    self._display_task_result(task_type, measurement_result)

                    # 🔧 修复：叠加显示所有任务的绘制结果
                    if combined_img is None:
                        combined_img = result_img.copy()
                    else:
                        # 简单替换，避免维度问题
                        combined_img = result_img.copy()
                else:
                    error = measurement_result.get("error", "未知错误")
                    self.result_text.insert(tk.END, f"❌ {task_type_cn} 测量失败: {error}\n")

            # 显示最终组合结果
            self.result_text.insert(tk.END, "\n" + "=" * 50 + "\n")
            self.result_text.insert(tk.END, "📊 批量执行完成，综合结果:\n")
            self._display_combined_summary(combined_results)

            # 显示组合图像
            self._display_image(combined_img)

        except Exception as e:
            messagebox.showerror("错误", f"批量执行失败: {str(e)}")

    def _display_task_result(self, task_type, result):
        """显示单个任务的结果"""
        if task_type == "fillet_radius":
            if result.get("corners"):
                self.result_text.insert(tk.END, f"   检测到 {len(result['corners'])} 个圆角\n")
                for i, corner in enumerate(result["corners"]):
                    radius = corner.get("radius", 0)
                    self.result_text.insert(tk.END, f"   圆角{i+1}: 半径={radius:.2f}px\n")

        elif task_type == "angle":
            angle = result.get("angle")
            if angle is not None:
                self.result_text.insert(tk.END, f"   主边方向角: {angle:.2f}°\n")
                if result.get("corner_details"):
                    self.result_text.insert(tk.END, f"   角点信息:\n")
                    for detail in result["corner_details"]:
                        self.result_text.insert(tk.END, f"     角点{detail['id']}: {detail['angle']:.1f}°\n")

        elif task_type == "cross_caliper":
            width = result.get("width")
            height = result.get("height")
            if width and height:
                self.result_text.insert(tk.END, f"   宽度: {width:.2f}px, 高度: {height:.2f}px\n")

        elif task_type in ["circle", "arc"]:
            radius = result.get("radius")
            if radius:
                self.result_text.insert(tk.END, f"   半径: {radius:.2f}px\n")

    def _display_combined_summary(self, combined_results):
        """显示组合结果摘要"""
        success_count = sum(1 for r in combined_results.values() if r.get("ok"))
        total_count = len(combined_results)

        self.result_text.insert(tk.END, f"成功: {success_count}/{total_count} 个任务\n")

        # 按类型汇总
        for task_type, result in combined_results.items():
            status = "✅" if result.get("ok") else "❌"
            task_type_cn = self.tool_name_map_reverse.get(task_type, task_type)
            self.result_text.insert(tk.END, f"{status} {task_type_cn}\n")

    def _apply_params(self):
        """应用参数设置"""
        try:
            for param_name, var in self.param_vars.items():
                value = var.get()
                self.plugin.params[param_name] = value
                if param_name == 'template_file':
                    print(f"[DEBUG] 应用 template_file 参数: {value}")

            print(f"[DEBUG] 插件参数中的 template_file: {self.plugin.params.get('template_file', 'NOT_FOUND')}")

            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "✅ 参数已应用\n")

        except Exception as e:
            messagebox.showerror("错误", f"应用参数失败: {str(e)}")
            
    def _reset_params(self):
        """重置参数"""
        try:
            # 重置为默认值
            default_params = {
                "measurement_type": "cross_caliper",
                "scan_lines": 24,
                "line_width": 10,
                "edge_threshold": 5,
                "polarity": "BOTH",
                "roi_expand_ratio": 1.1,
                "angle_method": "shape_match",
                "debug_draw": True,
                "debug_log": False,
                "show_roi_box": True,
            }
            
            for param_name, default_value in default_params.items():
                if param_name in self.param_vars:
                    self.param_vars[param_name].set(default_value)
                self.plugin.params[param_name] = default_value
            
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "✅ 参数已重置\n")
            
        except Exception as e:
            messagebox.showerror("错误", f"重置参数失败: {str(e)}")
            
    def _save_config(self):
        """保存配置"""
        from pathlib import Path

        # 使用固定的配置目录和文件名格式，与其他几何工具保持一致
        cfg_dir = Path(__file__).resolve().parents[3] / 'configs' / 'integrated_geometry'
        cfg_dir.mkdir(parents=True, exist_ok=True)

        file_path = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".yml",
            initialdir=str(cfg_dir),
            initialfile="default.yml",
            filetypes=[("YAML文件", "*.yml"), ("所有文件", "*.*")]
        )

        if file_path:
            self._apply_params()
            self.plugin.save_config(file_path)
            
    def _load_config(self):
        """加载配置"""
        from pathlib import Path

        # 使用固定的配置目录，与其他几何工具保持一致
        cfg_dir = Path(__file__).resolve().parents[3] / 'configs' / 'integrated_geometry'
        cfg_dir.mkdir(parents=True, exist_ok=True)

        file_path = filedialog.askopenfilename(
            title="加载配置文件",
            initialdir=str(cfg_dir),
            filetypes=[("YAML文件", "*.yml"), ("所有文件", "*.*")]
        )

        if file_path:
            self.plugin.load_config(file_path)

            # 更新UI显示
            for param_name, var in self.param_vars.items():
                if param_name in self.plugin.params:
                    var.set(self.plugin.params[param_name])

            # 🔧 修复：同步更新任务列表
            self.tasks = self.plugin.params.get("tasks", [])
            self._refresh_task_tree()

    def _save_default_config(self):
        """保存为默认配置"""
        try:
            self._apply_params()
            self.plugin.save_config()  # 不传参数，使用默认路径
            messagebox.showinfo("成功", "默认配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存默认配置失败: {str(e)}")

    def _load_default_config(self):
        """加载默认配置"""
        try:
            self.plugin.load_config()  # 不传参数，使用默认路径
            # 更新UI显示
            for param_name, var in self.param_vars.items():
                if param_name in self.plugin.params:
                    var.set(self.plugin.params[param_name])

            # 🔧 修复：同步更新任务列表
            self.tasks = self.plugin.params.get("tasks", [])
            self._refresh_task_tree()
            messagebox.showinfo("成功", "默认配置已加载")
        except Exception as e:
            messagebox.showerror("错误", f"加载默认配置失败: {str(e)}")

    def _create_template(self):
        """创建模板"""
        if self.img_src is None:
            messagebox.showwarning("警告", "请先加载图像")
            return

        if self.template_roi is None:
            messagebox.showwarning("警告", "请先用鼠标拖拽选择ROI区域")
            return

        try:
            # 从图像中提取ROI区域作为模板
            roi = self.template_roi
            x, y, w, h = roi["x"], roi["y"], roi["width"], roi["height"]

            # 转换为灰度图
            if len(self.img_src.shape) == 3:
                gray = cv2.cvtColor(self.img_src, cv2.COLOR_BGR2GRAY)
            else:
                gray = self.img_src

            # 提取模板区域
            self.template_img = gray[y:y+h, x:x+w]
            if self.template_img.size == 0:
                messagebox.showerror("错误", "模板区域为空，请检查ROI是否越界")
                return

            self.result_text.insert(tk.END, f"✅ 模板已创建: {w}x{h}\n")
            print(f"[DEBUG] 模板大小: {w}x{h}")
            self.result_text.insert(tk.END, f"位置: ({x}, {y})\n")

            # 🔧 自动保存模板文件到默认位置
            try:
                from pathlib import Path
                import datetime

                # 创建模板目录
                template_dir = Path(__file__).resolve().parents[3] / 'configs' / 'integrated_geometry'
                template_dir.mkdir(parents=True, exist_ok=True)

                # 生成默认文件名
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                template_file = template_dir / f'template_{timestamp}.png'

                # 保存模板文件
                success = cv2.imwrite(str(template_file), self.template_img)
                if success:
                    # 更新模板文件路径参数
                    if 'template_file' in self.param_vars:
                        self.param_vars['template_file'].set(str(template_file))
                        print(f"[DEBUG] 模板已自动保存并设置路径: {template_file}")
                        # 更新UI显示
                        self.template_path_label.config(text=template_file.name, foreground="black")
                        self.result_text.insert(tk.END, f"📁 模板已自动保存: {template_file.name}\n")
                    else:
                        print(f"[ERROR] template_file 参数变量不存在！")
                else:
                    print(f"[WARNING] 模板文件保存失败: {template_file}")

            except Exception as save_error:
                print(f"[WARNING] 自动保存模板失败: {save_error}")
                # 保存失败不影响模板创建，继续执行

        except Exception as e:
            messagebox.showerror("错误", f"创建模板失败: {str(e)}")

    def _save_template(self):
        """保存模板"""
        if self.template_img is None:
            messagebox.showwarning("警告", "请先创建模板")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="保存模板文件",
                defaultextension=".png",
                filetypes=[("PNG文件", "*.png"), ("JPG文件", "*.jpg"), ("所有文件", "*.*")]
            )

            if file_path:
                # 若无扩展名，默认加 .png
                if Path(file_path).suffix == "":
                    file_path = f"{file_path}.png"
                # 尝试直接保存
                ok = cv2.imwrite(file_path, self.template_img)
                if not ok:
                    # 兼容含非ASCII路径：imencode + tofile
                    ext = Path(file_path).suffix or '.png'
                    success, buf = cv2.imencode(ext, self.template_img)
                    if success:
                        buf.tofile(file_path)
                        ok = True
                if ok:
                    self.result_text.insert(tk.END, f"✅ 模板已保存: {Path(file_path).name}\n")

                    # 🔧 修复：保存模板文件路径到参数中
                    if 'template_file' in self.param_vars:
                        self.param_vars['template_file'].set(file_path)
                        print(f"[DEBUG] 模板保存后，路径已更新到参数变量: {file_path}")
                        # 更新UI显示
                        self.template_path_label.config(text=Path(file_path).name, foreground="black")
                    else:
                        print(f"[ERROR] template_file 参数变量不存在！")
                else:
                    messagebox.showerror("错误", "模板保存失败，请检查路径或权限")

        except Exception as e:
            messagebox.showerror("错误", f"保存模板失败: {str(e)}")

    def _load_template(self):
        """加载模板文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择模板图像",
                filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"), ("所有文件", "*.*")]
            )

            if file_path:
                try:
                    # 使用 imdecode 支持中文/非ASCII 路径
                    file_data = np.fromfile(file_path, dtype=np.uint8)
                    template = cv2.imdecode(file_data, cv2.IMREAD_GRAYSCALE)
                except Exception as e:
                    template = None
                    print(f"读取模板文件异常: {e}")

                if template is None:
                    messagebox.showerror("错误", f"无法加载模板文件:\n{file_path}")
                    return

                self.template_img = template
                h, w = template.shape[:2]

                # 重置ROI信息
                self.template_roi = {
                    "x": 0,
                    "y": 0,
                    "width": w,
                    "height": h,
                    "center": (w // 2, h // 2)
                }

                # 保存模板文件路径到参数中
                if 'template_file' in self.param_vars:
                    self.param_vars['template_file'].set(file_path)
                    print(f"[DEBUG] 模板路径已保存到参数变量: {file_path}")
                else:
                    print(f"[ERROR] template_file 参数变量不存在！")
                    print(f"[DEBUG] 当前参数变量: {list(self.param_vars.keys())}")

                # 更新路径显示
                if hasattr(self, 'template_path_label'):
                    self.template_path_label.config(text=Path(file_path).name, foreground="black")

                self.result_text.insert(tk.END, f"✅ 模板已加载: {Path(file_path).name}\n")
                self.result_text.insert(tk.END, f"尺寸: {w} x {h}\n")
                self.result_text.insert(tk.END, f"路径: {file_path}\n")

        except Exception as e:
            messagebox.showerror("错误", f"加载模板失败: {str(e)}")

    def _execute_matching(self):
        """执行匹配"""
        if self.img_src is None:
            messagebox.showwarning("警告", "请先加载图像")
            return

        if self.template_img is None:
            messagebox.showwarning("警告", "请先创建模板")
            return

        try:
            # 清空之前的结果文本
            self.result_text.delete(1.0, tk.END)

            match_method = self.param_vars["match_method"].get()

            # 确保使用原始图像进行匹配
            if self.img_original is not None:
                self.img_src = self.img_original.copy()

            if match_method == "template":
                result = self._template_matching()
            elif match_method == "shape":
                result = self._shape_matching()
            elif match_method == "feature":
                result = self._feature_matching()
            else:
                messagebox.showerror("错误", f"不支持的匹配方式: {match_method}")
                return

            if result:
                self.match_result = result

                # 使用原始图像绘制匹配结果
                if self.img_original is not None:
                    result_img = self._draw_match_result(self.img_original.copy(), result)
                else:
                    result_img = self._draw_match_result(self.img_src.copy(), result)

                self._display_image(result_img)

                self.result_text.insert(tk.END, f"✅ {match_method}匹配成功!\n")
                self.result_text.insert(tk.END, f"位置: {result['center']}\n")
                self.result_text.insert(tk.END, f"匹配度: {result.get('score', 'N/A'):.3f}\n")
                if 'angle' in result:
                    self.result_text.insert(tk.END, f"角度: {result['angle']:.1f}°\n")
            else:
                self.result_text.insert(tk.END, f"❌ {match_method}匹配失败\n")
                # 匹配失败时显示原始图像
                if self.img_original is not None:
                    self._display_image(self.img_original)

        except Exception as e:
            messagebox.showerror("错误", f"执行匹配失败: {str(e)}")

    def _on_mouse_down(self, event):
        """鼠标按下事件"""
        if self.img_src is not None:
            self.roi_start = (event.x, event.y)
            self.is_drawing_roi = True
            # 删除之前的ROI框
            if self.roi_rect_id:
                self.canvas.delete(self.roi_rect_id)

    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if self.is_drawing_roi and self.roi_start:
            # 删除之前的临时ROI框
            if self.roi_rect_id:
                self.canvas.delete(self.roi_rect_id)

            # 绘制新的ROI框
            self.roi_rect_id = self.canvas.create_rectangle(
                self.roi_start[0], self.roi_start[1],
                event.x, event.y,
                outline="red", width=2
            )

    def _on_mouse_up(self, event):
        """鼠标释放事件"""
        if self.is_drawing_roi and self.roi_start:
            self.roi_end = (event.x, event.y)
            self.is_drawing_roi = False

            # 将画布坐标转换为原图坐标
            x1_c, y1_c = self.roi_start
            x2_c, y2_c = self.roi_end
            off_x, off_y = self.display_offset
            scale = self.display_scale if self.display_scale else 1.0
            x1 = int((x1_c - off_x) / scale)
            y1 = int((y1_c - off_y) / scale)
            x2 = int((x2_c - off_x) / scale)
            y2 = int((y2_c - off_y) / scale)

            # 限制在图像范围内
            if self.img_src is not None:
                h_img, w_img = self.img_src.shape[:2]
                x1 = max(0, min(w_img-1, x1))
                x2 = max(0, min(w_img-1, x2))
                y1 = max(0, min(h_img-1, y1))
                y2 = max(0, min(h_img-1, y2))

            roi_x = min(x1, x2)
            roi_y = min(y1, y2)
            roi_w = abs(x2 - x1)
            roi_h = abs(y2 - y1)

            print(f"[DEBUG] ROI 原图坐标: ({roi_x},{roi_y},{roi_w},{roi_h})")

            if roi_w > 10 and roi_h > 10:  # 最小ROI尺寸
                self.result_text.insert(tk.END, f"✅ ROI已选择: ({roi_x}, {roi_y}) {roi_w}x{roi_h}\n")

                # 保存ROI信息
                self.template_roi = {
                    "x": roi_x,
                    "y": roi_y,
                    "width": roi_w,
                    "height": roi_h,
                    "center": (roi_x + roi_w//2, roi_y + roi_h//2)
                }
            else:
                self.result_text.insert(tk.END, "❌ ROI太小，请重新选择\n")

    def _template_matching(self):
        """模板匹配"""
        try:
            # 转换为灰度图
            if len(self.img_src.shape) == 3:
                gray = cv2.cvtColor(self.img_src, cv2.COLOR_BGR2GRAY)
            else:
                gray = self.img_src

            # 执行模板匹配
            result = cv2.matchTemplate(gray, self.template_img, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            match_threshold = self.param_vars["match_threshold"].get()

            if max_val >= match_threshold:
                h, w = self.template_img.shape
                match_center = (max_loc[0] + w // 2, max_loc[1] + h // 2)

                return {
                    "center": match_center,
                    "len_x": w,
                    "len_y": h,
                    "angle": 0,
                    "score": max_val,
                    "method": "template"
                }
            return None

        except Exception as e:
            print(f"模板匹配错误: {e}")
            return None

    def _shape_matching(self):
        """形状匹配"""
        try:
            # 转换为灰度图
            if len(self.img_src.shape) == 3:
                gray = cv2.cvtColor(self.img_src, cv2.COLOR_BGR2GRAY)
            else:
                gray = self.img_src

            # 提取模板轮廓 - 使用更宽松的参数
            template_edges = cv2.Canny(self.template_img, 30, 100)
            template_contours, _ = cv2.findContours(template_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not template_contours:
                print("模板中未找到轮廓")
                return None

            template_contour = max(template_contours, key=cv2.contourArea)
            template_area = cv2.contourArea(template_contour)
            print(f"模板轮廓面积: {template_area}")

            # 提取图像轮廓
            img_edges = cv2.Canny(gray, 30, 100)
            img_contours, _ = cv2.findContours(img_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            print(f"图像中找到 {len(img_contours)} 个轮廓")

            best_match = None
            best_score = 0
            match_threshold = self.param_vars["match_threshold"].get()

            # 降低匹配阈值，更容易匹配
            shape_threshold = min(match_threshold, 0.3)

            for i, contour in enumerate(img_contours):
                contour_area = cv2.contourArea(contour)
                if contour_area < 500:  # 过滤太小的轮廓
                    continue

                # 计算形状匹配度
                try:
                    shape_diff = cv2.matchShapes(template_contour, contour, cv2.CONTOURS_MATCH_I1, 0)
                    score = 1.0 / (1.0 + shape_diff * 10)  # 调整评分公式

                    print(f"轮廓 {i}: 面积={contour_area:.0f}, 形状差异={shape_diff:.3f}, 得分={score:.3f}")

                    if score > best_score and score >= shape_threshold:
                        best_score = score
                        # 计算轮廓中心
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])

                            # 计算轮廓的最小外接矩形（带角度）
                            rect = cv2.minAreaRect(contour)
                            angle = rect[2]  # 角度

                            # 调整角度到合理范围
                            if angle < -45:
                                angle += 90

                            # 用minAreaRect的宽高
                            best_match = {
                                "center": (cx, cy),
                                "len_x": rect[1][0],
                                "len_y": rect[1][1],
                                "angle": angle,
                                "score": score,
                                "method": "shape",
                                "contour": contour  # 保存轮廓用于绘制
                            }
                            print(f"找到更好的匹配: 中心({cx}, {cy}), 得分={score:.3f}")

                except Exception as e:
                    print(f"轮廓匹配错误: {e}")
                    continue

            if best_match:
                print(f"最终匹配结果: {best_match}")
            else:
                print(f"未找到匹配，最高得分: {best_score:.3f}, 阈值: {shape_threshold:.3f}")

            return best_match

        except Exception as e:
            print(f"形状匹配错误: {e}")
            return None

    def _feature_matching(self):
        """特征匹配"""
        try:
            # 转换为灰度图
            if len(self.img_src.shape) == 3:
                gray = cv2.cvtColor(self.img_src, cv2.COLOR_BGR2GRAY)
            else:
                gray = self.img_src

            # 创建ORB特征检测器
            orb = cv2.ORB_create()

            # 检测模板特征点
            kp1, des1 = orb.detectAndCompute(self.template_img, None)
            if des1 is None:
                return None

            # 检测图像特征点
            kp2, des2 = orb.detectAndCompute(gray, None)
            if des2 is None:
                return None

            # 特征匹配
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches = bf.match(des1, des2)

            if len(matches) < 10:  # 需要足够的匹配点
                return None

            # 按距离排序
            matches = sorted(matches, key=lambda x: x.distance)
            good_matches = matches[:int(len(matches) * 0.3)]  # 取前30%的匹配

            if len(good_matches) < 4:
                return None

            # 计算匹配点的中心位置
            src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

            # 计算平均位置作为匹配中心
            center_x = np.mean(dst_pts[:, 0, 0])
            center_y = np.mean(dst_pts[:, 0, 1])

            # 计算匹配度（基于匹配点数量和距离）
            avg_distance = np.mean([m.distance for m in good_matches])
            score = len(good_matches) / (1 + avg_distance / 100)  # 简单的评分公式
            score = min(score / 50, 1.0)  # 归一化到0-1

            match_threshold = self.param_vars["match_threshold"].get()

            if score >= match_threshold:
                h, w = self.template_img.shape
                return {
                    "center": (int(center_x), int(center_y)),
                    "len_x": w,
                    "len_y": h,
                    "angle": 0,
                    "score": score,
                    "method": "feature",
                    "matches": len(good_matches)
                }

            return None

        except Exception as e:
            print(f"特征匹配错误: {e}")
            return None

    def _draw_match_result(self, img, match_result):
        """绘制匹配结果"""
        try:
            center = match_result["center"]
            len_x = match_result["len_x"]
            len_y = match_result["len_y"]
            angle = match_result.get("angle", 0)
            method = match_result.get("method", "unknown")

            # 绘制中心点
            cv2.circle(img, center, 5, (0, 255, 0), -1)

            if method == "shape" and "contour" in match_result:
                # 形状匹配：绘制轮廓和旋转矩形
                contour = match_result["contour"]

                # 绘制轮廓
                cv2.drawContours(img, [contour], -1, (0, 255, 0), 2)

                # 绘制最小外接矩形
                rect = cv2.minAreaRect(contour)
                box = cv2.boxPoints(rect)
                box = np.array(box, dtype=np.int32)
                cv2.drawContours(img, [box], 0, (255, 0, 0), 2)

                # 绘制角度指示线
                center_x, center_y = center
                angle_rad = np.radians(angle)
                line_length = 50
                end_x = int(center_x + line_length * np.cos(angle_rad))
                end_y = int(center_y + line_length * np.sin(angle_rad))
                cv2.arrowedLine(img, center, (end_x, end_y), (255, 255, 0), 2)

            else:
                # 模板匹配和特征匹配：绘制矩形框
                half_x, half_y = len_x // 2, len_y // 2

                if angle != 0:
                    # 绘制旋转矩形
                    angle_rad = np.radians(angle)
                    cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)

                    corners = []
                    for x, y in [(-half_x, -half_y), (half_x, -half_y), (half_x, half_y), (-half_x, half_y)]:
                        rx = x * cos_a - y * sin_a + center[0]
                        ry = x * sin_a + y * cos_a + center[1]
                        corners.append((int(rx), int(ry)))

                    pts = np.array(corners, np.int32).reshape((-1, 1, 2))
                    cv2.polylines(img, [pts], True, (0, 255, 0), 2)
                else:
                    # 绘制普通矩形
                    x1 = center[0] - half_x
                    y1 = center[1] - half_y
                    x2 = center[0] + half_x
                    y2 = center[1] + half_y
                    cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 绘制文本信息
            text = f"{method}: {match_result.get('score', 0):.3f}"
            if angle != 0:
                text += f" {angle:.1f}°"

            cv2.putText(img, text, (center[0] + 10, center[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.2, (0, 255, 0), 1)

            return img

        except Exception as e:
            print(f"绘制匹配结果错误: {e}")
            return img


# 测试代码
if __name__ == "__main__":
    # 创建模拟插件实例进行测试
    class MockPlugin:
        def __init__(self):
            self.params = {
                "measurement_type": "cross_caliper",
                "scan_lines": 24,
                "line_width": 10,
                "edge_threshold": 5,
                "polarity": "BOTH",
                "roi_expand_ratio": 1.1,
                "angle_method": "shape_match",
                "debug_draw": True,
                "debug_log": True,
                "show_roi_box": True,
            }
        
        def process(self, img, ctx):
            # 调用真正的测量算法
            try:
                import sys
                import os

                # 添加项目根目录到路径
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.join(current_dir, '..', '..', '..')
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)

                # 导入真正的插件
                from plugins.geometry_tools.integrated_geometry_tool_plugin import IntegratedGeometryToolPlugin

                # 创建真正的插件实例
                real_plugin = IntegratedGeometryToolPlugin()
                real_plugin.params = self.params.copy()

                print(f"🔧 MockPlugin调用真实插件，测量类型: {self.params.get('measurement_type')}, debug_log={self.params.get('debug_log')}, show_roi_box={self.params.get('show_roi_box')}")

                # 调用真正的process方法
                return real_plugin.process(img, ctx)

            except Exception as e:
                print(f"❌ MockPlugin调用真实插件失败: {e}")
                import traceback
                traceback.print_exc()
                return img, {"measurement_result": {"ok": False, "error": str(e)}}
        
        def save_config(self, file_path):
            print(f"保存配置到: {file_path}")
        
        def load_config(self, file_path):
            print(f"从文件加载配置: {file_path}")
    
    root = tk.Tk()
    root.withdraw()
    
    mock_plugin = MockPlugin()
    ui = IntegratedGeometryToolUI(mock_plugin)
    ui.show()
    
    root.mainloop()
