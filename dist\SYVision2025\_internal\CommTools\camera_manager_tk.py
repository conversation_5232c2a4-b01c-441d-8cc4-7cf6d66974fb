# ====== 环境变量设置（必须在所有导入之前） ======
import os
import sys

# 设置环境变量避免Qt冲突 - 必须在导入任何可能触发Qt的模块之前
os.environ['MPLBACKEND'] = 'Agg'
os.environ.pop('QT_API', None)
os.environ.pop('PYQT_API', None)
os.environ.pop('PYQT5_API', None)
os.environ.pop('PYQT6_API', None)

# 禁用matplotlib的Qt后端
os.environ['MPLBACKEND'] = 'Agg'
# 强制matplotlib使用Agg后端
import matplotlib
matplotlib.use('Agg', force=True)

# ====== 标准库与第三方库导入 ======
# 先将 core 目录加入 sys.path，再导入自定义模块
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_DIR not in sys.path:
    sys.path.insert(0, BASE_DIR)

# 导入路径管理模块
from path_manager import get_config_path
import tkinter as tk
from tkinter import ttk, messagebox
import logging
from core.camera_params import CameraParams
try:
    from .param_panel import ParamPanel  # 包内导入
except ImportError:
    # 脚本直接运行：同级目录
    from param_panel import ParamPanel
logging.getLogger('camera_hikvision').setLevel(logging.DEBUG)

# ====== 布局参数定义 ======
LEFT_FRAME_WIDTH = 240
RIGHT_FRAME_WIDTH = 185
PREVIEW_WIDTH = 520
PREVIEW_HEIGHT = 390
DEVICE_LISTBOX_WIDTH = 28

# ====== SDK加载策略 ======
# 注意：不在模块级别加载SDK，避免与主程序的SDK加载冲突
# SDK将在需要时通过core.camera_hikvision模块间接使用
print("[CameraManagerTK] 跳过模块级SDK加载，将复用主程序已加载的SDK")

# ====== 配置文件路径 ======
CONFIG_PATH = get_config_path('camera_config.yaml')
CONFIG_DIR = os.path.dirname(CONFIG_PATH)

# 确保config目录存在
if not os.path.exists(CONFIG_DIR):
    os.makedirs(CONFIG_DIR, exist_ok=True)
    print(f"创建配置目录：{CONFIG_DIR}")

# 如果配置文件不存在，创建一个默认配置
if not os.path.exists(CONFIG_PATH):
    try:
        import yaml
        default_config = {
            'cameras': [
                {
                    'name': '相机1',
                    'type': 'hikvision',
                    'serial': '',  # 留空表示使用第一个可用相机
                    'parameters': {}
                }
            ]
        }
        with open(CONFIG_PATH, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        print(f"创建默认配置文件：{CONFIG_PATH}")
    except Exception as e:
        print(f"创建默认配置文件失败：{e}")

# ====== 依赖检查与调试信息（仅main中输出，避免污染全局） ======
if __name__ == "__main__":
    print("当前解释器:", sys.executable)
    print("sys.path:", sys.path)
    try:
        import yaml
        print("PyYAML路径:", yaml.__file__)
    except Exception as e:
        print("导入yaml失败:", e)
        import traceback
        traceback.print_exc()
        messagebox.showerror('环境或依赖错误', f'导入yaml失败：{e}\n请检查sy_vision环境和PyYAML安装！')
        sys.exit(1)

class CameraManagerTk(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title('海康工业相机管理工具 (Tkinter版)')
        total_width = LEFT_FRAME_WIDTH + PREVIEW_WIDTH + RIGHT_FRAME_WIDTH + 60
        total_height = max(PREVIEW_HEIGHT + 40, 600)
        self.geometry(f'{total_width}x{total_height}')
        self.minsize(total_width, total_height)
        self.devices = []
        self.current_idx = None
        self.cam_select_var = tk.StringVar()
        # param_vars 由 ParamPanel 统一维护，初始化为空占位
        self.param_vars = {}
        self.previewing = False
        # 保持对预览图像的强引用，防止被垃圾回收
        self.preview_img = None
        # 摄像机类型变量
        self.type_var = tk.StringVar()
        self.status_var = tk.StringVar(value='状态: 未连接')
        # 延迟初始化CameraManager，避免在构造函数中导入可能有问题的模块
        self.cam_manager = None
        self._init_camera_manager()
        self.create_widgets()
        self.enum_devices()
        self.update_btn_state(init=True)

    def _init_camera_manager(self):
        """延迟初始化CameraManager，避免在构造函数中导入可能有问题的模块"""
        try:
            from core.camera_manager import CameraManager
            import yaml

            with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            camera_configs = config.get('cameras', []) if config else []
            self.cam_manager = CameraManager(camera_configs)
            print("[CameraManagerTK] CameraManager初始化成功")
        except Exception as e:
            print(f"[CameraManagerTK] CameraManager初始化失败: {e}")
            messagebox.showerror('配置加载失败', f'加载相机配置失败: {e}')
            self.cam_manager = None

    def create_widgets(self):
        # ======= 三栏布局 =======
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=0, minsize=LEFT_FRAME_WIDTH)
        self.grid_columnconfigure(1, weight=1, minsize=PREVIEW_WIDTH)
        self.grid_columnconfigure(2, weight=0, minsize=RIGHT_FRAME_WIDTH)

        # 左侧设备列表
        self.left_frame = tk.Frame(self, width=LEFT_FRAME_WIDTH)
        self.left_frame.grid(row=0, column=0, sticky='nswe', padx=(10,0), pady=10)
        self.device_listbox = tk.Listbox(self.left_frame, width=DEVICE_LISTBOX_WIDTH, height=20)
        self.device_listbox.pack(fill=tk.BOTH, expand=True, padx=4, pady=4)
        self.device_listbox.bind('<<ListboxSelect>>', self.on_cam_selected)
        self.refresh_btn = tk.Button(self.left_frame, text='刷新设备', command=self.enum_devices)
        self.refresh_btn.pack(pady=5)

        # 预览区（含顶部控制条）
        self.center_outer_frame = tk.Frame(self)
        self.center_outer_frame.grid(row=0, column=1, sticky='nsew', padx=5, pady=10)
        # 顶部控制条
        top_control_frame = tk.Frame(self.center_outer_frame, bg='#f0f0f0', height=36)
        top_control_frame.pack(fill=tk.X, side=tk.TOP)
        top_control_frame.pack_propagate(False)
        # -------- 设备选择下拉框 --------
        self.cam_select_combo = ttk.Combobox(top_control_frame, textvariable=self.cam_select_var,
                                             state='readonly', width=18)
        self.cam_select_combo.pack(side=tk.LEFT, padx=8, pady=6)
        self.cam_select_combo.bind('<<ComboboxSelected>>', self.on_cam_selected)
        # 预览按钮
        self.preview_btn = tk.Button(top_control_frame, text='开始预览', command=self.toggle_preview, width=10)
        self.preview_btn.pack(side=tk.RIGHT, padx=8, pady=6)
        # 预览画面区
        self.center_frame = tk.Frame(self.center_outer_frame, bg='#222')
        self.center_frame.pack(fill=tk.BOTH, expand=True, pady=(6,0))
        self.center_frame.grid_rowconfigure(0, weight=1)
        self.center_frame.grid_columnconfigure(0, weight=1)
        # 使用Label显示图像，避免Canvas的PhotoImage垃圾回收问题
        self.preview_label = tk.Label(self.center_frame, bg='#222', text="无图像", fg='white', font=('微软雅黑', 12))
        self.preview_label.grid(row=0, column=0, sticky='nsew')
        self.preview_img = None
        self._image_refs = []  # 保存图像引用防止垃圾回收


        # 右侧参数与操作区
        self.right_frame = tk.Frame(self, width=RIGHT_FRAME_WIDTH, bg='#f8f8f8')
        self.right_frame.grid(row=0, column=2, sticky='nswe', padx=(8,12), pady=10)
        self.right_frame.grid_propagate(False)
        tk.Label(self.right_frame, text='参数设置', bg='#f8f8f8').pack(anchor='w', padx=8, pady=(4,6))

        # ----- 参数面板 -----
        # 使用封装后的 ParamPanel 简化逻辑
        print("[DEBUG] 创建参数面板...")
        self.param_panel = ParamPanel(self.right_frame, width=RIGHT_FRAME_WIDTH)
        self.param_panel.pack(fill='both', expand=True, pady=(0, 4))
        # 让旧代码的 self.param_vars 指向新的变量字典，保持兼容
        self.param_vars = self.param_panel.param_vars
        print(f"[DEBUG] 参数面板创建完成，参数变量数量: {len(self.param_vars)}")

        # ----- 基本信息输入框 -----
        info_frame = tk.LabelFrame(self.right_frame, text='摄像机信息', bg='#f8f8f8')
        info_frame.pack(fill=tk.X, padx=8, pady=(0, 6))
        # 编号(ID)
        tk.Label(info_frame, text='编号(ID):', bg='#f8f8f8').grid(row=0, column=0, sticky='e', padx=2, pady=2)
        self.id_entry = tk.Entry(info_frame, width=18)
        self.id_entry.grid(row=0, column=1, sticky='w', padx=2, pady=2)
        # 描述
        tk.Label(info_frame, text='描述:', bg='#f8f8f8').grid(row=1, column=0, sticky='e', padx=2, pady=2)
        self.desc_entry = tk.Entry(info_frame, width=18)
        self.desc_entry.grid(row=1, column=1, sticky='w', padx=2, pady=2)
        # 类型
        tk.Label(info_frame, text='类型:', bg='#f8f8f8').grid(row=2, column=0, sticky='e', padx=2, pady=2)
        self.type_entry = tk.Entry(info_frame, textvariable=self.type_var, width=18)
        self.type_entry.grid(row=2, column=1, sticky='w', padx=2, pady=2)

        # ====== 底部按钮区（连接/保存等） -----
        self.bottom_frame = tk.Frame(self.right_frame, bg='#f8f8f8')
        self.bottom_frame.pack(fill=tk.X, pady=10, padx=6)
        self.status_label = tk.Label(self.bottom_frame, textvariable=self.status_var, bg='#f8f8f8')
        self.status_label.pack(anchor='w', padx=2)
        btn_row = tk.Frame(self.bottom_frame, bg='#f8f8f8')
        btn_row.pack(anchor='w', pady=2)
        self.connect_btn = tk.Button(btn_row, text='连接', command=self.connect_camera, width=6)
        self.connect_btn.pack(side=tk.LEFT, padx=2)
        self.disconnect_btn = tk.Button(btn_row, text='断开', command=self.disconnect_camera, width=6)
        self.disconnect_btn.pack(side=tk.LEFT, padx=2)

        # 第二行按钮
        btn_row2 = tk.Frame(self.bottom_frame, bg='#f8f8f8')
        btn_row2.pack(anchor='w', pady=2)
        self.apply_btn = tk.Button(btn_row2, text='应用参数', command=self.apply_params, width=8)
        self.apply_btn.pack(side=tk.LEFT, padx=2)
        self.save_btn = tk.Button(btn_row2, text='保存配置', command=self.save_config, width=12)
        self.save_btn.pack(side=tk.LEFT, padx=2)

    def update_btn_state(self, init=False):
        # 按钮状态随连接状态动态切换
        if self.current_idx is None or not self.devices:
            self.connect_btn.config(state=tk.NORMAL)
            self.disconnect_btn.config(state=tk.NORMAL)
            self.save_btn.config(state=tk.DISABLED)
            self.apply_btn.config(state=tk.DISABLED)
            if hasattr(self, 'preview_btn'):
                self.preview_btn.config(state=tk.DISABLED)
            return
        info = self.devices[self.current_idx]
        # 连接、断开按钮始终可用
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.NORMAL)
        # 其余按钮逻辑保持不变
        self.save_btn.config(state=tk.NORMAL)
        self.apply_btn.config(state=tk.NORMAL)
        # 预览按钮始终可用，由点击时判断连接状态


    def enum_devices(self):
        # === 通过CameraHikvision枚举物理设备，结合配置文件显示自定义名称 ===
        import yaml
        try:
            from core.camera_hikvision import CameraHikvision
        except Exception as e:
            print(f"[CameraManagerTK] 导入CameraHikvision失败: {e}")
            self.device_listbox.insert(tk.END, f'导入相机模块失败: {e}')
            self.update_btn_state()
            return
        # 加载配置，建立SN到自定义名称（id）映射
        try:
            with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            sn2id = {}
            if isinstance(config, dict) and 'cameras' in config:
                for cam in config['cameras']:
                    sn = cam.get('serial', '').replace('\0', '').strip()
                    if sn:
                        sn2id[sn] = cam.get('id', '未命名')
        except Exception:
            sn2id = {}
        self.devices = []
        self.device_listbox.delete(0, tk.END)
        try:
            device_list = CameraHikvision.enum_devices()
        except Exception as e:
            self.device_listbox.insert(tk.END, f'枚举设备失败: {e}')
            self.update_btn_state()
            return
        if not device_list:
            self.device_listbox.insert(tk.END, '未检测到任何相机设备！')
            self.update_btn_state()
            return
        for idx, info in enumerate(device_list):
            sn = info.get('sn', '').replace('\0', '').strip()
            cam_id = sn2id.get(sn, '未命名')
            info['id'] = cam_id
            info['idx'] = idx  # 记录物理设备在枚举列表中的索引
            # UI只显示“自定义名称 | 类型 | IP”
            self.device_listbox.insert(tk.END, f"{cam_id} | {info.get('type','')} | {info.get('ip','')}")
            self.devices.append(info)
        # 枚举完成后自动设置下拉框选项和默认选中项
        id_list = [d['id'] for d in self.devices]
        if hasattr(self, 'cam_select_combo'):
            self.cam_select_combo['values'] = id_list
        if id_list:
            self.cam_select_var.set(id_list[0])
            self.device_listbox.selection_set(0)
            self.current_idx = 0
            # 自动填充编号输入框
            self.id_entry.delete(0, tk.END)
            self.id_entry.insert(0, id_list[0])
            # 自动填充类型
            cam_type = self.devices[0].get('type', '')
            if cam_type:
                self.type_var.set(cam_type)
            else:
                self.type_var.set('')
            # 加载上次保存的参数文件并填充
            from os.path import dirname, join, exists
            config_dir = dirname(CONFIG_PATH)
            param_file = self.devices[0].get('param_file', '')
            param_path = join(config_dir, param_file) if param_file else ''
            if param_path and exists(param_path):
                try:
                    with open(param_path, 'r', encoding='utf-8') as f:
                        pdata = yaml.safe_load(f) or {}
                    self.param_panel.set_params(pdata)
                except Exception as e:
                    print(f"[UI] 加载参数文件失败: {e}")
        else:
            self.cam_select_var.set('')
            self.type_var.set('')
            self.current_idx = None
        self.update_btn_state()


    def on_cam_selected(self, event):
        # 判断事件来源：Listbox 或 Combobox
        if event.widget == self.cam_select_combo:
            cam_id = self.cam_select_var.get()
            self.current_idx = next((i for i, d in enumerate(self.devices) if d['id'] == cam_id), None)
            if self.current_idx is None:
                return
            # 同步高亮 Listbox
            self.device_listbox.selection_clear(0, tk.END)
            self.device_listbox.selection_set(self.current_idx)
        else:
            sel = self.device_listbox.curselection()
            if not sel:
                return
            self.current_idx = sel[0]
        info = self.devices[self.current_idx]
        self.status_var.set(f"状态: 已选择 {info['id']}")
        # 填充编号/描述/类型输入框
        self.id_entry.delete(0, tk.END)
        self.id_entry.insert(0, info.get('id', ''))
        self.desc_entry.delete(0, tk.END)
        self.desc_entry.insert(0, info.get('desc', ''))
        self.type_var.set(info.get('type', ''))
        try:
            cam_id = info['id']
            cam = self.cam_manager.get_camera(cam_id)
            if cam and cam.opened:
                params = cam.get_params()
                cam_params = CameraParams.from_dict(params)
                self.param_panel.set_params(cam_params)
            else:
                # 相机未连接，尝试从本地参数文件加载
                from os.path import dirname, join, exists
                config_dir = dirname(CONFIG_PATH)
                param_file = info.get('param_file', '')
                param_path = join(config_dir, param_file) if param_file else ''
                if param_path and exists(param_path):
                    try:
                        with open(param_path, 'r', encoding='utf-8') as f:
                            pdata = yaml.safe_load(f) or {}
                        self.param_panel.set_params(pdata)
                    except Exception as e:
                        print(f"[UI] on_cam_selected 加载参数失败: {e}")
        except Exception as e:
            print(f"[UI] on_cam_selected 填充参数异常: {e}")


    def connect_camera(self):
        print("[UI] connect_camera: 入口，current_idx=", self.current_idx)
        if self.current_idx is None:
            print("[UI] connect_camera: 未选择设备，直接返回")
            messagebox.showinfo('提示', '请先选择一个设备！')
            return
        info = self.devices[self.current_idx]
        print(f"[UI] connect_camera: 选中设备 info={info}")
        if info.get('connected'):
            print("[UI] connect_camera: 设备已连接，直接返回")
            messagebox.showinfo('提示', '该相机已连接！')
            return
        cam_id = info['id']
        idx = info.get('idx', None)
        try:
            print(f"[UI] connect_camera: 获取cam_manager.get_camera({cam_id}) ...")
            cam_obj = self.cam_manager.get_camera(cam_id)
            print(f"[UI] connect_camera: cam_obj={cam_obj}")
            if cam_obj is None:
                print("[UI] connect_camera: cam_obj为None，抛出异常")
                raise RuntimeError('找不到该相机配置，请检查配置文件')
            # 传递物理设备idx给驱动层，确保连接的是UI选中的物理设备
            print(f"[UI] connect_camera: 调用cam_obj.open(physical_idx={idx}) ...")
            if hasattr(cam_obj, 'open'):
                cam_obj.open(physical_idx=idx)
                print(f"[UI] connect_camera: open调用成功")
            else:
                print(f"[UI] connect_camera: cam_obj不支持open(physical_idx)接口")
                raise RuntimeError('相机对象不支持open(physical_idx)接口')
            info['connected'] = True
            self.status_var.set('状态: 已连接')
            print("[UI] connect_camera: 连接成功")
        except Exception as e:
            info['connected'] = False
            self.status_var.set('状态: 连接失败')
            print(f"[UI] connect_camera: 连接异常: {e}")
            messagebox.showerror('连接失败', f'相机连接失败：{e}')
        finally:
            print("[UI] connect_camera: 调用update_btn_state()，流程结束")
            self.update_btn_state()


    def disconnect_camera(self):
        if self.current_idx is None:
            return
        info = self.devices[self.current_idx]
        if info.get('connected'):
            # TODO: 实现相机断开逻辑
            info['connected'] = False
        self.status_var.set('状态: 已断开')
        self.update_btn_state()

    def apply_params(self):
        """应用当前参数到相机（不保存到文件）"""
        if self.current_idx is None:
            messagebox.showwarning('警告', '请先选择一个设备')
            return
        info = self.devices[self.current_idx]
        cam_id = info['id']
        # 从界面读取参数
        param_obj = self.param_panel.get_params()
        # 直接应用到相机
        self._apply_params_to_cam(cam_id, param_obj)
        messagebox.showinfo('应用成功', '参数已应用到相机')

    def save_config(self):
        i = self.current_idx
        if i is None:
            messagebox.showinfo('提示', '请先选择一个设备！')
            return
        info = self.devices[i]
        info['id'] = self.id_entry.get()
        info['desc'] = self.desc_entry.get()
        info['type'] = self.type_var.get()
        param_obj = self.param_panel.get_params()
        # 使用相机id作为参数文件名，避免混淆
        cam_id = info['id']
        param_file = f"{cam_id}_config.yaml"
        param_path = os.path.join(os.path.dirname(CONFIG_PATH), param_file)
        with open(param_path, 'w', encoding='utf-8') as f:
            yaml.dump(param_obj.to_dict(), f, allow_unicode=True)
        info['param_file'] = param_file
        # 主配置只保存设备基本信息和参数文件路径
        cameras = []
        for dev in self.devices:
            cameras.append({
                'id': dev['id'],
                'brand': dev.get('brand', 'hikvision'),
                'type': dev['type'],
                'serial': dev['sn'],
                'description': dev.get('desc', ''),
                'param_file': dev.get('param_file', '')
            })
        config = {'cameras': cameras}
        with open(CONFIG_PATH, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, allow_unicode=True)
        # 将参数立即写入相机
        self._apply_params_to_cam(cam_id, param_obj)
        messagebox.showinfo('保存成功', f'参数已保存并同步到相机\n文件: {param_file}')

    def _apply_params_to_cam(self, cam_id, params):
        """将参数字典写入指定相机"""
        from tkinter import messagebox
        try:
            cam = self.cam_manager.get_camera(cam_id)
            if cam is None:
                print(f"[UI] _apply_params_to_cam: 未找到相机对象 {cam_id}")
                return

            # 如果传入的是 dict，转换
            if isinstance(params, dict):
                params = CameraParams.from_dict(params)

            # -------- 预览状态处理 --------
            was_previewing = self.previewing
            if was_previewing:
                self.toggle_preview()  # 停止预览，防止主线程阻塞
                self.update()

            # 直接调用 cam.apply_params
            cam.apply_params(params)
            print(f"[UI] _apply_params_to_cam: 参数已写入 {cam_id}")

            # -------- 恢复预览或提示 --------
            if params.TriggerSource.startswith('线路'):
                # 硬触发：保持暂停，提示用户
                if was_previewing:
                    messagebox.showinfo('提示', '已切换为硬触发模式，预览已停止。\n外部触发后可点击“开始预览”查看单帧。')
            else:
                if was_previewing:
                    self.toggle_preview()  # 恢复连续/软触发预览
        except Exception as e:
            print(f"[UI] _apply_params_to_cam: 异常: {e}")

    # 覆盖原 set_params/read_params 按钮函数为兼容占位
    def set_params(self):
        messagebox.showinfo('提示', '已合并到“保存配置”按钮，无需单独操作！')

    def read_params(self):
        messagebox.showinfo('提示', '已合并到“保存配置”按钮，无需单独操作！')


    def toggle_preview(self):
        print(f"[UI] toggle_preview: 按钮被点击，当前previewing={self.previewing}")
        try:
            if not self.previewing:
                print(f"[UI] toggle_preview: 尝试进入预览模式，current_idx={self.current_idx}, devices={len(self.devices)}")
                if self.current_idx is None or not self.devices:
                    print("[UI] toggle_preview: 未选择设备，弹窗提示")
                    messagebox.showinfo('提示', '请先选择并连接相机！')
                    return
                info = self.devices[self.current_idx]
                if not info.get('connected'):
                    print("[UI] toggle_preview: 设备未连接，弹窗提示")
                    messagebox.showinfo('提示', '请先连接相机！')
                    return
                # 启动相机采集
                cam_obj = self.cam_manager.get_camera(info['id'])
                if cam_obj is not None:
                    cam_obj.start_grab(continuous=not cam_obj.params.get('trigger_enabled', False))
                print("[UI] toggle_preview: 进入预览模式，切换按钮文字")
                self.previewing = True
                self.preview_btn.config(text='停止预览')
                self.after(100, self.update_preview)
            else:
                print("[UI] toggle_preview: 停止预览，切换按钮文字")
                self.previewing = False
                # 停止相机采集
                if self.current_idx is not None and self.current_idx < len(self.devices):
                    cam_id_stop = self.devices[self.current_idx]['id']
                    cam_obj_stop = self.cam_manager.get_camera(cam_id_stop)
                    if cam_obj_stop is not None:
                        cam_obj_stop.stop_grab()
                self.preview_btn.config(text='开始预览')
                self.preview_label.config(image="", text="预览已停止")
                self.preview_img = None
                self._image_refs.clear()  # 清空图像引用
        except Exception as e:
            print(f"[UI] toggle_preview: 异常: {e}")


    def update_preview(self):
        if not self.previewing:
            print("[UI] update_preview: 非预览状态，直接返回")
            return
        try:
            print(f"[UI] update_preview: 入口，current_idx={self.current_idx}, devices={len(self.devices)}")
            if self.current_idx is None or not self.devices:
                print("[UI] update_preview: 未选择设备或设备列表为空，返回")
                return
            info = self.devices[self.current_idx]
            if not info.get('connected'):
                print("[UI] update_preview: 当前设备未连接，返回")
                return
            cam_id = info['id']
            cam_obj = self.cam_manager.get_camera(cam_id)
            if cam_obj is None:
                print(f"[UI] update_preview: 未找到相机对象 cam_id={cam_id}")
                return
            print(f"[UI] update_preview: 调用grab采集图像 cam_id={cam_id}")
            img = cam_obj.grab()
            if img is not None:
                print(f"[UI] update_preview: grab成功，准备显示，img.size={img.size if hasattr(img,'size') else '未知'}")
                from PIL import ImageTk
                w, h = self.preview_label.winfo_width(), self.preview_label.winfo_height()
                print(f"[UI] update_preview: 预览区大小 w={w}, h={h}")
                if w > 0 and h > 0:
                    img = img.resize((w, h))
                    print(f"[UI] update_preview: resize后 img.size={img.size}")
                # 直接从PIL Image创建PhotoImage
                try:
                    new_img = ImageTk.PhotoImage(img)
                    print("[UI] update_preview: PhotoImage创建成功，更新Label")

                    # 保存引用防止垃圾回收
                    self.preview_img = new_img
                    self._image_refs.append(new_img)

                    # 清理旧的图像引用
                    if len(self._image_refs) > 3:
                        self._image_refs.pop(0)

                    # 直接在Label上显示图像
                    self.preview_label.config(image=new_img, text="")

                    print("[UI] update_preview: Label图像已更新")

                except Exception as img_error:
                    print(f"[UI] update_preview: 图像处理异常: {img_error}")
                    # 显示错误信息
                    self.preview_label.config(image="", text=f"图像错误: {str(img_error)}")
            else:
                print("[UI] update_preview: grab返回None，清空画面")
                self.preview_label.config(image="", text="无图像")
                self.preview_img = None
                self._image_refs.clear()  # 清空图像引用
        except Exception as e:
            print(f"[UI] update_preview异常: {e}")
        if self.previewing:
            self.after(50, self.update_preview)


if __name__ == '__main__':
    app = CameraManagerTk()
    app.mainloop()