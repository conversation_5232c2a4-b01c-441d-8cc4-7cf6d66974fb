"""Simple product locator demo.

In real deployment this module should locate product in the image and return
an affine/3×3 homography matrix that maps design coordinates (CAD) to image
pixel coordinates.

Here we provide a placeholder implementation that always returns identity
transform so that auto-placing caliper still works as long as CAD coordinates
are already given in pixel space. Replace `locate` with actual algorithm later.
"""
from __future__ import annotations
import numpy as np
import cv2
from typing import <PERSON>ple


def _detect_main_angle(gray: np.ndarray) -> float:
    """Rudimentary orientation estimation: use largest contour's minAreaRect angle."""
    _, th = cv2.threshold(gray, 0, 255, cv2.THRESH_OTSU | cv2.THRESH_BINARY)
    cnts, _ = cv2.findContours(th, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not cnts:
        return 0.0
    cnt = max(cnts, key=cv2.contourArea)
    rect = cv2.minAreaRect(cnt)  # (center,(w,h),angle)
    angle = rect[2]
    # convert to [-90,90]
    if angle < -45:
        angle += 90
    return angle


def locate(img: np.ndarray) -> <PERSON>ple[np.ndarray, np.ndarray]:
    """Estimate rotation of the main object and return homography H and warped image.

    This simplistic version uses the largest contour angle. For consistent output,
    the object long edge will be horizontal after correction.
    """
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim == 3 else img
    angle = _detect_main_angle(gray)
    h, w = gray.shape[:2]
    center = (w/2, h/2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    warped = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)
    # convert 2x3 to 3x3
    H = np.vstack([M, [0,0,1]]).astype(np.float32)
    return H, warped
