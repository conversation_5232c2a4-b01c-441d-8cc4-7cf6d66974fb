"""Angle measurement core logic.

Multiple modes:
1. Given three image points (p0 vertex, p1, p2), compute angle.
2. Given result from arc_tool (central_angle_rad) for circular arc.
3. Line fitting mode: fit lines through point sets and compute angle.
"""
from __future__ import annotations
from typing import <PERSON><PERSON>, Dict, Any, List
import math
import numpy as np
import cv2

__all__ = [
    'measure_angle_pts',
    'angle_from_arc_result',
    'measure_angle_fitted_lines',
    'fit_line_ransac',
]

def measure_angle_pts(p0: <PERSON>ple[float, float], p1: <PERSON><PERSON>[float, float], p2: <PERSON>ple[float, float]) -> Dict[str, Any]:
    """Return angle (rad & deg) formed by p1-p0-p2 (p0 is vertex)."""
    v1 = np.array(p1, dtype=float) - p0
    v2 = np.array(p2, dtype=float) - p0
    if np.linalg.norm(v1) < 1e-6 or np.linalg.norm(v2) < 1e-6:
        return {'error': 'points too close'}
    cos_a = float(np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2)))
    cos_a = max(-1.0, min(1.0, cos_a))
    rad = math.acos(cos_a)
    return {'angle_rad': rad, 'angle_deg': rad * 180.0 / math.pi}

def angle_from_arc_result(arc_res: Dict[str, Any]) -> Dict[str, Any]:
    """Extract angle from arc_tool.measure_arc result."""
    if 'central_angle_rad' not in arc_res:
        return {'error': 'missing central_angle_rad'}
    rad = float(arc_res['central_angle_rad'])
    return {'angle_rad': rad, 'angle_deg': rad * 180.0 / math.pi}


def fit_line_ransac(points: np.ndarray, ransac_thresh: float = 2.0, iterations: int = 1000) -> Dict[str, Any]:
    """
    使用RANSAC拟合直线

    Parameters:
    -----------
    points : np.ndarray
        点集，形状为 (N, 2)，每行是 (x, y)
    ransac_thresh : float
        RANSAC内点阈值（像素）
    iterations : int
        RANSAC迭代次数

    Returns:
    --------
    Dict containing:
        - 'line_params': (a, b, c) where ax + by + c = 0
        - 'direction': (dx, dy) 单位方向向量
        - 'inliers': 内点索引
        - 'point_on_line': 直线上的一个点
    """
    if len(points) < 2:
        return {'error': 'need at least 2 points'}

    points = np.array(points, dtype=np.float32)

    # 使用OpenCV的fitLine进行RANSAC拟合
    try:
        # fitLine返回 [vx, vy, x0, y0] 其中(vx,vy)是方向向量，(x0,y0)是直线上一点
        line_params = cv2.fitLine(points, cv2.DIST_L2, 0, 0.01, 0.01)
        vx, vy, x0, y0 = line_params.flatten()

        # 计算直线方程 ax + by + c = 0
        # 方向向量 (vx, vy)，法向量 (-vy, vx)
        a, b = -vy, vx
        c = -(a * x0 + b * y0)

        # 计算内点
        distances = np.abs(a * points[:, 0] + b * points[:, 1] + c) / np.sqrt(a*a + b*b)
        inliers = np.where(distances <= ransac_thresh)[0]

        return {
            'line_params': (float(a), float(b), float(c)),
            'direction': (float(vx), float(vy)),
            'point_on_line': (float(x0), float(y0)),
            'inliers': inliers,
            'num_inliers': len(inliers)
        }

    except Exception as e:
        return {'error': f'line fitting failed: {str(e)}'}


def measure_angle_fitted_lines(points1: List[Tuple[float, float]],
                              points2: List[Tuple[float, float]],
                              ransac_thresh: float = 2.0) -> Dict[str, Any]:
    """
    通过拟合两条直线来测量角度

    Parameters:
    -----------
    points1, points2 : List[Tuple[float, float]]
        两组点，用于拟合两条直线
    ransac_thresh : float
        RANSAC拟合阈值

    Returns:
    --------
    Dict containing:
        - 'angle_rad': 角度（弧度）
        - 'angle_deg': 角度（度）
        - 'line1_params': 第一条直线参数
        - 'line2_params': 第二条直线参数
        - 'intersection': 交点坐标（如果存在）
    """
    # 拟合第一条直线
    line1_result = fit_line_ransac(np.array(points1), ransac_thresh)
    if 'error' in line1_result:
        return {'error': f'line1 fitting failed: {line1_result["error"]}'}

    # 拟合第二条直线
    line2_result = fit_line_ransac(np.array(points2), ransac_thresh)
    if 'error' in line2_result:
        return {'error': f'line2 fitting failed: {line2_result["error"]}'}

    # 获取方向向量
    dir1 = np.array(line1_result['direction'])
    dir2 = np.array(line2_result['direction'])

    # 计算角度
    cos_angle = np.dot(dir1, dir2) / (np.linalg.norm(dir1) * np.linalg.norm(dir2))
    cos_angle = np.clip(cos_angle, -1.0, 1.0)
    angle_rad = math.acos(abs(cos_angle))  # 取锐角

    # 计算交点
    a1, b1, c1 = line1_result['line_params']
    a2, b2, c2 = line2_result['line_params']

    # 解方程组求交点
    det = a1 * b2 - a2 * b1
    if abs(det) < 1e-10:
        intersection = None  # 平行线
    else:
        x = (b1 * c2 - b2 * c1) / det
        y = (a2 * c1 - a1 * c2) / det
        intersection = (float(x), float(y))

    return {
        'angle_rad': float(angle_rad),
        'angle_deg': float(angle_rad * 180.0 / math.pi),
        'line1_result': line1_result,
        'line2_result': line2_result,
        'intersection': intersection
    }

