"""Tkinter UI panel for image smoothing/softening.
Standalone UI for creating soft, pleasant visual effects on images.
"""
from __future__ import annotations
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2, numpy as np, yaml, sys, pathlib

ROOT_DIR = pathlib.Path(__file__).resolve().parents[3]  # SY_VISION2025
if str(ROOT_DIR) not in sys.path:
    sys.path.insert(0, str(ROOT_DIR))

CONFIG_DIR = ROOT_DIR / 'configs' / 'smooth'
CONFIG_DIR.mkdir(parents=True, exist_ok=True)

# ----------------- Smooth core -----------------
from plugins.core.image_process.smooth import SmoothProcessor

# ----------------- UI -----------------
class SmoothFrame(tk.Frame):
    METHODS = {
        '高斯模糊': 'gaussian',
        '双边滤波': 'bilateral', 
        '方框滤波': 'box',
        '中值滤波': 'median',
    }

    def __init__(self, master: tk.Widget|None=None, params=None, on_change=None, preview_img=None):
        super().__init__(master)
        if isinstance(master, (tk.Tk, tk.Toplevel)):
            master.geometry('1000x650')
        
        # 初始化参数
        self.params = params or {}
        self.on_change = on_change
        
        self.proc = SmoothProcessor(**self.params)
        self.img_src: np.ndarray|None = preview_img
        self.preview_max = 450
        self._build_ui()
        
        # 如果有预览图像，立即显示
        if self.img_src is not None:
            self._show_image(self.canvas_src, self.img_src, is_src=True)
            self._auto_run()

    def load_image(self, img: 'np.ndarray'):
        """公共接口，供插件自动加载上游图片"""
        self.img_src = img.copy()
        self._show_image(self.canvas_src, self.img_src, is_src=True)
        self._auto_run()

    def _build_ui(self):
        # 左侧参数面板 - 缩小宽度，添加滚动条
        param_canvas = tk.Canvas(self, width=300, height=650)
        param_canvas.grid(row=0, column=0, sticky='nsw', padx=3, pady=3)

        scrollbar = ttk.Scrollbar(self, orient="vertical", command=param_canvas.yview)
        scrollbar.grid(row=0, column=0, sticky='nse')
        param_canvas.configure(yscrollcommand=scrollbar.set)

        param_frm = tk.LabelFrame(param_canvas, text='图像柔化参数', padx=3, pady=3)
        param_canvas.create_window((0, 0), window=param_frm, anchor="nw")

        # 绑定鼠标滚轮到参数面板
        def _on_param_mousewheel(event):
            param_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        param_canvas.bind("<MouseWheel>", _on_param_mousewheel)

        row = 0

        # 平滑方法
        ttk.Label(param_frm, text='平滑方法', width=10).grid(row=row, column=0, sticky='e', pady=1)
        default_method = self.params.get('method', 'gaussian')
        default_cn = next((k for k,v in self.METHODS.items() if v==default_method), '高斯模糊')
        self.method_var = tk.StringVar(value=default_cn)
        method_combo = ttk.Combobox(param_frm, textvariable=self.method_var, values=list(self.METHODS.keys()),
                                   state='readonly', width=12)
        method_combo.grid(row=row, column=1, sticky='w', pady=1, padx=(3,0))
        method_combo.bind('<<ComboboxSelected>>', lambda e: self._delayed_run())
        row += 1

        # 核大小
        self.kernel_var = tk.IntVar(value=self.params.get('kernel_size', 15))
        self.kernel_scale, self.kernel_label = self._create_scale_with_spinbox(
            param_frm, '核大小', self.kernel_var, 3, 51, 2, False, row, ensure_odd=True)
        row += 1

        # 对比度调整
        self.contrast_var = tk.DoubleVar(value=self.params.get('contrast_alpha', 0.8))
        self.contrast_scale, self.contrast_label = self._create_scale_with_spinbox(
            param_frm, '对比度系数', self.contrast_var, 0.1, 2.0, 0.1, True, row)
        row += 1

        # 亮度偏移
        self.brightness_var = tk.IntVar(value=self.params.get('brightness_beta', 20))
        self.brightness_scale, self.brightness_label = self._create_scale_with_spinbox(
            param_frm, '亮度偏移', self.brightness_var, -100, 100, 5, False, row)
        row += 1

        # 启用对比度调整
        self.enable_contrast_var = tk.BooleanVar(value=self.params.get('enable_contrast_adjust', True))
        ttk.Checkbutton(param_frm, text='启用对比度调整', variable=self.enable_contrast_var,
                        command=self._delayed_run).grid(row=row, column=0, columnspan=2, sticky='w', pady=1)
        row += 1

        # 启用形态学处理
        self.enable_morph_var = tk.BooleanVar(value=self.params.get('enable_morph', False))
        ttk.Checkbutton(param_frm, text='启用形态学处理', variable=self.enable_morph_var,
                        command=self._delayed_run).grid(row=row, column=0, columnspan=2, sticky='w', pady=1)
        row += 1

        # 形态学核大小
        self.morph_kernel_var = tk.IntVar(value=self.params.get('morph_kernel_size', 3))
        self.morph_scale, self.morph_label = self._create_scale_with_spinbox(
            param_frm, '形态学核', self.morph_kernel_var, 3, 15, 2, False, row, ensure_odd=True)
        row += 1

        # 分隔线
        ttk.Separator(param_frm, orient='horizontal').grid(row=row, column=0, columnspan=2, sticky='ew', pady=5)
        row += 1

        # 伽马校正
        self.enable_gamma_var = tk.BooleanVar(value=self.params.get('enable_gamma', False))
        ttk.Checkbutton(param_frm, text='启用伽马校正', variable=self.enable_gamma_var,
                        command=self._delayed_run).grid(row=row, column=0, columnspan=2, sticky='w', pady=1)
        row += 1

        self.gamma_var = tk.DoubleVar(value=self.params.get('gamma', 1.0))
        self.gamma_scale, self.gamma_label = self._create_scale_with_spinbox(
            param_frm, '伽马值', self.gamma_var, 0.1, 3.0, 0.1, True, row)
        row += 1

        # 颜色调整
        self.enable_color_var = tk.BooleanVar(value=self.params.get('enable_color_adjust', False))
        ttk.Checkbutton(param_frm, text='启用颜色调整', variable=self.enable_color_var,
                        command=self._delayed_run).grid(row=row, column=0, columnspan=2, sticky='w', pady=1)
        row += 1

        self.saturation_var = tk.DoubleVar(value=self.params.get('saturation', 1.0))
        self.saturation_scale, self.saturation_label = self._create_scale_with_spinbox(
            param_frm, '饱和度', self.saturation_var, 0.0, 2.0, 0.1, True, row)
        row += 1

        self.hue_var = tk.IntVar(value=self.params.get('hue_shift', 0))
        self.hue_scale, self.hue_label = self._create_scale_with_spinbox(
            param_frm, '色相偏移', self.hue_var, -180, 180, 5, False, row)
        row += 1

        # 噪声减少
        self.enable_noise_var = tk.BooleanVar(value=self.params.get('enable_noise_reduction', False))
        ttk.Checkbutton(param_frm, text='启用噪声减少', variable=self.enable_noise_var,
                        command=self._delayed_run).grid(row=row, column=0, columnspan=2, sticky='w', pady=1)
        row += 1

        self.noise_var = tk.DoubleVar(value=self.params.get('noise_reduction', 0.0))
        self.noise_scale, self.noise_label = self._create_scale_with_spinbox(
            param_frm, '噪声强度', self.noise_var, 0.0, 1.0, 0.1, True, row)
        row += 1

        # 边缘保持
        self.enable_edge_var = tk.BooleanVar(value=self.params.get('enable_edge_preserve', False))
        ttk.Checkbutton(param_frm, text='启用边缘保持', variable=self.enable_edge_var,
                        command=self._delayed_run).grid(row=row, column=0, columnspan=2, sticky='w', pady=1)
        row += 1

        self.edge_var = tk.DoubleVar(value=self.params.get('edge_preserve', 0.5))
        self.edge_scale, self.edge_label = self._create_scale_with_spinbox(
            param_frm, '边缘强度', self.edge_var, 0.0, 1.0, 0.1, True, row)
        row += 1

        # 预设配置
        ttk.Label(param_frm, text='预设配置', width=10).grid(row=row, column=0, sticky='e', pady=2)
        preset_frame = tk.Frame(param_frm)
        preset_frame.grid(row=row, column=1, sticky='w', pady=2, padx=(3,0))

        self.preset_var = tk.StringVar(value='自定义')
        preset_combo = ttk.Combobox(preset_frame, textvariable=self.preset_var,
                                   values=['自定义', '轻度柔化', '中度柔化', '重度柔化', '人像美化', '风景柔化'],
                                   state='readonly', width=12)
        preset_combo.pack(side='left')
        preset_combo.bind('<<ComboboxSelected>>', lambda e: self._apply_preset())
        row += 1

        # 按钮区域 - 垂直排列节省空间
        btn_frame = tk.Frame(param_frm)
        btn_frame.grid(row=row, column=0, columnspan=2, pady=5, sticky='ew')

        ttk.Button(btn_frame, text='载入图片', command=self._load_image).pack(fill='x', pady=1)
        ttk.Button(btn_frame, text='执行', command=self._run).pack(fill='x', pady=1)
        ttk.Button(btn_frame, text='保存配置', command=self._save_cfg).pack(fill='x', pady=1)
        ttk.Button(btn_frame, text='加载配置', command=self._load_cfg).pack(fill='x', pady=1)
        ttk.Button(btn_frame, text='保存结果', command=self._save_result).pack(fill='x', pady=1)

        # 右侧图像显示区域 - 使用grid布局
        # 原图画布
        self.canvas_src = tk.Canvas(self, width=self.preview_max, height=self.preview_max, bg='#333')
        self.canvas_src.grid(row=0, column=1, padx=6)

        # 结果图画布
        self.canvas_dst = tk.Canvas(self, width=self.preview_max, height=self.preview_max, bg='#333')
        self.canvas_dst.grid(row=0, column=2, padx=6)

        # 绑定滚轮缩放到两个画布
        for canvas in (self.canvas_src, self.canvas_dst):
            canvas.bind('<MouseWheel>', self._on_wheel)          # Windows
            canvas.bind('<Button-4>', lambda e: self._on_wheel(e, delta=120))  # Linux scroll up
            canvas.bind('<Button-5>', lambda e: self._on_wheel(e, delta=-120)) # Linux scroll down

        # 状态信息显示
        self.info_var = tk.StringVar(value='坐标:(-, -)  值:-,-,-  分辨率:-x-')
        tk.Label(self, textvariable=self.info_var).grid(row=1, column=1, columnspan=2, sticky='w', pady=(4,0))

        # 配置网格权重
        self.grid_columnconfigure(1, weight=1)
        self.grid_columnconfigure(2, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # 防抖动定时器和缩放变量
        self._update_timer = None
        self.zoom_var = tk.DoubleVar(value=1.0)

        # 更新参数面板滚动区域
        param_frm.update_idletasks()
        param_canvas.configure(scrollregion=param_canvas.bbox("all"))

    def _create_scale_with_spinbox(self, parent, label, var, from_, to, step=1, is_float=False, row=0, ensure_odd=False):
        """创建带精调按钮的滑动条控件"""
        ttk.Label(parent, text=label, width=10).grid(row=row, column=0, sticky='e', pady=1)

        frame = tk.Frame(parent)
        frame.grid(row=row, column=1, sticky='w', pady=1, padx=(3,0))

        # 滑动条
        scale = ttk.Scale(frame, from_=from_, to=to, variable=var, orient='horizontal', length=80)
        scale.pack(side='left')

        # 数值显示和精调按钮
        value_frame = tk.Frame(frame)
        value_frame.pack(side='left', padx=(2,0))

        # 向上箭头 - 极小的按钮
        up_btn = tk.Button(value_frame, text='▲', width=1,
                          font=('Arial', 6), relief='flat', bd=0,
                          command=lambda: self._adjust_value(var, step, from_, to, is_float, value_label, ensure_odd))
        up_btn.pack(pady=0)

        # 数值显示
        if is_float:
            value_label = ttk.Label(value_frame, text=f"{var.get():.2f}", width=4, font=('Arial', 8))
        else:
            value_label = ttk.Label(value_frame, text=str(int(var.get())), width=4, font=('Arial', 8))
        value_label.pack(pady=0)

        # 向下箭头 - 极小的按钮
        down_btn = tk.Button(value_frame, text='▼', width=1,
                           font=('Arial', 6), relief='flat', bd=0,
                           command=lambda: self._adjust_value(var, -step, from_, to, is_float, value_label, ensure_odd))
        down_btn.pack(pady=0)

        # 绑定滑动条变化事件
        scale.configure(command=lambda v: self._on_scale_change(var, value_label, v, is_float, ensure_odd))

        return scale, value_label

    def _adjust_value(self, var, step, min_val, max_val, is_float, label, ensure_odd=False):
        """精调数值"""
        current = var.get()
        if is_float:
            new_val = round(current + step, 2)
        else:
            new_val = int(current + step)
            # 对于核大小参数，确保奇数
            if ensure_odd and new_val >= 3:
                if new_val % 2 == 0:
                    new_val += 1 if step > 0 else -1

        # 限制在有效范围内
        new_val = max(min_val, min(max_val, new_val))
        var.set(new_val)

        # 更新标签显示
        if is_float:
            label.config(text=f"{new_val:.2f}")
        else:
            label.config(text=str(int(new_val)))

        # 触发处理
        self._delayed_run()

    def _on_scale_change(self, var, label, value, is_float, ensure_odd=False):
        """滑动条变化处理"""
        if is_float:
            val = round(float(value), 2)
            var.set(val)
            label.config(text=f"{val:.2f}")
        else:
            val = int(float(value))
            # 对于核大小参数，确保奇数
            if ensure_odd and val >= 3:
                if val % 2 == 0:
                    val += 1
            var.set(val)
            label.config(text=str(val))
        self._delayed_run()

    def _delayed_run(self):
        """延迟执行，防止滑动条抖动"""
        if self._update_timer:
            self.after_cancel(self._update_timer)
        self._update_timer = self.after(200, self._auto_run)  # 200ms延迟

    def _auto_run(self):
        """参数变化时自动执行"""
        if self.img_src is not None:
            self._run()
        self._update_params()



    def _on_wheel(self, event, *, delta=None):
        """滚轮缩放处理"""
        d = delta if delta is not None else event.delta
        step = 0.1 if d > 0 else -0.1
        new_val = max(0.25, min(2.0, self.zoom_var.get() + step))
        self.zoom_var.set(round(new_val, 2))
        self._refresh_images()

    def _refresh_images(self):
        """刷新图像显示"""
        if self.img_src is not None:
            self._show_image(self.canvas_src, self.img_src, is_src=True)
            if hasattr(self, 'img_dst') and self.img_dst is not None:
                self._show_image(self.canvas_dst, self.img_dst, is_src=False)

    def _update_params(self):
        """更新参数并通知回调"""
        if self.on_change:
            new_params = {
                'method': self.METHODS[self.method_var.get()],
                'kernel_size': int(self.kernel_var.get()) | 1,  # 确保奇数
                'contrast_alpha': self.contrast_var.get(),
                'brightness_beta': self.brightness_var.get(),
                'enable_contrast_adjust': self.enable_contrast_var.get(),
                'enable_morph': self.enable_morph_var.get(),
                'morph_kernel_size': int(self.morph_kernel_var.get()) | 1,
                # 新增参数
                'gamma': self.gamma_var.get(),
                'saturation': self.saturation_var.get(),
                'hue_shift': self.hue_var.get(),
                'enable_gamma': self.enable_gamma_var.get(),
                'enable_color_adjust': self.enable_color_var.get(),
                'noise_reduction': self.noise_var.get(),
                'enable_noise_reduction': self.enable_noise_var.get(),
                'edge_preserve': self.edge_var.get(),
                'enable_edge_preserve': self.enable_edge_var.get(),
            }
            self.params.update(new_params)
            self.on_change(self.params)

    def _run(self):
        """执行图像柔化处理"""
        if self.img_src is None:
            messagebox.showwarning('警告', '请先载入图片')
            return
        
        # 更新处理器参数
        method = self.METHODS[self.method_var.get()]
        kernel_size = int(self.kernel_var.get()) | 1  # 确保奇数

        self.proc = SmoothProcessor(
            method=method,
            kernel_size=kernel_size,
            contrast_alpha=self.contrast_var.get(),
            brightness_beta=self.brightness_var.get(),
            enable_contrast_adjust=self.enable_contrast_var.get(),
            enable_morph=self.enable_morph_var.get(),
            morph_kernel_size=int(self.morph_kernel_var.get()) | 1,
            # 新增参数
            gamma=self.gamma_var.get(),
            saturation=self.saturation_var.get(),
            hue_shift=self.hue_var.get(),
            enable_gamma=self.enable_gamma_var.get(),
            enable_color_adjust=self.enable_color_var.get(),
            noise_reduction=self.noise_var.get(),
            enable_noise_reduction=self.enable_noise_var.get(),
            edge_preserve=self.edge_var.get(),
            enable_edge_preserve=self.enable_edge_var.get()
        )
        
        # 处理图像
        result = self.proc.process(self.img_src)
        self.img_dst = result['output']
        
        # 显示结果
        self._show_image(self.canvas_dst, self.img_dst, is_src=False)

    def _load_image(self):
        """载入图片"""
        path = filedialog.askopenfilename(filetypes=[('图片', '*.jpg;*.png;*.bmp;*.tiff')])
        if not path:
            return
        self.img_src = cv2.imread(path)
        if self.img_src is None:
            messagebox.showerror('错误', '无法读取图片')
            return
        self._show_image(self.canvas_src, self.img_src, is_src=True)
        self._auto_run()

    def _show_image(self, canvas: tk.Canvas, img: np.ndarray, *, is_src=False):
        """在画布上显示图像，支持缩放"""
        if img is None:
            canvas.delete('all')
            return

        # 确保图像是BGR格式
        if len(img.shape) == 2:  # 灰度图
            img_bgr = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        else:
            img_bgr = img.copy()

        # 对源图像始终使用最新的 zoom_var, 结果图像沿用源图像的实际显示比例(_src_zoom)
        if is_src:
            zoom_display = self.zoom_var.get()
        else:
            zoom_display = getattr(self, '_src_zoom', self.zoom_var.get())

        h, w = img_bgr.shape[:2]
        disp_w, disp_h = int(w * zoom_display), int(h * zoom_display)

        # 如果显示尺寸超过画布最大尺寸，进行缩放
        if max(disp_w, disp_h) > self.preview_max:
            scale = self.preview_max / max(disp_w, disp_h)
            disp_w, disp_h = int(disp_w * scale), int(disp_h * scale)
            zoom_display *= scale

        # 调整图像大小
        img_disp = cv2.resize(img_bgr, (disp_w, disp_h)) if zoom_display != 1 else img_bgr.copy()

        # 转换为RGB并显示
        img_rgb = cv2.cvtColor(img_disp, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(img_rgb)
        photo = ImageTk.PhotoImage(pil_img)

        canvas.delete('all')
        canvas.create_image(0, 0, anchor='nw', image=photo)
        canvas.image = photo  # 保存引用防止垃圾回收

        if is_src:
            # 记录当前源图显示的缩放比例
            self._src_zoom = zoom_display
            # 绑定鼠标移动事件显示像素信息
            canvas.bind('<Motion>', lambda e, z=zoom_display: self._update_info(e, z))
            self.info_var.set(f'坐标:(-, -)  值:-,-,-  分辨率:{w}x{h}')

    def _update_info(self, event, zoom_display):
        """更新鼠标位置信息"""
        if self.img_src is None:
            return
        x = int(event.x / zoom_display)
        y = int(event.y / zoom_display)
        h, w = self.img_src.shape[:2]
        if 0 <= x < w and 0 <= y < h:
            if len(self.img_src.shape) == 3:
                b, g, r = self.img_src[y, x]
                self.info_var.set(f'坐标:({x}, {y})  值:{r},{g},{b}  分辨率:{w}x{h}')
            else:
                gray = self.img_src[y, x]
                self.info_var.set(f'坐标:({x}, {y})  值:{gray}  分辨率:{w}x{h}')
        else:
            self.info_var.set(f'坐标:(-, -)  值:-,-,-  分辨率:{w}x{h}')

    def get_params(self) -> dict:
        """获取当前所有参数"""
        return {
            'method': self.METHODS[self.method_var.get()],
            'kernel_size': self.kernel_var.get(),
            'contrast_alpha': self.contrast_var.get(),
            'brightness_beta': self.brightness_var.get(),
            'enable_contrast_adjust': self.enable_contrast_var.get(),
            'enable_morph': self.enable_morph_var.get(),
            'morph_kernel_size': self.morph_kernel_var.get(),
            # 新增参数
            'gamma': self.gamma_var.get(),
            'saturation': self.saturation_var.get(),
            'hue_shift': self.hue_var.get(),
            'enable_gamma': self.enable_gamma_var.get(),
            'enable_color_adjust': self.enable_color_var.get(),
            'noise_reduction': self.noise_var.get(),
            'enable_noise_reduction': self.enable_noise_var.get(),
            'edge_preserve': self.edge_var.get(),
            'enable_edge_preserve': self.enable_edge_var.get(),
        }

    def set_params(self, params: dict):
        """设置参数到UI并刷新"""
        # 更新UI控件
        method_cn = next((k for k,v in self.METHODS.items() if v==params.get('method', 'gaussian')), '高斯模糊')
        self.method_var.set(method_cn)
        self.kernel_var.set(params.get('kernel_size', 15))
        self.contrast_var.set(params.get('contrast_alpha', 0.8))
        self.brightness_var.set(params.get('brightness_beta', 20))
        self.enable_contrast_var.set(params.get('enable_contrast_adjust', True))
        self.enable_morph_var.set(params.get('enable_morph', False))
        self.morph_kernel_var.set(params.get('morph_kernel_size', 3))
        # 新增参数
        self.gamma_var.set(params.get('gamma', 1.0))
        self.saturation_var.set(params.get('saturation', 1.0))
        self.hue_var.set(params.get('hue_shift', 0))
        self.enable_gamma_var.set(params.get('enable_gamma', False))
        self.enable_color_var.set(params.get('enable_color_adjust', False))
        self.noise_var.set(params.get('noise_reduction', 0.0))
        self.enable_noise_var.set(params.get('enable_noise_reduction', False))
        self.edge_var.set(params.get('edge_preserve', 0.5))
        self.enable_edge_var.set(params.get('enable_edge_preserve', False))

        # 更新标签显示
        self.kernel_label.config(text=str(self.kernel_var.get()))
        self.contrast_label.config(text=f"{self.contrast_var.get():.2f}")
        self.brightness_label.config(text=str(self.brightness_var.get()))
        self.morph_label.config(text=str(self.morph_kernel_var.get()))
        self.gamma_label.config(text=f"{self.gamma_var.get():.2f}")
        self.saturation_label.config(text=f"{self.saturation_var.get():.2f}")
        self.hue_label.config(text=str(self.hue_var.get()))
        self.noise_label.config(text=f"{self.noise_var.get():.2f}")
        self.edge_label.config(text=f"{self.edge_var.get():.2f}")

        self._auto_run()

    def _apply_preset(self):
        """应用预设配置"""
        preset = self.preset_var.get()

        presets = {
            '轻度柔化': {
                'method': 'gaussian',
                'kernel_size': 5,
                'contrast_alpha': 0.9,
                'brightness_beta': 5,
                'enable_contrast_adjust': True,
                'enable_morph': False,
                'enable_gamma': False,
                'enable_color_adjust': False,
                'enable_noise_reduction': False,
                'enable_edge_preserve': False,
            },
            '中度柔化': {
                'method': 'gaussian',
                'kernel_size': 15,
                'contrast_alpha': 0.8,
                'brightness_beta': 15,
                'enable_contrast_adjust': True,
                'enable_morph': True,
                'morph_kernel_size': 3,
                'enable_gamma': True,
                'gamma': 1.1,
                'enable_color_adjust': False,
                'enable_noise_reduction': True,
                'noise_reduction': 0.3,
                'enable_edge_preserve': False,
            },
            '重度柔化': {
                'method': 'bilateral',
                'kernel_size': 25,
                'bilateral_sigma_color': 80,
                'bilateral_sigma_space': 80,
                'contrast_alpha': 0.7,
                'brightness_beta': 25,
                'enable_contrast_adjust': True,
                'enable_morph': True,
                'morph_kernel_size': 5,
                'enable_gamma': True,
                'gamma': 1.2,
                'enable_color_adjust': True,
                'saturation': 1.1,
                'enable_noise_reduction': True,
                'noise_reduction': 0.5,
                'enable_edge_preserve': True,
                'edge_preserve': 0.7,
            },
            '人像美化': {
                'method': 'bilateral',
                'kernel_size': 9,
                'bilateral_sigma_color': 75,
                'bilateral_sigma_space': 75,
                'contrast_alpha': 0.85,
                'brightness_beta': 10,
                'enable_contrast_adjust': True,
                'enable_gamma': True,
                'gamma': 1.1,
                'enable_color_adjust': True,
                'saturation': 1.05,
                'hue_shift': 0,
                'enable_noise_reduction': True,
                'noise_reduction': 0.4,
                'enable_edge_preserve': True,
                'edge_preserve': 0.6,
            },
            '风景柔化': {
                'method': 'gaussian',
                'kernel_size': 11,
                'contrast_alpha': 0.9,
                'brightness_beta': 8,
                'enable_contrast_adjust': True,
                'enable_gamma': True,
                'gamma': 1.05,
                'enable_color_adjust': True,
                'saturation': 1.15,
                'hue_shift': 0,
                'enable_noise_reduction': True,
                'noise_reduction': 0.2,
                'enable_edge_preserve': False,
            }
        }

        if preset in presets:
            # 合并默认参数
            default_params = {
                'method': 'gaussian',
                'kernel_size': 15,
                'sigma_x': 0,
                'sigma_y': 0,
                'bilateral_d': 9,
                'bilateral_sigma_color': 75,
                'bilateral_sigma_space': 75,
                'contrast_alpha': 0.8,
                'brightness_beta': 20,
                'morph_kernel_size': 3,
                'enable_contrast_adjust': True,
                'enable_morph': False,
                'gamma': 1.0,
                'saturation': 1.0,
                'hue_shift': 0,
                'enable_gamma': False,
                'enable_color_adjust': False,
                'noise_reduction': 0.0,
                'enable_noise_reduction': False,
                'edge_preserve': 0.5,
                'enable_edge_preserve': False,
            }
            default_params.update(presets[preset])
            self.set_params(default_params)

    def _save_cfg(self):
        """保存配置"""
        init_file = CONFIG_DIR / 'default.yml'
        path = filedialog.asksaveasfilename(
            initialdir=str(CONFIG_DIR),
            initialfile=init_file.name,
            defaultextension='.yml',
            filetypes=[('YAML配置文件', '*.yml'), ('YAML配置文件', '*.yaml')]
        )
        if path:
            with open(path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(self.get_params(), f, allow_unicode=True)
            messagebox.showinfo('成功', f'配置已保存到:\n{path}')

    def _load_cfg(self):
        """加载配置"""
        path = filedialog.askopenfilename(
            initialdir=str(CONFIG_DIR),
            filetypes=[('YAML配置文件', '*.yml'), ('YAML配置文件', '*.yaml')]
        )
        if not path:
            return
        try:
            with open(path, 'r', encoding='utf-8') as f:
                params = yaml.safe_load(f)
            self.set_params(params or {})
            messagebox.showinfo('成功', f'配置已加载:\n{path}')
        except Exception as e:
            messagebox.showerror('错误', f'加载配置失败: {e}')

    def _save_result(self):
        """保存处理结果"""
        if not hasattr(self, 'img_dst') or self.img_dst is None:
            messagebox.showwarning('警告', '没有处理结果可保存')
            return
        path = filedialog.asksaveasfilename(
            defaultextension='.jpg',
            filetypes=[('JPEG', '*.jpg'), ('PNG', '*.png'), ('BMP', '*.bmp')]
        )
        if path:
            cv2.imwrite(path, self.img_dst)
            messagebox.showinfo('成功', '结果已保存')


# 测试代码
if __name__ == '__main__':
    root = tk.Tk()
    root.title('图像柔化测试')
    frame = SmoothFrame(root)
    frame.pack(fill='both', expand=True)
    root.mainloop()
