# -*- coding: utf-8 -*-
"""Rectangular caliper measurement (independent plugin).

This module replicates the logic of CrossCaliperMeasure from
`plugins/DetectIdentify/geometry_measure.py` but in a self-contained form so
that rectangle measurement is on par with other geometry_tools such as
`circle_tool`, `arc_tool`, etc.

API is intentionally minimal:

    >>> from plugins.geometry_tools.rect_tool import measure_rect
    >>> res = measure_rect(img, center=(320,240), len_x=200, len_y=60,
                           angle_deg=30)
    >>> print(res['width'], res['height'])

"""
from __future__ import annotations

import cv2
import numpy as np
from dataclasses import dataclass
from typing import Tuple, List, Dict, Any, Optional

__all__ = [
    'measure_rect',
    'RectCaliperParams',
]


# ---------------------------- helpers -----------------------------
class _CaliperEdgeFinder:
    """Utility to sample gray profile along a scan line and find edge position."""

    @staticmethod
    def _find_edge_diff(profile: np.ndarray, polarity: str, thresh: float) -> Optional[float]:
        # simple 1st-order derivative
        g = np.convolve(profile.astype(np.float32), [-1, 0, 1], mode='same')
        if polarity == 'dark2bright':
            idxs = np.where(g > thresh)[0]
        elif polarity == 'bright2dark':
            idxs = np.where(g < -thresh)[0]
        else:  # both – use abs
            idxs = np.where(np.abs(g) > thresh)[0]
        if idxs.size == 0:
            return None
        # sub-pixel refine using quadratic fit around max gradient
        i = int(idxs[np.argmax(np.abs(g[idxs]))])
        if 1 <= i < len(g)-1:
            y0, y1, y2 = g[i-1], g[i], g[i+1]
            denom = y0 - 2*y1 + y2
            if abs(denom) > 1e-6:
                x_peak = i - (y2 - y0) / (2*denom)
                return float(x_peak)
        return float(i)

    @staticmethod
    def sample_line(img: np.ndarray, p0: Tuple[float, float], p1: Tuple[float, float], thickness: int = 3) -> np.ndarray:
        length = int(np.hypot(p1[0]-p0[0], p1[1]-p0[1]))
        if length <= 1:
            return np.array([])
        # unit perpendicular for averaging
        dx, dy = p1[0]-p0[0], p1[1]-p0[1]
        norm = np.hypot(dx, dy)
        if norm == 0:
            return np.array([])
        ux, uy = dx / norm, dy / norm
        vx, vy = -uy, ux
        xs = np.linspace(0, length, length+1)
        coords = np.stack([
            p0[0] + ux * xs[:, None] + vx * np.linspace(-thickness//2, thickness//2, thickness)[None, :],
            p0[1] + uy * xs[:, None] + vy * np.linspace(-thickness//2, thickness//2, thickness)[None, :]
        ], axis=-1)
        # bilinear sampling
        h, w = img.shape
        coords = coords.reshape(-1, 2)
        x0 = np.clip(coords[:, 0], 0, w-1)
        y0 = np.clip(coords[:, 1], 0, h-1)
        vals = img[y0.astype(np.intp), x0.astype(np.intp)].reshape(length+1, thickness)
        profile = vals.mean(axis=1)
        return profile

    @staticmethod
    def find_edge(profile: np.ndarray, polarity: str = 'both', thresh: float = 5.0) -> Optional[float]:
        return _CaliperEdgeFinder._find_edge_diff(profile, polarity, thresh)


@dataclass
class _MultiCaliperParams:
    p0: Tuple[float, float]
    p1: Tuple[float, float]
    width: float = 20.0
    n_lines: int = 10
    polarity: str = 'both'
    threshold: float = 5.0
    thickness: int = 3


class _MultiCaliper:
    """Use multiple scan lines inside a rectangle to find two parallel edges."""

    def __init__(self, prm: _MultiCaliperParams):
        self.prm = prm
        self.edge_pts1: List[Tuple[float, float]] = []
        self.edge_pts2: List[Tuple[float, float]] = []
        self.lines: List[Tuple[float, float]] = []  # (m,c)
        self.scan_lines: List[Tuple[Tuple[float, float], Tuple[float, float]]] = []
        self.result: Dict[str, Any] = {}

    def detect(self, img: np.ndarray) -> bool:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim == 3 else img
        dx, dy = self.prm.p1[0]-self.prm.p0[0], self.prm.p1[1]-self.prm.p0[1]
        length = np.hypot(dx, dy)
        if length < 2 or self.prm.width < 1:
            return False
        ux, uy = dx/length, dy/length
        vx, vy = -uy, ux  # perpendicular
        d_step = self.prm.width / self.prm.n_lines
        d_start = -self.prm.width/2 + d_step/2
        self.edge_pts1.clear(); self.edge_pts2.clear(); self.scan_lines.clear()
        good = 0
        for i in range(self.prm.n_lines):
            offset = d_start + i*d_step
            ofx, ofy = vx*offset, vy*offset
            p0 = (self.prm.p0[0]+ofx, self.prm.p0[1]+ofy)
            p1 = (self.prm.p1[0]+ofx, self.prm.p1[1]+ofy)
            profile = _CaliperEdgeFinder.sample_line(gray, p0, p1, self.prm.thickness)
            if profile.size < 3:
                continue
            pos1 = _CaliperEdgeFinder.find_edge(profile, self.prm.polarity, self.prm.threshold)
            opp = {'dark2bright':'bright2dark','bright2dark':'dark2bright'}.get(self.prm.polarity, self.prm.polarity)
            pos2 = _CaliperEdgeFinder.find_edge(profile, opp, self.prm.threshold)
            if pos1 is None or pos2 is None:
                continue
            good += 1
            # points in image coord
            px1 = p0[0] + ux*pos1
            py1 = p0[1] + uy*pos1
            px2 = p0[0] + ux*pos2
            py2 = p0[1] + uy*pos2
            self.edge_pts1.append((px1, py1)); self.edge_pts2.append((px2, py2))
            self.scan_lines.append((p0, p1))
        if good < max(4, self.prm.n_lines//3):
            return False
        # fit two straight lines y = m x + c
        def _fit(pts):
            if len(pts) < 2:
                return None, None
            pts = np.array(pts)
            x, y = pts[:,0], pts[:,1]
            A = np.vstack([x, np.ones_like(x)]).T
            m, c = np.linalg.lstsq(A, y, rcond=None)[0]
            return float(m), float(c)
        m1, c1 = _fit(self.edge_pts1)
        m2, c2 = _fit(self.edge_pts2)
        if m1 is None or m2 is None:
            return False
        self.lines = [(m1, c1), (m2, c2)]
        dist = abs(c2 - c1) / np.hypot(m1, 1)
        self.result = {
            'distance': dist,
            'lines': self.lines,
            'edge_pts1': self.edge_pts1,
            'edge_pts2': self.edge_pts2,
            'scan_lines': self.scan_lines,
        }
        return True


@dataclass
class RectCaliperParams:
    center: Tuple[float, float]
    len_x: float
    len_y: float
    angle_deg: float = 0.0
    width: float = 30.0
    n_lines: int = 10
    polarity: str = 'both'
    threshold: float = 5.0
    thickness: int = 3


def measure_rect(img: np.ndarray, *, params: RectCaliperParams | None = None,
                 center: Tuple[float, float] | None = None,
                 len_x: float | None = None, len_y: float | None = None,
                 angle_deg: float = 0.0, width: float = 30.0, n_lines: int = 10,
                 polarity: str = 'both', threshold: float = 5.0, thickness: int = 3) -> Dict[str, Any]:
    """Measure rectangle width / height using cross multi-line calipers.

    Either pass a fully constructed `RectCaliperParams` or individual args.
    Returns dict with keys: width, height, lines_h, lines_v, etc.
    Raise ValueError if detection fails.
    """
    if params is None:
        if None in (center, len_x, len_y):
            raise ValueError('Either `params` or (center, len_x, len_y) must be provided')
        params = RectCaliperParams(center=center, len_x=len_x, len_y=len_y,
                                   angle_deg=angle_deg, width=width, n_lines=n_lines,
                                   polarity=polarity, threshold=threshold, thickness=thickness)

    cx, cy = params.center
    ang = np.deg2rad(params.angle_deg)
    cos_a, sin_a = np.cos(ang), np.sin(ang)

    # compute arm endpoints (horizontal along len_x, vertical along len_y) BEFORE rotation
    p0_h = (-params.len_x/2, 0); p1_h = (params.len_x/2, 0)
    p0_v = (0, -params.len_y/2); p1_v = (0, params.len_y/2)

    def _rot(pt):
        return (cx + pt[0]*cos_a - pt[1]*sin_a, cy + pt[0]*sin_a + pt[1]*cos_a)

    arm_h = _MultiCaliper(_MultiCaliperParams(p0=_rot(p0_h), p1=_rot(p1_h), width=params.width,
                                              n_lines=params.n_lines, polarity=params.polarity,
                                              threshold=params.threshold, thickness=params.thickness))
    arm_v = _MultiCaliper(_MultiCaliperParams(p0=_rot(p0_v), p1=_rot(p1_v), width=params.width,
                                              n_lines=params.n_lines, polarity=params.polarity,
                                              threshold=params.threshold, thickness=params.thickness))
    if not (arm_h.detect(img) and arm_v.detect(img)):
        raise ValueError('detect_failed')

    res = {
        'width': arm_h.result['distance'],
        'height': arm_v.result['distance'],
        'edge_pts1': arm_h.edge_pts1 + arm_v.edge_pts1,
        'edge_pts2': arm_h.edge_pts2 + arm_v.edge_pts2,
        'scan_lines': arm_h.scan_lines + arm_v.scan_lines,
        'lines_h': arm_h.lines,
        'lines_v': arm_v.lines,
        'angle': params.angle_deg,
        'type': 'rect_caliper'
    }
    return res
