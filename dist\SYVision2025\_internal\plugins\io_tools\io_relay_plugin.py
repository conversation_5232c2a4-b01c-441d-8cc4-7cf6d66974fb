"""IO Relay 流水线插件
控制 USB 串口继电器板，可在流程中切换 IO。
UI: io_relay_ui.IORelayFrame
"""
from __future__ import annotations
from typing import Dict, Any
from pathlib import Path
import importlib, time

try:
    import serial
except ImportError:
    serial = None

from plugins.plugin_base import PluginBase

# 全局串口共享管理器
class SerialManager:
    """串口共享管理器，允许多个插件实例共享同一个串口"""
    _instances = {}  # {port: serial_instance}
    _ref_counts = {}  # {port: reference_count}

    @classmethod
    def get_serial(cls, port, baud=9600, timeout=0.2):
        """获取串口实例，如果已存在则复用"""
        if port in cls._instances:
            cls._ref_counts[port] += 1
            print(f"[SERIAL_MGR] 复用串口 {port}，引用计数: {cls._ref_counts[port]}")
            return cls._instances[port]

        try:
            ser = serial.Serial(port, baud, timeout=timeout)
            cls._instances[port] = ser
            cls._ref_counts[port] = 1
            print(f"[SERIAL_MGR] 创建新串口 {port}，引用计数: 1")
            return ser
        except Exception as e:
            print(f"[SERIAL_MGR] 串口 {port} 创建失败: {e}")
            return None

    @classmethod
    def release_serial(cls, port):
        """释放串口引用，当引用计数为0时关闭串口"""
        if port not in cls._ref_counts:
            return

        cls._ref_counts[port] -= 1
        print(f"[SERIAL_MGR] 释放串口 {port}，引用计数: {cls._ref_counts[port]}")

        if cls._ref_counts[port] <= 0:
            if port in cls._instances:
                try:
                    cls._instances[port].close()
                    print(f"[SERIAL_MGR] 关闭串口 {port}")
                except:
                    pass
                del cls._instances[port]
            del cls._ref_counts[port]

class IORelayPlugin(PluginBase):
    name = "io_relay"
    label = "继电器控制"
    category = "IO"

    params: Dict[str, Any] = {
        "port": "",
        "baud": 9600,
        "relay_index": 1,   # 1-based
        "action": "ON",    # ON / OFF / TOG / QRY / CONDITIONAL
        "trigger_on_ng": True,    # NG时触发
        "trigger_on_ok": False,   # OK时触发
        "pulse_duration": 100,    # 脉冲持续时间(ms)
        "trigger_delay": 0,       # 触发延时(ms)
        "enable_delay": False,    # 启用延时触发
    }

    param_labels = {
        "port": "端口",
        "baud": "波特率",
        "relay_index": "通道",
        "action": "动作",
        "trigger_on_ng": "NG时触发",
        "trigger_on_ok": "OK时触发",
        "pulse_duration": "脉冲时长(ms)",
        "trigger_delay": "触发延时(ms)",
        "enable_delay": "启用延时触发",
    }

    def setup(self, params: Dict[str, Any]):
        print(f"[IO_RELAY] setup开始，传入参数: {params}")

        # 先加载工位配置，然后与传入参数合并
        merged_params = self._load_workstation_config(params)
        print(f"[IO_RELAY] 合并后参数: {merged_params}")

        # 保存合并后的参数
        self.params.update(merged_params)

        self.ser = None
        print(f"[IO_RELAY] serial模块状态: {serial is not None}")
        if serial is None:
            print(f"[IO_RELAY] serial模块未导入，跳过串口初始化")
            return

        port = merged_params.get("port"); baud = int(merged_params.get("baud", 9600))
        relay_index = merged_params.get("relay_index", 1)
        print(f"[IO_RELAY] 继电器{relay_index} - 尝试初始化串口: {port}, 波特率: {baud}")

        self.port = port  # 保存端口名用于释放
        if port:
            self.ser = SerialManager.get_serial(port, baud, timeout=0.2)
            if self.ser:
                print(f"[IO_RELAY] 继电器{relay_index} - 串口{port}初始化成功")
            else:
                print(f"[IO_RELAY] 继电器{relay_index} - 串口{port}初始化失败")
        else:
            print(f"[IO_RELAY] 继电器{relay_index} - 未配置串口端口")

    def __del__(self):
        """析构时释放串口引用"""
        if hasattr(self, 'port') and self.port:
            SerialManager.release_serial(self.port)

    def process(self, img, ctx):
        relay_index = self.params.get("relay_index", 1)
        if not (self.ser and self.ser.is_open):
            print(f"[IO_RELAY] 继电器{relay_index} - 串口未打开，跳过处理")
            return img, ctx

        action = self.params.get("action", "ON").upper()

        # 条件触发模式
        if action == "CONDITIONAL":
            relay_index = self.params.get("relay_index", 1)
            print(f"[IO_RELAY] 继电器{relay_index} - 条件触发模式，检查触发条件...")
            should_trigger = self._check_trigger_condition(ctx)
            print(f"[IO_RELAY] 继电器{relay_index} - 触发条件检查结果: {should_trigger}")
            if should_trigger:
                enable_delay = self.params.get("enable_delay", False)
                print(f"[IO_RELAY] 启用延时: {enable_delay}")
                if enable_delay:
                    print(f"[IO_RELAY] 执行延时脉冲触发")
                    self._trigger_pulse_with_delay()
                else:
                    print(f"[IO_RELAY] 执行立即脉冲触发")
                    self._trigger_pulse()
                ctx[self.name] = {"action": "triggered", "condition": "met", "mode": "pulse"}
            else:
                print(f"[IO_RELAY] 不满足触发条件，不触发")
                ctx[self.name] = {"action": "no_trigger", "condition": "not_met"}
        else:
            # 原有的直接触发模式
            self._send_relay_command(action)
            ctx[self.name] = {"action": action, "mode": "direct"}

        return img, ctx

    def _check_trigger_condition(self, ctx):
        """检查是否满足触发条件"""
        # 检查YOLO检测结果
        yolo_result = ctx.get('yolo_detect', {})
        ng_count = yolo_result.get('ng_count', 0)
        ok_count = yolo_result.get('ok_count', 0)

        trigger_on_ng = self.params.get("trigger_on_ng", True)
        trigger_on_ok = self.params.get("trigger_on_ok", False)

        # 添加详细调试信息
        print(f"[IO_RELAY] 上下文检查: yolo_result={yolo_result}")
        print(f"[IO_RELAY] NG计数: {ng_count}, OK计数: {ok_count}")
        print(f"[IO_RELAY] 触发配置: trigger_on_ng={trigger_on_ng}, trigger_on_ok={trigger_on_ok}")

        if trigger_on_ng and ng_count > 0:
            print(f"[IO_RELAY] 检测到NG: {ng_count}个，触发继电器")
            return True
        if trigger_on_ok and ok_count > 0:
            print(f"[IO_RELAY] 检测到OK: {ok_count}个，触发继电器")
            return True

        print(f"[IO_RELAY] 不满足触发条件: NG={ng_count}, OK={ok_count}")
        return False

    def _trigger_pulse(self):
        """发送脉冲信号（立即触发）"""
        idx = int(self.params.get("relay_index", 1))
        duration = int(self.params.get("pulse_duration", 100))

        try:
            # 开启继电器
            frame_on = bytes([0xA0, idx, 0x01, (0xA0+idx+0x01)&0xFF])
            self.ser.write(frame_on)

            # 等待指定时间
            time.sleep(duration / 1000.0)

            # 关闭继电器
            frame_off = bytes([0xA0, idx, 0x00, (0xA0+idx+0x00)&0xFF])
            self.ser.write(frame_off)

            print(f"[IO_RELAY] 触发脉冲: 通道{idx}, 持续{duration}ms")
        except Exception as e:
            print(f"[IO_RELAY] 脉冲触发失败: {e}")

    def _trigger_pulse_with_delay(self):
        """发送延时脉冲信号"""
        import threading

        idx = int(self.params.get("relay_index", 1))
        duration = int(self.params.get("pulse_duration", 100))
        delay = int(self.params.get("trigger_delay", 0))

        def delayed_trigger():
            try:
                # 延时等待
                if delay > 0:
                    print(f"[IO_RELAY] 延时触发: 通道{idx}, 延时{delay}ms后执行")
                    time.sleep(delay / 1000.0)

                # 开启继电器
                frame_on = bytes([0xA0, idx, 0x01, (0xA0+idx+0x01)&0xFF])
                self.ser.write(frame_on)

                # 等待指定时间
                time.sleep(duration / 1000.0)

                # 关闭继电器
                frame_off = bytes([0xA0, idx, 0x00, (0xA0+idx+0x00)&0xFF])
                self.ser.write(frame_off)

                print(f"[IO_RELAY] 延时脉冲完成: 通道{idx}, 延时{delay}ms + 持续{duration}ms")
            except Exception as e:
                print(f"[IO_RELAY] 延时脉冲触发失败: {e}")

        # 在后台线程中执行延时触发，避免阻塞主流程
        thread = threading.Thread(target=delayed_trigger, daemon=True)
        thread.start()

    def _send_relay_command(self, action):
        """发送继电器命令（原有功能）"""
        idx = int(self.params.get("relay_index", 1))
        op_map = {"ON": 0x01, "OFF": 0x00, "TOG": 0x04, "QRY": 0x05}
        op = op_map.get(action, 0x05)
        frame = bytes([0xA0, idx, op, (0xA0+idx+op)&0xFF])
        try:
            self.ser.write(frame)
            time.sleep(0.05)
            resp = self.ser.read_all()
            print(f"[IO_RELAY] 发送命令: {action} -> 通道{idx}")
        except Exception as e:
            print(f"[IO_RELAY] 命令发送失败: {e}")

    def close(self):
        if self.ser and self.ser.is_open:
            try: self.ser.close()
            except Exception: pass

    def save_config(self, file_path: str = None):
        """保存配置到文件，与其他插件保持一致"""
        try:
            import yaml
            from pathlib import Path

            if file_path is None:
                # 使用路径管理模块获取配置目录
                try:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                    from path_manager import get_plugin_config_path
                    file_path = get_plugin_config_path('io_relay', 'default.yml')
                except ImportError:
                    # 回退到原来的逻辑
                    cfg_dir = Path(__file__).resolve().parents[2] / 'configs' / 'io_relay'
                    cfg_dir.mkdir(parents=True, exist_ok=True)
                    file_path = cfg_dir / 'default.yml'

            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(self.params, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ IO继电器配置已保存到: {file_path}")
        except Exception as e:
            print(f"❌ 保存IO继电器配置失败: {e}")

    def load_config(self, file_path: str = None):
        """从文件加载配置，与其他插件保持一致"""
        try:
            import yaml
            from pathlib import Path

            if file_path is None:
                # 使用路径管理模块获取配置目录
                try:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                    from path_manager import get_plugin_config_path
                    file_path = get_plugin_config_path('io_relay', 'default.yml')
                except ImportError:
                    # 回退到原来的逻辑
                    cfg_dir = Path(__file__).resolve().parents[2] / 'configs' / 'io_relay'
                    file_path = cfg_dir / 'default.yml'

                # 如果默认配置文件不存在，先保存当前参数作为默认配置
                if not Path(file_path).exists():
                    self.save_config()
                    return

            with open(file_path, 'r', encoding='utf-8') as f:
                loaded_params = yaml.safe_load(f)

            # 更新参数
            if loaded_params:
                self.params.update(loaded_params)
                # 重新初始化
                self.setup(self.params)

            print(f"✅ IO继电器配置已从文件加载: {file_path}")

        except Exception as e:
            print(f"❌ 加载IO继电器配置失败: {e}")

    def _load_workstation_config(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """从工位配置加载参数，与其他插件保持一致"""
        try:
            import yaml
            import os
            from pathlib import Path

            # 方法1: 尝试从环境变量获取当前工位配置文件
            workstation_file = os.environ.get('CURRENT_WORKSTATION_PATH')
            if workstation_file:
                workstation_path = Path(workstation_file)

                # 如果是配方目录结构（有工位子目录）
                if workstation_path.is_dir():
                    # 首先尝试专用的io_relay.yml文件
                    config_file = workstation_path / "io_relay.yml"
                    if config_file.exists():
                        print(f"[IO_RELAY] 从环境变量找到工位配置: {config_file}")
                        config_data = yaml.safe_load(config_file.read_text(encoding="utf-8"))
                        if config_data:
                            merged_params = base_params.copy()
                            merged_params.update(config_data)
                            return merged_params

                    # 如果没有专用文件，尝试从pipeline.yaml中读取io_relay配置
                    pipeline_file = workstation_path / "pipeline.yaml"
                    if pipeline_file.exists():
                        print(f"[IO_RELAY] 尝试从pipeline配置读取: {pipeline_file}")
                        pipeline_data = yaml.safe_load(pipeline_file.read_text(encoding="utf-8"))
                        if pipeline_data:
                            # 处理直接数组格式或带steps的格式
                            steps = pipeline_data if isinstance(pipeline_data, list) else pipeline_data.get('steps', [])
                            # 查找io_relay插件的配置
                            for step in steps:
                                if isinstance(step, dict) and 'io_relay' in step:
                                    io_params = step['io_relay']
                                    if io_params:
                                        print(f"[IO_RELAY] 从pipeline找到IO继电器配置: {io_params}")
                                        merged_params = base_params.copy()
                                        merged_params.update(io_params)
                                        return merged_params

                # 如果是系统配置文件（直接是yaml文件）
                elif workstation_path.is_file() and workstation_path.suffix in ['.yaml', '.yml']:
                    print(f"[IO_RELAY] 尝试从工位配置文件读取: {workstation_path}")
                    pipeline_data = yaml.safe_load(workstation_path.read_text(encoding="utf-8"))
                    if pipeline_data:
                        # 处理直接数组格式
                        steps = pipeline_data if isinstance(pipeline_data, list) else []
                        # 查找io_relay插件的配置
                        for step in steps:
                            if isinstance(step, dict) and 'io_relay' in step:
                                io_params = step['io_relay']
                                if io_params:
                                    print(f"[IO_RELAY] 从工位文件找到IO继电器配置: {io_params}")
                                    merged_params = base_params.copy()
                                    merged_params.update(io_params)
                                    return merged_params

            # 方法2: 尝试从当前工作目录找到工位配置文件
            cwd = Path.cwd()
            config_file = cwd / "io_relay.yml"
            if config_file.exists():
                print(f"[IO_RELAY] 从当前目录找到工位配置: {config_file}")
                config_data = yaml.safe_load(config_file.read_text(encoding="utf-8"))
                if config_data:
                    merged_params = base_params.copy()
                    merged_params.update(config_data)
                    return merged_params

            # 方法3: 尝试从当前目录的pipeline.yaml读取
            pipeline_file = cwd / "pipeline.yaml"
            if pipeline_file.exists():
                print(f"[IO_RELAY] 尝试从当前目录pipeline配置读取: {pipeline_file}")
                pipeline_data = yaml.safe_load(pipeline_file.read_text(encoding="utf-8"))
                if pipeline_data and 'steps' in pipeline_data:
                    # 查找io_relay插件的配置
                    for step in pipeline_data['steps']:
                        if step.get('plugin') == 'io_relay':
                            io_params = step.get('params', {})
                            if io_params:
                                print(f"[IO_RELAY] 从当前目录pipeline找到IO继电器配置: {io_params}")
                                merged_params = base_params.copy()
                                merged_params.update(io_params)
                                return merged_params

            print(f"[IO_RELAY] 未找到工位配置文件，使用基础参数")
            return base_params

        except Exception as e:
            print(f"[ERROR] 加载工位配置失败: {e}")
            return base_params

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img=None):
        """打开参数配置对话框"""
        import tkinter as tk
        from tkinter import messagebox
        try:
            # 使用专门的配置UI
            create_config_dialog = importlib.import_module('plugins.ui.io_tools.io_relay_config_ui').create_config_dialog
            create_config_dialog(master, params, on_change)
        except Exception as e:
            messagebox.showerror('错误', f'加载配置UI失败: {e}')
            print(f"详细错误: {e}")
            return


_ = IORelayPlugin
