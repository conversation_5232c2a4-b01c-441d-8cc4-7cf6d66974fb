"""Refactored main GUI for SY Vision 2025.

Key points compared with legacy `main_app.py`:
-----------------------------------------------------------------
1. Removed **start / pause / stop** preview buttons – capture starts automatically
   when the application launches and restarts automatically after mode switch.
2. Preview tab keeps the familiar layout / mode / camera-settings bar as before.
3. Pipeline control buttons (Run / Pause / Single / Stop) are kept intact.
4. Sidebar, header, status-bar visuals reuse the same ttkbootstrap style names to
   keep the original look & feel.
5. Camera integration still relies on external `CameraManager` and `Workstation`
   models; failures are handled gracefully with message boxes.

You can copy-replace the old `main_app.py` with this file after confirming it
works, or simply rename the old file and rename this one to `main_app.py`.
"""
from __future__ import annotations

import math
import os
import shutil
import subprocess
import time
import yaml
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

# 导入路径管理模块
from path_manager import (
    get_base_path, get_data_path, get_resource_path,
    get_config_path, get_log_path, get_capture_path,
    get_recipe_path, print_path_info, ensure_data_directories
)

import tkinter as tk
from tkinter import ttk

# 导入系统清理模块
from core.system_cleaner import get_system_cleaner, start_system_cleaner, stop_system_cleaner

# 导入系统日志模块
from core.system_logger import get_system_logger, log_system, log_detection, log_operation, log_error

# --------------------------------------------------
# Logging config: console + file, fine-grained levels
import logging
from logging.handlers import RotatingFileHandler
from pathlib import Path

LOG_DIR = Path(get_log_path())
LOG_DIR.mkdir(exist_ok=True)
LOG_FILE = LOG_DIR / 'main_app.log'

_fmt = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
formatter = logging.Formatter(_fmt)

root = logging.getLogger()
root.setLevel(logging.DEBUG)  # capture all and let handlers filter

# Console handler（INFO 及以上）
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)
ch.setFormatter(formatter)
root.addHandler(ch)

# File handler（DEBUG，全量日志，5×5 MB 轮转）
fh = RotatingFileHandler(LOG_FILE, maxBytes=5*1024*1024, backupCount=5, encoding='utf-8')
fh.setLevel(logging.DEBUG)
fh.setFormatter(formatter)
root.addHandler(fh)

# 专门调高需要的模块
for _mod in ("PipelineRunner", "plugins", "plugins.core", "plugins.image_process"):
    logging.getLogger(_mod).setLevel(logging.DEBUG)
# 降低海康相机日志级别，防止刷屏
logging.getLogger("core.camera_hikvision").setLevel(logging.INFO)



from PIL import Image, ImageTk  # type: ignore
from ttkbootstrap import Style
from ttkbootstrap.dialogs import Messagebox

# Optional heavy deps – import lazily where required
try:
    import numpy as np  # type: ignore
except ImportError:
    np = None  # type: ignore

# ---------------------------------------------------------------------------
# Domain models (provided by existing codebase)
# ---------------------------------------------------------------------------
from models.workstation_cfg import Workstation, load_workstations  # type: ignore
from models.pipeline_runner import PipelineRunner  # type: ignore

try:
    from core.camera_manager import CameraManager  # type: ignore
except Exception:
    CameraManager = None  # type: ignore

# ---------------------------------------------------------------------------
# Constants / globals
# ---------------------------------------------------------------------------
LAYOUT_OPTIONS = [1, 2, 4, 6, 8, 12]
SIDEBAR_MIN, SIDEBAR_MAX = 60, 150

__all__ = ["MainWindow"]


class MainWindow(tk.Tk):
    """Main application window."""

    # ---------------------------------------------------------------------
    # Construction helpers
    # ---------------------------------------------------------------------
    def __init__(self) -> None:
        super().__init__()

        # 立即设置深色背景避免白色闪烁
        self.configure(bg="#2b2b2b")

        # 初始化UI配置
        from ui_config import UIConfig
        self.ui_config = UIConfig()

        # 使用保存的主题
        theme_name = self.ui_config.get("theme", "superhero")
        self.style = Style(theme_name)

        self.title("SY Vision 2025 (Refactored)")

        # 设置窗口图标
        try:
            # 使用路径管理模块获取图标路径
            icon_paths = [
                get_resource_path("assets/sy_vision_app_icon.ico"),
                get_resource_path("assets/application_icon.ico"),
                get_resource_path("assets/main_app_icon.ico"),
                get_resource_path("assets/sy_vision_2025_icon.ico"),
                get_resource_path("assets/app_icon.ico"),
                get_resource_path("assets/logo_32x32.ico"),
                get_resource_path("assets/icon.ico")
            ]

            print(f"[DEBUG] 检查图标文件:")
            for i, icon_path in enumerate(icon_paths):
                exists = os.path.exists(icon_path)
                print(f"[DEBUG]   {i+1}. {os.path.basename(icon_path)}: {'存在' if exists else '不存在'}")
                if exists:
                    try:
                        self.iconbitmap(icon_path)
                        print(f"[DEBUG] ✓ 已设置窗口图标: {icon_path}")
                        break
                    except Exception as e:
                        print(f"[DEBUG] ✗ 设置图标失败: {e}")
            else:
                print("[DEBUG] ✗ 未找到可用的窗口图标文件")

        except Exception as e:
            print(f"[DEBUG] 设置图标失败: {e}")

        # 使用固定的较小窗口大小
        self.geometry("1024x720+100+100")
        self.minsize(1024, 600)

        # state holders ----------------------------------------------------
        self.sidebar_expanded = True
        self.current_layout = tk.IntVar(value=2)  # 默认显示2个工位

        # workflow / recipe state ------------------------------------
        self._pl_mode: str = "stop"  # run | pause | stop
        self._pl_single: bool = False  # single-step flag
        self._ws_runners: Dict[str, PipelineRunner] = {}
        self._ws_labels: Dict[str, ttk.Label] = {}
        self._ws_stats: Dict[str, Dict[str, int]] = {}

        # load default workstations / pipelines -----------------------
        self.workstations: List[Workstation] = []
        self._load_default_workstations()
        self.mode_var = tk.StringVar(value="连续调试")

        self._layout_rows = 2
        self._layout_cols = 2
        self._frames: Dict[str, Any] = {}
        self._photo_cache: Dict[str, ImageTk.PhotoImage] = {}
        self.canvas_map: Dict[str, tk.Canvas] = {}
        self._camera_order: List[str] = []
        self._capture_running = False

        # camera manager ---------------------------------------------------
        self._camera_mgr: Optional[CameraManager] = None
        if CameraManager is not None:
            cfg = Path(get_config_path("camera_config.yaml"))
            print(f"[DEBUG] 相机配置文件路径: {cfg}")
            if cfg.exists():
                try:
                    print("[DEBUG] 开始初始化相机管理器...")
                    self._camera_mgr = CameraManager(cfg)
                    print(f"[DEBUG] 相机管理器初始化成功，相机数量: {len(self._camera_mgr.cameras)}")
                except Exception as exc:
                    print(f"[ERROR] CameraManager init failed: {exc}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"[WARNING] 相机配置文件不存在: {cfg}")
        else:
            print("[ERROR] CameraManager 类未能导入")

        # 显示启动画面并异步初始化
        self._show_startup_splash()

    def _show_startup_splash(self):
        """显示启动画面并异步初始化应用程序"""
        try:
            import startup_splash

            # 隐藏主窗口
            self.withdraw()

            # 设置窗口图标
            self._set_window_icon()

            # 显示启动画面
            self.splash = startup_splash.show_startup_splash(self._initialize_main_app)

        except Exception as e:
            print(f"[DEBUG] 显示启动画面失败: {e}")
            self._initialize_main_app()

    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            # 使用路径管理模块获取图标路径
            icon_paths = [
                get_resource_path("assets/sy_vision_app_icon.ico"),
                get_resource_path("assets/app_icon.ico"),
                get_resource_path("assets/logo_32x32.ico"),
                get_resource_path("assets/icon.ico")
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.iconbitmap(icon_path)
                    print(f"[DEBUG] 已设置窗口图标: {icon_path}")
                    break
            else:
                print("[DEBUG] 未找到窗口图标文件")

        except Exception as e:
            print(f"[DEBUG] 设置图标失败: {e}")

    def _initialize_main_app(self):
        """初始化主应用程序"""
        try:
            # build GUI --------------------------------------------------------
            self._build_layout()
            self.protocol("WM_DELETE_WINDOW", self._on_close)

            # 添加窗口状态管理
            self._setup_window_state_management()

            # try start capture automatically after idle ----------------------
            self.after_idle(self._start_capture)
            # start preview refresh loop
            self.after_idle(self._refresh_preview)

            # 完成初始化
            self.after_idle(self._finish_initialization)

            # 启动系统清理服务
            self.after_idle(self._start_system_cleaner)

            # 启动系统日志服务
            self.after_idle(self._start_system_logger)

            print("[DEBUG] 主应用程序初始化完成")

        except Exception as e:
            print(f"[DEBUG] 主应用程序初始化失败: {e}")

    def _start_system_cleaner(self):
        """启动系统清理服务"""
        try:
            start_system_cleaner()
            print("[DEBUG] 系统清理服务已启动")
        except Exception as e:
            print(f"[DEBUG] 启动系统清理服务失败: {e}")

    def _start_system_logger(self):
        """启动系统日志服务"""
        try:
            self.system_logger = get_system_logger()
            log_system("SY Vision 2025 系统启动", "INFO", version="3.2")
            print("[DEBUG] 系统日志服务已启动")
        except Exception as e:
            print(f"[DEBUG] 启动系统日志服务失败: {e}")

    def _manual_system_cleanup(self):
        """手动触发系统清理"""
        try:
            from tkinter import messagebox

            # 确认对话框
            result = messagebox.askyesno(
                "系统清理",
                "确定要执行系统清理吗？\n\n将清理以下内容：\n"
                "• 过期的捕获图像\n"
                "• 旧的日志文件\n"
                "• 测试输出文件\n"
                "• Python缓存文件\n"
                "• 临时配置文件",
                icon="question"
            )

            if result:
                # 在后台线程中执行清理
                def cleanup_thread():
                    try:
                        cleaner = get_system_cleaner()
                        cleaner.manual_cleanup()

                        # 在主线程中显示结果
                        self.after(0, lambda: messagebox.showinfo(
                            "清理完成",
                            "系统清理已完成！\n请查看日志了解详细信息。",
                            icon="info"
                        ))
                    except Exception as e:
                        self.after(0, lambda: messagebox.showerror(
                            "清理失败",
                            f"系统清理失败：{e}",
                            icon="error"
                        ))

                import threading
                threading.Thread(target=cleanup_thread, daemon=True).start()

                # 显示进度提示
                messagebox.showinfo(
                    "清理中",
                    "系统清理正在后台进行...\n完成后会有通知。",
                    icon="info"
                )

        except Exception as e:
            print(f"[DEBUG] 手动清理失败: {e}")
            try:
                from tkinter import messagebox
                messagebox.showerror("错误", f"启动清理失败：{e}")
            except:
                pass

    def _open_cleaner_config(self):
        """打开清理配置界面"""
        try:
            from system_config_ui.cleaner_config_ui import open_cleaner_config
            open_cleaner_config(self)
        except Exception as e:
            print(f"[DEBUG] 打开清理配置失败: {e}")
            try:
                from tkinter import messagebox
                messagebox.showerror("错误", f"打开清理配置失败：{e}")
            except:
                pass

    def _finish_initialization(self):
        """完成初始化，显示主窗口并关闭启动画面"""
        try:
            # 恢复窗口状态
            self._restore_window_state()

            # 关闭启动画面
            if hasattr(self, 'splash') and self.splash:
                self.splash.close_splash()

            print("[DEBUG] 主界面已显示，启动画面已关闭")

        except Exception as e:
            print(f"[DEBUG] 完成初始化失败: {e}")
            # 确保主窗口显示
            self._force_show_window()

    def _restore_window_state(self):
        """恢复窗口状态"""
        try:
            # 获取保存的窗口状态
            saved_state = self.ui_config.get("window_state", "normal")

            # 强制显示窗口
            self.deiconify()
            self.lift()
            self.focus_force()

            # 确保窗口在屏幕上可见
            self.update_idletasks()

            print(f"[DEBUG] 窗口状态已恢复: {saved_state}")

        except Exception as e:
            print(f"[DEBUG] 恢复窗口状态失败: {e}")
            self._force_show_window()

    def _force_show_window(self):
        """强制显示窗口"""
        try:
            # 多种方法确保窗口显示
            self.withdraw()  # 先隐藏
            self.deiconify()  # 再显示
            self.lift()  # 提升到前台
            self.focus_force()  # 强制获得焦点
            self.attributes('-topmost', True)  # 临时置顶
            self.after(100, lambda: self.attributes('-topmost', False))  # 100ms后取消置顶

            print("[DEBUG] 强制显示窗口完成")

        except Exception as e:
            print(f"[DEBUG] 强制显示窗口失败: {e}")

    def _setup_window_state_management(self):
        """设置窗口状态管理"""
        try:
            # 绑定窗口状态变化事件
            self.bind("<Map>", self._on_window_map)
            self.bind("<Unmap>", self._on_window_unmap)
            self.bind("<FocusIn>", self._on_window_focus_in)
            self.bind("<Button-1>", self._on_window_click)  # 鼠标点击事件

            # 添加窗口状态变量
            self._window_state = "normal"  # normal, minimized, withdrawn
            self._last_geometry = None
            self._minimized_time = None

            # 设置窗口属性，确保可以从任务栏恢复
            try:
                self.attributes('-toolwindow', False)  # 确保在任务栏显示
            except:
                pass

            # 定期检查窗口状态
            self._check_window_state()

            print("[DEBUG] 窗口状态管理已设置")

        except Exception as e:
            print(f"[DEBUG] 设置窗口状态管理失败: {e}")

    def _check_window_state(self):
        """定期检查窗口状态"""
        try:
            current_state = self.state()

            # 如果窗口被最小化超过一定时间，确保可以恢复
            if current_state == "iconic" and self._window_state != "minimized":
                self._window_state = "minimized"
                self._minimized_time = time.time()
                print("[DEBUG] 检测到窗口最小化")
            elif current_state == "normal" and self._window_state == "minimized":
                self._window_state = "normal"
                self._minimized_time = None
                print("[DEBUG] 检测到窗口恢复")

        except Exception as e:
            pass  # 忽略状态检查错误

        # 每秒检查一次
        self.after(1000, self._check_window_state)

    def _on_window_map(self, event):
        """窗口显示事件"""
        if event.widget == self:
            self._window_state = "normal"
            print("[DEBUG] 窗口已显示")

    def _on_window_unmap(self, event):
        """窗口隐藏事件"""
        if event.widget == self:
            # 检查是否是最小化
            try:
                state = self.state()
                if state == "iconic":
                    self._window_state = "minimized"
                    print("[DEBUG] 窗口已最小化")
                elif state == "withdrawn":
                    self._window_state = "withdrawn"
                    print("[DEBUG] 窗口已隐藏")
            except:
                pass

    def _on_window_focus_in(self, event):
        """窗口获得焦点事件"""
        if event.widget == self:
            # 确保窗口正常显示
            try:
                if self._window_state == "minimized":
                    self.deiconify()
                    self._window_state = "normal"
                    print("[DEBUG] 窗口从最小化恢复")
            except Exception as e:
                print(f"[DEBUG] 恢复窗口失败: {e}")

    def _on_window_click(self, event):
        """窗口点击事件"""
        # 确保窗口处于正常状态
        try:
            if self._window_state == "minimized":
                self._force_show_window()
        except Exception as e:
            print(f"[DEBUG] 处理窗口点击失败: {e}")

    # =====================================================================
    # UI building helpers
    # =====================================================================
    def _build_layout(self) -> None:
        # 创建分割窗口，设置最细的分割线
        self.paned = ttk.Panedwindow(self, orient=tk.HORIZONTAL)
        self.paned.pack(fill=tk.BOTH, expand=True)

        # 直接配置分割线样式 - 尽可能隐藏分割线
        try:
            # 尝试设置最小的分割线宽度和样式
            self.paned.configure(
                sashwidth=1,      # 最小宽度
                sashrelief="flat", # 平面效果
                sashpad=0,        # 无内边距
                borderwidth=0     # 无边框
            )
        except Exception as e:
            print(f"[DEBUG] 配置分割线样式失败: {e}")
            # 基本配置
            try:
                self.paned.configure(sashwidth=1)
            except:
                pass

        self.sidebar = ttk.Frame(self.paned, width=SIDEBAR_MAX, padding=(5, 10), style="Sidebar.TFrame")
        self.main_frame = ttk.Frame(self.paned)
        self.paned.add(self.sidebar, weight=0)
        self.paned.add(self.main_frame, weight=1)

        # 确保侧边栏可见
        self.sidebar_expanded = True

        self._build_sidebar()
        self._build_header()
        self._build_status_bar()
        self._build_notebook()
        self._apply_custom_styles()

        # 强制设置侧边栏固定宽度
        self.after(100, self._fix_sidebar_width)

        # 延迟优化分割线外观
        self.after(200, self._optimize_sash_appearance)

    # ---------------- Sidebar ------------------------------------------
    def _build_sidebar(self) -> None:
        # 创建logo和标题的容器
        brand_frame = ttk.Frame(self.sidebar)
        brand_frame.pack(pady=(0, 20))

        # 尝试加载logo图片
        self.logo_image = None
        try:
            from PIL import Image, ImageTk, ImageDraw
            import numpy as np

            # 使用专门优化的侧边栏logo
            logo_paths = [
                get_resource_path("assets/sidebar_logo.png"),
                get_resource_path("assets/logo_48x48.png"),
                get_resource_path("assets/logo_64x64.png"),
                get_resource_path("icon.ico")
            ]

            for logo_path in logo_paths:
                if os.path.exists(logo_path):
                    # 加载图片
                    pil_image = Image.open(logo_path)

                    # 转换为RGBA模式以支持透明度
                    if pil_image.mode != 'RGBA':
                        pil_image = pil_image.convert('RGBA')

                    # 如果不是48x48，调整大小
                    if pil_image.size != (48, 48):
                        pil_image = pil_image.resize((48, 48), Image.Resampling.LANCZOS)

                    # 直接使用处理后的图片
                    self.logo_image = ImageTk.PhotoImage(pil_image)
                    print(f"[DEBUG] 已加载侧边栏logo: {logo_path}")
                    break
        except Exception as e:
            print(f"[DEBUG] 加载logo失败: {e}")

        # 如果成功加载logo，显示logo和文字
        if self.logo_image:
            # logo图片
            logo_label = ttk.Label(brand_frame, image=self.logo_image)
            logo_label.pack(pady=(0, 5))

            # 品牌文字（较小字体）
            self.brand_label = ttk.Label(brand_frame, text="SY Vision",
                                       font=(self.ui_config.get("font_family", "微软雅黑"), 12, "bold"),
                                       style="Brand.TLabel")
            self.brand_label.pack()
        else:
            # 如果没有logo，只显示文字
            self.brand_label = ttk.Label(brand_frame, text="SY Vision",
                                       font=self.ui_config.get_font("title"),
                                       style="Brand.TLabel")
            self.brand_label.pack()
        self._nav_buttons: List[ttk.Button] = []
        self._add_nav_btn("实时监控", self._show_preview)
        self._add_nav_btn("配置中心", self._open_config_center)
        self._add_nav_btn("流程配置", self._open_pipeline_editor)
        self._add_nav_btn("工位配置", self._open_workstation_cfg)
        # ---- 新增：相机标定工具 ----
        self._add_nav_btn("相机标定", self._open_camera_calibrate)
        self._add_nav_btn("系统日志", self._show_log_tab)
        self._add_nav_btn("导入配方", self._import_recipe_dialog)
        self._add_nav_btn("配方配置", self._open_recipe_editor)
        self._add_nav_btn("系统清理", self._manual_system_cleanup)
        self._add_nav_btn("清理配置", self._open_cleaner_config)
        ttk.Separator(self.sidebar).pack(fill=tk.X, pady=10)
        self._collapse_btn = ttk.Button(self.sidebar, text="<<", width=4, command=self._toggle_sidebar)
        self._collapse_btn.pack(side=tk.BOTTOM, pady=10)

    def _add_nav_btn(self, text: str, cmd):
        btn = ttk.Button(self.sidebar, text=text, command=cmd, width=12)
        btn.pack(pady=5)
        self._nav_buttons.append(btn)

    def _fix_sidebar_width(self):
        """强制设置侧边栏固定宽度"""
        try:
            # 强制设置侧边栏固定宽度
            self.paned.sashpos(0, SIDEBAR_MAX)
            # 禁用侧边栏的自动调整大小
            self.sidebar.pack_propagate(False)
            # 绑定窗口大小变化事件，保持侧边栏宽度
            self.bind("<Configure>", self._on_window_resize)
            print(f"[DEBUG] 侧边栏宽度已固定为: {SIDEBAR_MAX}px")
        except Exception as e:
            print(f"[DEBUG] 设置侧边栏宽度失败: {e}")

    def _optimize_sash_appearance(self):
        """优化分割线外观，让它尽可能不显眼"""
        try:
            # 获取背景色
            colors = self.style.colors
            bg_color = colors.bg

            # 尝试多种方法来隐藏或淡化分割线
            print(f"[DEBUG] 开始优化分割线，背景色: {bg_color}")

            # 方法1：设置分割线颜色与背景相同
            try:
                self.paned.configure(background=bg_color)
                print("[DEBUG] 方法1: 设置分割线背景色成功")
            except Exception as e:
                print(f"[DEBUG] 方法1失败: {e}")

            # 方法2：通过样式设置分割线
            try:
                self.style.configure("TPanedwindow.Sash", background=bg_color, relief="flat")
                print("[DEBUG] 方法2: 设置分割线样式成功")
            except Exception as e:
                print(f"[DEBUG] 方法2失败: {e}")

            # 方法3：尝试让分割线几乎不可见
            try:
                # 设置分割线的映射样式
                self.style.map("TPanedwindow",
                             background=[("active", bg_color), ("!active", bg_color)],
                             relief=[("active", "flat"), ("!active", "flat")])
                print("[DEBUG] 方法3: 设置分割线映射样式成功")
            except Exception as e:
                print(f"[DEBUG] 方法3失败: {e}")

            print("[DEBUG] 分割线外观优化完成")

        except Exception as e:
            print(f"[DEBUG] 分割线外观优化失败: {e}")

    def _on_window_resize(self, event=None):
        """窗口大小变化时保持侧边栏宽度"""
        if event and event.widget == self:
            try:
                # 延迟执行，确保窗口调整完成
                self.after_idle(lambda: self.paned.sashpos(0, SIDEBAR_MAX))
            except:
                pass

    def _toggle_sidebar(self):
        if self.sidebar_expanded:
            for b in self._nav_buttons:
                b.pack_forget()
            self.paned.sashpos(0, SIDEBAR_MIN)
            self._collapse_btn.config(text=">>")
            self.sidebar_expanded = False
        else:
            self.paned.sashpos(0, SIDEBAR_MAX)
            for b in self._nav_buttons:
                b.pack(pady=5)
            self._collapse_btn.config(text="<<")
            self.sidebar_expanded = True
        self.sidebar.update_idletasks()

    # ---------------- Header -------------------------------------------
    def _build_header(self):
        hdr = ttk.Frame(self.main_frame, padding=(5, 2), style="Header.TFrame")
        hdr.pack(fill=tk.X)

        # 中间：系统标题（居中显示）
        title_frame = ttk.Frame(hdr)
        title_frame.pack(expand=True)
        self.system_title = ttk.Label(title_frame, text="十易技术视觉检测系统V3.2",
                                     font=(self.ui_config.get("font_family", "微软雅黑"), 14, "bold"),
                                     style="Title.TLabel")
        self.system_title.pack()

        # 右侧：界面设置按钮
        ttk.Button(hdr, text="界面设置", command=self._open_ui_settings).pack(side=tk.RIGHT, padx=5)

    # ---------------- Status bar ---------------------------------------
    def _build_status_bar(self):
        self.status_var = tk.StringVar(value="准备就绪")
        self.ok_var = tk.IntVar(value=0)
        self.ng_var = tk.IntVar(value=0)
        self.total_var = tk.IntVar(value=0)

        bar = ttk.Frame(self.main_frame, padding=(5, 0), style="Status.TFrame")
        ttk.Label(bar, textvariable=self.status_var, anchor="w").pack(side=tk.LEFT)
        ttk.Label(bar, textvariable=self.total_var, foreground="white", background="#3498db", padding=(6, 1)).pack(side=tk.RIGHT, padx=2)
        ttk.Label(bar, text="总数").pack(side=tk.RIGHT)
        ttk.Label(bar, textvariable=self.ng_var, foreground="white", background="#e74c3c", padding=(6, 1)).pack(side=tk.RIGHT, padx=2)
        ttk.Label(bar, text="NG").pack(side=tk.RIGHT)
        ttk.Label(bar, textvariable=self.ok_var, foreground="white", background="#2ecc71", padding=(6, 1)).pack(side=tk.RIGHT, padx=2)
        ttk.Label(bar, text="OK").pack(side=tk.RIGHT)
        ttk.Button(bar, text="清零", width=4, command=self._reset_counter).pack(side=tk.RIGHT, padx=6)
        bar.pack(side=tk.BOTTOM, fill=tk.X)
        bar.configure(height=26)
        bar.pack_propagate(False)

    # ---------------- Notebook & tabs ----------------------------------
    def _build_notebook(self):
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        self._build_preview_tab()

    def _show_preview(self):
        if not hasattr(self, "preview_tab") or self.preview_tab is None or not self.preview_tab.winfo_exists():
            self._build_preview_tab()
        self.notebook.select(self.preview_tab)

    # ------------------------------------------------------------------
    # Preview tab
    # ------------------------------------------------------------------
    def _build_preview_tab(self):
        self.preview_tab = ttk.Frame(self.notebook, style="Preview.TFrame")
        self.notebook.add(self.preview_tab, text="实时监控")

        bar = ttk.Frame(self.preview_tab)
        bar.pack(fill=tk.X)
        ttk.Label(bar, text="布局:").pack(side=tk.LEFT, padx=(10, 5))
        cbo = ttk.Combobox(bar, values=LAYOUT_OPTIONS, width=5, state="readonly", textvariable=self.current_layout)
        cbo.pack(side=tk.LEFT)
        cbo.bind("<<ComboboxSelected>>", lambda _e: self._set_layout(self.current_layout.get()))

        # mode switch ----------------------------------------------------
        ttk.Label(bar, text="模式:").pack(side=tk.LEFT, padx=(15, 5))
        mode_cbo = ttk.Combobox(bar, values=["运行", "连续调试", "软触发调试"], width=10, state="readonly", textvariable=self.mode_var)
        mode_cbo.pack(side=tk.LEFT)
        mode_cbo.bind("<<ComboboxSelected>>", lambda _e: self._on_mode_change())

        # 统一显示选项 --------------------------------------------
        self.uniform_display_var = tk.BooleanVar(value=True)  # 默认开启统一显示
        uniform_check = ttk.Checkbutton(bar, text="统一显示大小", variable=self.uniform_display_var,
                                       command=self._on_uniform_display_change)
        uniform_check.pack(side=tk.LEFT, padx=(15, 5))
        ttk.Button(bar, text="相机设置...", command=self._open_camera_settings).pack(side=tk.LEFT, padx=10)

        # pipeline buttons (right-aligned) ------------------------------
        self.pl_run_btn = ttk.Button(bar, text="流程运行", width=8, command=self._pl_run)
        self.pl_single_btn = ttk.Button(bar, text="单步流程", width=8, command=self._pl_single_step)
        self.pl_pause_btn = ttk.Button(bar, text="暂停流程", width=8, command=self._pl_pause_toggle)
        self.pl_stop_btn = ttk.Button(bar, text="停止流程", width=8, command=self._pl_stop)
        for _b in (self.pl_stop_btn, self.pl_pause_btn, self.pl_single_btn, self.pl_run_btn):
            _b.pack(side=tk.RIGHT, padx=2)

        # soft-trigger button (only for mode == 软触发调试) -------------
        self.soft_btn = ttk.Button(bar, text="软触发一次", width=8, command=self._soft_trigger)
        self.soft_btn.pack(side=tk.RIGHT, padx=10)
        self.soft_btn.config(state=tk.DISABLED)

        # preview frame --------------------------------------------------
        self.preview_frame = ttk.Frame(self.preview_tab, style="Preview.TFrame")
        self.preview_frame.pack(fill=tk.BOTH, expand=True)
        self.preview_frame.bind("<Configure>", lambda _e: self._on_preview_resize())
        self._set_layout(self.current_layout.get())
        self._update_pl_buttons()

    # ---------------- Layout helper -----------------------------------
    def _set_layout(self, n: int):
        for child in self.preview_frame.winfo_children():
            child.destroy()
        self.canvas_map.clear()

        total_cells = max(1, int(n))
        cols = math.ceil(math.sqrt(total_cells))
        rows = math.ceil(total_cells / cols)
        self._layout_rows, self._layout_cols = rows, cols

        for r in range(rows):
            self.preview_frame.grid_rowconfigure(r, weight=1)
        for c in range(cols):
            self.preview_frame.grid_columnconfigure(c, weight=1)

        ws_count = len(self.workstations)
        for i in range(total_cells):
            # 优化预览窗口边框 - 减少padding和边框
            frm = ttk.Frame(self.preview_frame, style="Preview.TFrame", padding=0)
            frm.grid(row=i // cols, column=i % cols, sticky="nsew", padx=1, pady=1)
            # 固定使用黑色背景
            cvs = tk.Canvas(frm, background="#000000", highlightthickness=0)
            cvs.pack(fill=tk.BOTH, expand=True)

            # 绑定Canvas大小变化事件，重新绘制网格
            # 使用默认参数避免闭包问题
            cvs.bind('<Configure>', lambda e, canvas=cvs: self._draw_grid_on_canvas(canvas))

            # 为每个工位格子创建标签，显示工位编号
            if i < ws_count:
                # 有实际工位的情况
                ws = self.workstations[i]
                stat_lbl = ttk.Label(frm, text=ws.name, anchor="center")
                stat_lbl.pack(fill=tk.X)
                self._ws_labels[ws.camera_id] = stat_lbl
                self._ws_stats[ws.camera_id] = {"ok": 0, "ng": 0}
                self.canvas_map[ws.camera_id] = cvs
            else:
                # 没有实际工位的情况，显示工位编号
                placeholder_name = f"工位{i+1}"
                stat_lbl = ttk.Label(frm, text=placeholder_name, anchor="center")
                stat_lbl.pack(fill=tk.X)
                self.canvas_map[f"placeholder_{i}"] = cvs

        # 强制更新所有Canvas背景色为黑色
        self._force_update_canvas_background()

        # clear preview and trigger refresh soon

    def _on_preview_resize(self):
        """窗口尺寸变化时刷新预览，加入 200 ms 去抖动，防止频繁重绘导致卡顿。"""
        # 删除旧图像，避免尺寸不匹配的残影
        for cvs in self.canvas_map.values():
            cvs.delete("_img")

        # 若已有排队的刷新任务，先取消
        if getattr(self, "_resize_job", None):
            try:
                self.after_cancel(self._resize_job)
            except Exception:
                pass

        # 200 ms 后刷新；若在这段时间内再次触发，将被重新排队
        self._resize_job = self.after(200, self._refresh_preview)

    # ---------------- Camera frame callback ---------------------------
    def _on_frame(self, cam_id: str, frame):
        log = logging.getLogger("MainWindow.on_frame")
        """处理相机帧回调"""
        log.debug("Frame received from %s, is None? %s", cam_id, frame is None)
        if frame is not None:
            runner = self._ws_runners.get(cam_id)
            process_allowed = runner is not None and ((self._pl_mode == "run") or self._pl_single)
            log.debug("process_allowed=%s, _pl_mode=%s, _pl_single=%s", process_allowed, self._pl_mode, self._pl_single)
        
            if process_allowed and frame is not None:
                try:
                    frame_out, ctx = runner.process(frame)
                    frame = frame_out

                    # 更新工位统计
                    self._update_workstation_statistics(cam_id, ctx)

                    # 更新总体统计（基于所有工位的统计）
                    self._update_global_statistics()

                except Exception as exc:
                    print("pipeline process error", exc)
                if self._pl_single:
                    self._pl_single = False
                    self._pl_mode = "pause"
                    self._update_pl_buttons()

        # 若标记了保存下一帧，则保存并清除标记
        if getattr(self, "_save_next_frames", False) and frame is not None:
            try:
                from pathlib import Path
                from datetime import datetime
                save_dir = Path(get_capture_path(cam_id))
                save_dir.mkdir(parents=True, exist_ok=True)
                ts = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                fname = f"{ts}_{cam_id}.png"
                # frame 可能是 numpy / PIL；统一用 PIL 保存
                if hasattr(frame, "save"):
                    frame.save(save_dir / fname)
                else:
                    from PIL import Image
                    import numpy as np
                    if isinstance(frame, np.ndarray):
                        Image.fromarray(frame).save(save_dir / fname)
            except Exception as exc:
                print("save frame error", exc)
            finally:
                self._save_next_frames = False

        self._frames[cam_id] = frame

    # ==================================================================
    # Capture & preview loop
    # ==================================================================
    def _start_capture(self):
        if self._capture_running or self._camera_mgr is None:
            return

        # ensure camera workers are freshly created
        try:
            self._camera_mgr.reload()
        except Exception:
            pass

        mode = self.mode_var.get()
        for cam in self._camera_mgr.cameras.values():
            try:
                if not getattr(cam, "opened", True):
                    cam.open()
                if mode == "运行":
                    cam.start_grab(continuous=False)
                elif mode == "连续调试":
                    cam.start_grab(continuous=True)
                else:
                    cam.start_grab_software()
            except Exception as exc:
                # print(f"DEBUG: 相机 {cam.camera_id} 启动失败: {exc}")
                pass  # 相机启动失败，静默处理

        for worker in self._camera_mgr.workers.values():
            worker.push_callback = self._on_frame

        try:
            self._camera_mgr.start_all()
        except Exception as exc:
            Messagebox.show_error("启动失败", str(exc))
            return

        self._camera_order = list(self._camera_mgr.cameras.keys())
        self._capture_running = True
        self.status_var.set(f"{mode}中")
        self.soft_btn.config(state=tk.NORMAL if mode == "软触发调试" and self._capture_running else tk.DISABLED)
        self._update_pl_buttons()

    def _stop_capture(self):
        if not self._capture_running:
            return
        self._capture_running = False
        if self._camera_mgr:
            try:
                self._camera_mgr.stop_all()
            except Exception as exc:
                print("stop_all error", exc)
        self.status_var.set("已停止")
        self.soft_btn.config(state=tk.DISABLED)
        self._update_pl_buttons()

    # ---------------- Mode change -------------------------------------
    def _on_mode_change(self):
        new_mode = self.mode_var.get()

        # 记录模式切换日志
        try:
            log_operation("系统", "模式切换", new_mode, {"previous_mode": getattr(self, '_current_mode', '未知')})
            self._current_mode = new_mode
        except Exception as e:
            print(f"[DEBUG] 记录模式切换日志失败: {e}")

        self.soft_btn.config(state=tk.NORMAL if new_mode == "软触发调试" and self._capture_running else tk.DISABLED)
        if self._capture_running:
            self._stop_capture()
            self.after_idle(self._start_capture)

    def _on_uniform_display_change(self):
        """统一显示大小变化处理"""
        uniform_enabled = self.uniform_display_var.get()

        # 清理缓存，强制重新计算
        if hasattr(self, '_uniform_scale'):
            delattr(self, '_uniform_scale')

        # 记录显示模式切换日志
        try:
            log_operation("用户", "统一显示切换", "开启" if uniform_enabled else "关闭")
        except Exception as e:
            print(f"[DEBUG] 记录统一显示切换日志失败: {e}")

        print(f"[DEBUG] 统一显示大小: {'开启' if uniform_enabled else '关闭'}")

        # 立即刷新预览以应用新的显示模式
        self._refresh_preview()

    def _force_update_canvas_background(self):
        """强制更新所有Canvas背景色为网格状黑色"""
        for cvs in self.canvas_map.values():
            if cvs and cvs.winfo_exists():
                try:
                    cvs.configure(bg="#000000")
                    # 绘制网格
                    self._draw_grid_on_canvas(cvs)
                except Exception as e:
                    print(f"[DEBUG] 更新Canvas背景色失败: {e}")
        print("[DEBUG] 所有工位Canvas背景色已强制更新为网格状黑色")

    def _draw_grid_on_canvas(self, canvas):
        """在Canvas上绘制专业的视觉检测背景"""
        try:
            # 清除之前的背景元素
            canvas.delete("grid_line")
            canvas.delete("background_element")

            # 获取Canvas尺寸
            canvas.update_idletasks()
            width = canvas.winfo_width()
            height = canvas.winfo_height()

            if width <= 1 or height <= 1:
                # Canvas尺寸无效，稍后重试
                canvas.after(100, lambda: self._draw_grid_on_canvas(canvas))
                return

            # 专业视觉检测背景设计
            self._draw_professional_background(canvas, width, height)

            print(f"[DEBUG] 专业背景绘制完成: {width}x{height}")

        except Exception as e:
            print(f"[DEBUG] 绘制专业背景失败: {e}")

    def _draw_professional_background(self, canvas, width, height):
        """绘制专业的视觉检测背景，类似VisionPro/Halcon风格"""

        # 背景色渐变效果 - 深色专业风格
        bg_color = "#1a1a1a"  # 深灰色背景

        # 主网格 - 较大间距，细线
        major_grid_size = 50
        major_grid_color = "#333333"  # 深灰色主网格

        # 次网格 - 较小间距，更细的线
        minor_grid_size = 10
        minor_grid_color = "#262626"  # 更深的次网格

        # 绘制次网格（细密网格）
        for x in range(0, width, minor_grid_size):
            canvas.create_line(x, 0, x, height, fill=minor_grid_color, width=1, tags="background_element")
        for y in range(0, height, minor_grid_size):
            canvas.create_line(0, y, width, y, fill=minor_grid_color, width=1, tags="background_element")

        # 绘制主网格（粗网格）
        for x in range(0, width, major_grid_size):
            canvas.create_line(x, 0, x, height, fill=major_grid_color, width=1, tags="background_element")
        for y in range(0, height, major_grid_size):
            canvas.create_line(0, y, width, y, fill=major_grid_color, width=1, tags="background_element")

        # 添加中心十字线（类似Halcon的坐标系）
        center_x = width // 2
        center_y = height // 2
        cross_color = "#404040"

        # 中心垂直线
        canvas.create_line(center_x, 0, center_x, height, fill=cross_color, width=2, tags="background_element")
        # 中心水平线
        canvas.create_line(0, center_y, width, center_y, fill=cross_color, width=2, tags="background_element")

        # 添加角落标记（专业视觉软件常见的边界标记）
        corner_size = 20
        corner_color = "#505050"
        corner_width = 2

        # 左上角
        canvas.create_line(0, 0, corner_size, 0, fill=corner_color, width=corner_width, tags="background_element")
        canvas.create_line(0, 0, 0, corner_size, fill=corner_color, width=corner_width, tags="background_element")

        # 右上角
        canvas.create_line(width-corner_size, 0, width, 0, fill=corner_color, width=corner_width, tags="background_element")
        canvas.create_line(width, 0, width, corner_size, fill=corner_color, width=corner_width, tags="background_element")

        # 左下角
        canvas.create_line(0, height-corner_size, 0, height, fill=corner_color, width=corner_width, tags="background_element")
        canvas.create_line(0, height, corner_size, height, fill=corner_color, width=corner_width, tags="background_element")

        # 右下角
        canvas.create_line(width, height-corner_size, width, height, fill=corner_color, width=corner_width, tags="background_element")
        canvas.create_line(width-corner_size, height, width, height, fill=corner_color, width=corner_width, tags="background_element")

        # 添加刻度标记（类似测量工具的刻度）
        self._draw_scale_marks(canvas, width, height)

    def _draw_scale_marks(self, canvas, width, height):
        """绘制刻度标记，类似专业测量工具"""
        scale_color = "#404040"
        scale_interval = 50  # 刻度间距
        major_tick_length = 8  # 主刻度长度
        minor_tick_length = 4  # 次刻度长度

        # 顶部刻度
        for x in range(0, width, scale_interval):
            if x % (scale_interval * 2) == 0:  # 主刻度
                canvas.create_line(x, 0, x, major_tick_length, fill=scale_color, width=1, tags="background_element")
            else:  # 次刻度
                canvas.create_line(x, 0, x, minor_tick_length, fill=scale_color, width=1, tags="background_element")

        # 左侧刻度
        for y in range(0, height, scale_interval):
            if y % (scale_interval * 2) == 0:  # 主刻度
                canvas.create_line(0, y, major_tick_length, y, fill=scale_color, width=1, tags="background_element")
            else:  # 次刻度
                canvas.create_line(0, y, minor_tick_length, y, fill=scale_color, width=1, tags="background_element")



    # ==================================================================
    # Soft trigger callback (only for "软触发调试" mode)
    # ==================================================================
    def _soft_trigger(self):
        """Send a single software trigger to every opened camera and mark next frames to be saved."""
        if not self._capture_running or self._camera_mgr is None:
            return
        # 标记下一帧需要保存
        self._save_next_frames = True  
        for cam in self._camera_mgr.cameras.values():
            try:
                if hasattr(cam, "software_trigger"):
                    cam.software_trigger()
                elif hasattr(cam, "trigger_once"):
                    cam.trigger_once()
            except Exception as exc:
                print("software_trigger error", exc)
        self.status_var.set("已发送软触发，并将保存下一帧图片")

    # ==================================================================
    # Preview refresh
    # ==================================================================
    def _refresh_preview(self):
        """定期刷新预览画面"""
        if not hasattr(self, '_refresh_count'):
            self._refresh_count = 0
        
        if not self._capture_running:
            self.after(100, self._refresh_preview)
            return
        if not self.canvas_map:
            self.after(100, self._refresh_preview)
            return

        frames_displayed = 0
        for idx, cam_id in enumerate(self._camera_order):
            cvs = self.canvas_map.get(cam_id) or self.canvas_map.get(str(idx))
            if cvs is None:
                continue
                
            frame = self._frames.get(cam_id)
            if frame is None:
                continue
            
            if isinstance(frame, Image.Image):
                img = frame.copy()
            else:
                try:
                    if np is not None:
                        img = Image.fromarray(np.array(frame)[..., ::-1])  
                    else:
                        continue
                except Exception as e:
                    continue

            w, h = cvs.winfo_width(), cvs.winfo_height()
            if w < 2 or h < 2:
                continue

            iw, ih = img.size

            # 检查是否启用统一显示大小
            uniform_display = getattr(self, 'uniform_display_var', None) and self.uniform_display_var.get()

            if uniform_display:
                # 统一显示模式：使用缓存的缩放比例，减少计算
                if not hasattr(self, '_uniform_scale'):
                    # 只计算一次，基于固定的目标尺寸
                    self._uniform_scale = 0.6  # 固定缩放比例，避免重复计算

                scale = min(self._uniform_scale, min(w / iw, h / ih, 1.0))

                if scale < 1.0:
                    new_w, new_h = int(iw * scale), int(ih * scale)
                    img = img.resize((new_w, new_h), Image.LANCZOS)
            else:
                # 原有逻辑：适应窗口大小，但不放大
                scale = min(w / iw, h / ih, 1.0)  # 添加1.0限制，不放大
                if scale < 1:
                    img = img.resize((int(iw * scale), int(ih * scale)), Image.LANCZOS)

            photo = ImageTk.PhotoImage(img)
            self._photo_cache[cam_id] = photo
            cvs.delete("_img")
            cvs.delete("_resolution_info")  # 清除之前的分辨率信息
            x, y = w // 2, h // 2
            cvs.create_image(x, y, anchor="center", image=photo, tags="_img")

            # 添加分辨率信息显示
            self._draw_resolution_info(cvs, cam_id, iw, ih, w, h)

            frames_displayed += 1
        
        self.after(100, self._refresh_preview)  # 降低刷新频率，减少卡顿

    def _draw_resolution_info(self, canvas, cam_id, img_width, img_height, canvas_width, canvas_height):
        """在Canvas上绘制图像分辨率信息"""
        try:
            # 分辨率信息文本
            resolution_text = f"{img_width}×{img_height}"

            # 文本样式
            text_color = "#00FF00"  # 绿色文字，类似专业软件
            bg_color = "#000000"    # 黑色背景
            font_size = 10

            # 计算文本位置（左上角）
            text_x = 8
            text_y = 8

            # 绘制背景矩形（半透明效果）
            text_bbox = canvas.bbox("current") if canvas.find_all() else None

            # 创建文本背景
            canvas.create_rectangle(
                text_x - 4, text_y - 2,
                text_x + len(resolution_text) * 7, text_y + font_size + 2,
                fill=bg_color, outline="#333333", width=1,
                tags="_resolution_info"
            )

            # 创建分辨率文本
            canvas.create_text(
                text_x, text_y,
                text=resolution_text,
                fill=text_color,
                font=("Consolas", font_size, "bold"),
                anchor="nw",
                tags="_resolution_info"
            )

            # 如果有像素标定信息，也显示物理尺寸
            if hasattr(self, '_pixel_calibration') and self._pixel_calibration:
                mm_per_px = self._pixel_calibration.get('mm_per_px', 0)
                if mm_per_px > 0:
                    physical_width = img_width * mm_per_px
                    physical_height = img_height * mm_per_px
                    physical_text = f"({physical_width:.1f}×{physical_height:.1f}mm)"

                    # 物理尺寸文本位置（分辨率文本下方）
                    phys_y = text_y + font_size + 4

                    # 创建物理尺寸背景
                    canvas.create_rectangle(
                        text_x - 4, phys_y - 2,
                        text_x + len(physical_text) * 6, phys_y + font_size + 2,
                        fill=bg_color, outline="#333333", width=1,
                        tags="_resolution_info"
                    )

                    # 创建物理尺寸文本
                    canvas.create_text(
                        text_x, phys_y,
                        text=physical_text,
                        fill="#FFFF00",  # 黄色文字
                        font=("Consolas", font_size-1, "normal"),
                        anchor="nw",
                        tags="_resolution_info"
                    )

        except Exception as e:
            print(f"[DEBUG] 绘制分辨率信息失败: {e}")

    # ==================================================================
    # Pipeline button callbacks
    # ==================================================================
    def _pl_run(self):
        self._pl_mode = "run"
        self.status_var.set("流程连续运行中")
        self._update_pl_buttons()

    def _pl_pause_toggle(self):
        if self._pl_mode == "run":
            self._pl_mode = "pause"
            self.status_var.set("流程已暂停")
        elif self._pl_mode == "pause":
            self._pl_mode = "run"
            self.status_var.set("流程连续运行中")
        self._update_pl_buttons()

    def _pl_single_step(self):
        if self._pl_mode == "stop":
            self._pl_mode = "pause"
        self._pl_single = True
        self.status_var.set("单步执行")

    def _pl_stop(self):
        self._pl_mode = "stop"
        self._pl_single = False
        self.status_var.set("流程已停止")
        self._update_pl_buttons()

    def _update_pl_buttons(self):
        if self._pl_mode == "run":
            self.pl_run_btn.config(state=tk.DISABLED)
            self.pl_pause_btn.config(state=tk.NORMAL, text="暂停流程")
            self.pl_stop_btn.config(state=tk.NORMAL)
            self.pl_single_btn.config(state=tk.NORMAL)
        elif self._pl_mode == "pause":
            self.pl_run_btn.config(state=tk.NORMAL)
            self.pl_pause_btn.config(state=tk.NORMAL, text="继续流程")
            self.pl_stop_btn.config(state=tk.NORMAL)
            self.pl_single_btn.config(state=tk.NORMAL)
        else:  
            self.pl_run_btn.config(state=tk.NORMAL)
            self.pl_pause_btn.config(state=tk.DISABLED, text="暂停流程")
            self.pl_stop_btn.config(state=tk.DISABLED)
            self.pl_single_btn.config(state=tk.NORMAL)

    # ==================================================================
    # Misc helpers
    # ==================================================================
    def _load_default_workstations(self):
        """加载默认工位配置文件并构建 PipelineRunner 映射。"""
        try:
            self.workstations = load_workstations()
        except Exception as exc:
            print("load_workstations error", exc)
            self.workstations = []

        # 根据工位数量自动调整布局
        ws_count = len(self.workstations)
        if ws_count > 0:
            # 选择合适的布局选项
            suitable_layout = 2  # 默认2个
            for layout_option in LAYOUT_OPTIONS:
                if layout_option >= ws_count:
                    suitable_layout = layout_option
                    break

            # 更新布局变量
            self.current_layout.set(suitable_layout)
            print(f"[DEBUG] 检测到 {ws_count} 个工位，自动设置布局为 {suitable_layout}")

            # 记录操作日志
            try:
                log_operation("系统", "自动布局调整", f"{suitable_layout}工位", {
                    "workstation_count": ws_count,
                    "layout": suitable_layout
                })
            except Exception as e:
                print(f"[DEBUG] 记录布局调整日志失败: {e}")

        self._build_ws_runners()

    # -----------------------------------------------------------------
    # Recipe import helpers
    # -----------------------------------------------------------------
    def _build_ws_runners(self):
        """根据 self.workstations 构建 _ws_runners。"""
        self._ws_runners.clear()
        for ws in self.workstations:
            try:
                runner: Optional[PipelineRunner]
                if isinstance(ws.pipeline, list) and ws.pipeline:
                    # 使用内存中的步骤数组，但需要设置工位路径供插件使用
                    workstation_path = None
                    if hasattr(self, '_current_recipe_path') and self._current_recipe_path:
                        workstation_path = str(self._current_recipe_path / ws.id)
                    runner = PipelineRunner(steps=ws.pipeline, workstation_path=workstation_path)
                elif isinstance(ws.pipeline, str) and ws.pipeline:
                    # 使用路径管理模块获取pipeline路径
                    pipeline_path = Path(get_data_path()) / ws.pipeline
                    print(f"DEBUG: 尝试加载工位 {ws.id} 的pipeline: {pipeline_path}")
                    if not pipeline_path.exists():
                        print(f"DEBUG: Pipeline文件不存在: {pipeline_path}")
                        continue
                    runner = PipelineRunner(str(pipeline_path))
                else:
                    continue
                self._ws_runners[ws.camera_id] = runner
                print(f"DEBUG: 工位 {ws.id} PipelineRunner 初始化成功")
            except Exception as exc:
                print(f"DEBUG: 工位 {ws.id} PipelineRunner 初始化失败: {exc}")
                import traceback
                traceback.print_exc()

    def import_recipe(self, file_path: str):
        """加载指定配方 YAML 文件，并更新工位与流程。"""
        try:
            data = yaml.safe_load(Path(file_path).read_text(encoding="utf-8")) or {}
        except Exception as exc:
            Messagebox.show_error("加载配方失败", str(exc))
            return
        if isinstance(data, list):
            data = {"workstations": data}
        ws_defs = data.get("workstations", [])
        new_ws: List[Workstation] = []

        # 保存配方路径，供后续使用
        self._current_recipe_path = Path(file_path).parent

        for idx, wd in enumerate(ws_defs):
            try:
                ws = Workstation(
                    id=wd.get("id", f"ws{idx+1}"),
                    name=wd.get("name", f"工位{idx+1}"),
                    camera_id=wd.get("camera_id") or wd.get("camera", f"cam{idx+1}"),
                    pipeline=wd.get("pipeline", ""),
                )
                if isinstance(wd.get("pipeline"), list):
                    ws.pipeline = wd["pipeline"]
                new_ws.append(ws)
            except Exception as exc:
                print("parse workstation error", exc)
        if not new_ws:
            Messagebox.show_error("无有效工位", "该配方未包含有效的工位配置！")
            return
        self.workstations = new_ws
        self._build_ws_runners()

        # 根据新工位数量自动调整布局
        ws_count = len(new_ws)
        if ws_count > 0:
            # 选择合适的布局选项
            suitable_layout = 2  # 默认2个
            for layout_option in LAYOUT_OPTIONS:
                if layout_option >= ws_count:
                    suitable_layout = layout_option
                    break

            # 更新布局变量
            self.current_layout.set(suitable_layout)
            print(f"[DEBUG] 配方导入后检测到 {ws_count} 个工位，自动设置布局为 {suitable_layout}")

            # 记录操作日志
            try:
                log_operation("用户", "配方导入布局调整", f"{suitable_layout}工位", {
                    "workstation_count": ws_count,
                    "layout": suitable_layout
                })
            except Exception as e:
                print(f"[DEBUG] 记录配方导入布局调整日志失败: {e}")

        if hasattr(self, "preview_tab"):
            self._set_layout(self.current_layout.get())
            self._reset_counter()

    def _import_recipe_dialog(self):
        from tkinter import filedialog
        fp = filedialog.askopenfilename(
            title="选择配方 YAML 文件",
            filetypes=[("YAML files", "*.yaml *.yml"), ("All", "*.*")],
            initialdir=get_recipe_path(),
        )
        if fp:
            self.import_recipe(fp)

    # -----------------------------------------------------------------
    # Existing helpers
    # -----------------------------------------------------------------
    def _update_workstation_statistics(self, cam_id: str, ctx: dict):
        """更新工位统计信息

        Args:
            cam_id: 相机ID
            ctx: 处理上下文，包含检测结果
        """
        try:
            # 确保工位统计存在
            if cam_id not in self._ws_stats:
                self._ws_stats[cam_id] = {"ok": 0, "ng": 0, "last_detection_time": 0.0}

            # 分析上下文中的检测结果
            ng_count = 0
            ok_count = 0
            detection_time = 0.0  # 检测耗时

            # 检查各种可能的NG源
            has_detection = False  # 标记是否有检测活动

            # 提取检测耗时信息
            if 'detection_time' in ctx:
                detection_time = ctx['detection_time']
                self._ws_stats[cam_id]["last_detection_time"] = detection_time

            # 1. YOLO检测结果
            if 'yolo_detect' in ctx:
                yolo_result = ctx['yolo_detect']
                has_detection = True  # YOLO检测算作检测活动

                yolo_ng = yolo_result.get('ng_count', 0)
                ng_count += yolo_ng

                # 检查ROI形状匹配
                if not yolo_result.get('roi_shape_ok', True):
                    ng_count += 1
                else:
                    # ROI匹配成功且没有检测到缺陷，算作OK
                    if yolo_ng == 0:
                        ok_count += 1

            # 2. 逻辑判断结果
            if 'logic_judgment' in ctx:
                logic_result = ctx['logic_judgment']
                logic_status = logic_result.get('result')
                has_detection = True
                if logic_status == 'NG':
                    ng_count += 1
                elif logic_status == 'OK':
                    ok_count += 1

            # 3. 几何测量结果
            if 'integrated_geometry_tool' in ctx:
                geo_result = ctx['integrated_geometry_tool']
                geo_ok = geo_result.get('overall_ok', True)
                has_detection = True
                if not geo_ok:
                    ng_count += 1
                else:
                    ok_count += 1

            # 4. 其他检测插件结果
            for key, value in ctx.items():
                if isinstance(value, dict):
                    # 检查是否有NG/OK标识
                    if 'ng_count' in value:
                        plugin_ng = value.get('ng_count', 0)
                        ng_count += plugin_ng
                        has_detection = True
                    if 'ok_count' in value:
                        plugin_ok = value.get('ok_count', 0)
                        ok_count += plugin_ok
                        has_detection = True

                    # 检查通用的ok字段
                    if 'ok' in value and isinstance(value['ok'], bool):
                        has_detection = True
                        if value['ok']:
                            ok_count += 1
                        else:
                            ng_count += 1

            # 更新工位统计
            if ng_count > 0:
                self._ws_stats[cam_id]["ng"] += 1
                print(f"[STATS] 工位 {cam_id} NG +1, 总计: {self._ws_stats[cam_id]['ng']}")

                # 记录检测日志
                try:
                    log_detection(cam_id, "NG", {
                        "ng_count": ng_count,
                        "total_ng": self._ws_stats[cam_id]['ng'],
                        "context": ctx
                    })
                except Exception as e:
                    print(f"[DEBUG] 记录NG检测日志失败: {e}")

            elif ok_count > 0:
                self._ws_stats[cam_id]["ok"] += 1
                print(f"[STATS] 工位 {cam_id} OK +1, 总计: {self._ws_stats[cam_id]['ok']}")

                # 记录检测日志
                try:
                    log_detection(cam_id, "OK", {
                        "ok_count": ok_count,
                        "total_ok": self._ws_stats[cam_id]['ok'],
                        "context": ctx
                    })
                except Exception as e:
                    print(f"[DEBUG] 记录OK检测日志失败: {e}")

            elif has_detection:
                # 有检测活动但没有明确结果，记录为无效检测
                print(f"[STATS] 工位 {cam_id} 检测无明确结果")
                try:
                    log_detection(cam_id, "UNCLEAR", {"context": ctx})
                except Exception as e:
                    print(f"[DEBUG] 记录无效检测日志失败: {e}")
            else:
                print(f"[STATS] 工位 {cam_id} 本次无检测活动")

            # 更新工位显示标签
            if cam_id in self._ws_labels:
                total = self._ws_stats[cam_id]["ok"] + self._ws_stats[cam_id]["ng"]
                detection_time_ms = self._ws_stats[cam_id].get("last_detection_time", 0.0) * 1000
                label_text = f"OK:{self._ws_stats[cam_id]['ok']} NG:{self._ws_stats[cam_id]['ng']} 耗时:{detection_time_ms:.1f}ms"
                try:
                    self._ws_labels[cam_id].config(text=label_text)
                except Exception:
                    pass

        except Exception as e:
            print(f"[STATS] 更新工位统计失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_global_statistics(self):
        """更新全局统计信息

        统计逻辑：
        - NG总数 = 所有工位的NG相加
        - OK总数 = 所有工位的OK相加（修改：原来只取最后工位）
        - 总数 = OK总数 + NG总数
        """
        try:
            # 计算所有工位的NG总数
            total_ng = sum(stats["ng"] for stats in self._ws_stats.values())

            # 计算所有工位的OK总数（修改统计逻辑）
            total_ok = sum(stats["ok"] for stats in self._ws_stats.values())

            # 更新全局统计显示
            self.ng_var.set(total_ng)
            self.ok_var.set(total_ok)
            self.total_var.set(total_ok + total_ng)

            print(f"[STATS] 全局统计更新 - OK: {total_ok}, NG: {total_ng}, 总计: {total_ok + total_ng}")

        except Exception as e:
            print(f"[STATS] 更新全局统计失败: {e}")
            import traceback
            traceback.print_exc()

    def _reset_counter(self):
        self.ok_var.set(0)
        self.ng_var.set(0)
        self.total_var.set(0)
        for stats in self._ws_stats.values():
            stats["ok"] = stats["ng"] = 0
        for lbl in self._ws_labels.values():
            try:
                lbl.config(text="OK:0 NG:0")
            except Exception:
                pass
        self.status_var.set("计数已清零")
        print("[STATS] 所有统计已清零")

    def _open_camera_settings(self):
        """打开海康相机管理UI (内嵌窗口方式)。"""
        try:
            # 直接导入相机管理类，避免启动独立进程
            from CommTools.camera_manager_tk import CameraManagerTk

            was_running = self._capture_running
            if was_running:
                if not Messagebox.okcancel("暂停采集", "打开相机管理工具可能与当前采集冲突，是否暂停采集？"):
                    return
                self._stop_capture()

            # 创建相机管理窗口
            camera_window = CameraManagerTk()
            camera_window.transient(self)  # 设置为主窗口的子窗口
            camera_window.grab_set()       # 模态窗口

            # 窗口关闭时的回调
            def on_camera_window_close():
                camera_window.destroy()
                if was_running:
                    self.after_idle(self._start_capture)

            camera_window.protocol("WM_DELETE_WINDOW", on_camera_window_close)

        except ImportError as exc:
            Messagebox.show_error("无法导入相机管理工具", f"导入失败: {exc}")
            return
        except Exception as exc:
            Messagebox.show_error("无法启动相机管理工具", str(exc))
            return

    # ---------------- Camera Calibrate -----------------------------
    def _open_camera_calibrate(self):
        """打开相机标定工具"""
        try:
            from plugins.ui.camera_calibrate.calibrate_ui import CameraCalibFrame
        except Exception as exc:
            Messagebox.show_error("无法打开相机标定", str(exc))
            return
        win = tk.Toplevel(self)
        win.title("相机标定")
        win.geometry("1000x700")
        CameraCalibFrame(win).pack(fill=tk.BOTH, expand=True)
        win.transient(self)
        win.grab_set()

    def _open_high_precision_calibration(self):
        """打开高精度标定工具"""
        try:
            from plugins.ui.high_precision_calibrate import HighPrecisionCalibFrame
        except Exception as exc:
            # 如果模块不存在，创建一个简单的对话框
            self._show_high_precision_calibration_dialog()
            return

        win = tk.Toplevel(self)
        win.title("高精度标定 (±0.01mm)")
        win.geometry("1200x800")
        HighPrecisionCalibFrame(win).pack(fill=tk.BOTH, expand=True)
        win.transient(self)
        win.grab_set()

    def _show_high_precision_calibration_dialog(self):
        """显示高精度标定对话框"""
        dialog = tk.Toplevel(self)
        dialog.title("高精度标定系统")
        dialog.geometry("600x500")
        dialog.transient(self)
        dialog.grab_set()

        # 创建高精度标定界面
        self._build_high_precision_ui(dialog)

    def _open_config_center(self):
        """打开原参数配置中心（CameraConfigUI）。"""
        try:
            from system_config_ui.camera_config_ui import CameraConfigUI  
        except Exception as exc:
            Messagebox.show_error("无法打开配置中心", str(exc))
            return
        if hasattr(self, "_cfg_center_win") and self._cfg_center_win and self._cfg_center_win.winfo_exists():
            self._cfg_center_win.lift()
            return
        self._cfg_center_win = CameraConfigUI(self)
        self._cfg_center_win.protocol("WM_DELETE_WINDOW", self._cfg_center_win.destroy)

    def _open_pipeline_editor(self):
        try:
            from pipeline.pipeline_editor import PipelineEditor  
        except Exception as exc:
            Messagebox.show_error("无法打开流程配置", str(exc))
            return
        PipelineEditor(self)

    def _open_workstation_cfg(self):
        try:
            from system_config_ui.workstation_config_ui import WorkstationConfigUI
        except Exception as exc:
            Messagebox.show_error("无法打开工位配置", str(exc))
            return
        WorkstationConfigUI(self)

    def _open_recipe_editor(self):
        """显示配方配置分页（替代原来的弹窗模式）"""
        self._show_recipe_tab()

    def _show_recipe_tab(self):
        """显示配方配置分页"""
        if not hasattr(self, "recipe_tab") or self.recipe_tab is None or not self.recipe_tab.winfo_exists():
            try:
                from recipe_editor.recipe_editor import RecipeEditorTab
            except Exception as exc:
                Messagebox.show_error("无法打开配方配置", str(exc))
                return

            # 创建配方配置分页
            self.recipe_tab = ttk.Frame(self.notebook)
            self.notebook.add(self.recipe_tab, text="配方配置")

            # 添加关闭按钮栏
            close_bar = ttk.Frame(self.recipe_tab)
            close_bar.pack(fill=tk.X, anchor="ne")
            ttk.Button(close_bar, text="✕", width=2, command=lambda: self._close_recipe_tab()).pack(side=tk.RIGHT, padx=5, pady=2)

            # 创建配方编辑器内容容器
            recipe_container = ttk.Frame(self.recipe_tab)
            recipe_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 创建内嵌的配方编辑器实例
            try:
                self.recipe_editor_instance = RecipeEditorTab(recipe_container)
                self.recipe_editor_instance.pack(fill=tk.BOTH, expand=True)
            except Exception as exc:
                Messagebox.show_error("配方编辑器初始化失败", str(exc))
                self._close_recipe_tab()
                return

        self.notebook.select(self.recipe_tab)

    def _close_recipe_tab(self):
        """关闭配方配置分页"""
        if hasattr(self, "recipe_tab") and self.recipe_tab is not None:
            try:
                # 清理配方编辑器实例
                if hasattr(self, "recipe_editor_instance") and self.recipe_editor_instance:
                    try:
                        self.recipe_editor_instance.destroy()
                    except Exception:
                        pass
                    self.recipe_editor_instance = None

                # 移除分页
                self.notebook.forget(self.recipe_tab)
            except Exception:
                pass
            self.recipe_tab = None

    def _show_log_tab(self):
        if not hasattr(self, "log_tab") or self.log_tab is None or not self.log_tab.winfo_exists():
            self.log_tab = ttk.Frame(self.notebook)
            self.notebook.add(self.log_tab, text="系统日志")

            # 关闭按钮栏
            bar = ttk.Frame(self.log_tab)
            bar.pack(fill=tk.X, anchor="ne")
            ttk.Button(bar, text="✕", width=2, command=lambda: self._close_log_tab()).pack(side=tk.RIGHT, padx=5, pady=2)

            # 创建系统日志UI
            try:
                from system_config_ui.system_log_ui import create_system_log_ui
                self.log_ui = create_system_log_ui(self.log_tab)
                log_system("系统日志界面已打开", "INFO")
            except Exception as e:
                # 如果无法加载新的日志UI，使用简单的文本显示
                error_label = ttk.Label(self.log_tab, text=f"无法加载日志界面: {e}")
                error_label.pack(pady=20)
                simple_text = tk.Text(self.log_tab, state="disabled")
                simple_text.pack(fill=tk.BOTH, expand=True)
                print(f"[DEBUG] 加载日志UI失败: {e}")

        self.notebook.select(self.log_tab)

    def _close_log_tab(self):
        if hasattr(self, "log_tab") and self.log_tab is not None:
            try:
                self.notebook.forget(self.log_tab)
            except Exception:
                pass
            self.log_tab = None

    # ---------------- UI Settings ------------------------------------
    def _open_ui_settings(self):
        """打开界面设置对话框"""
        try:
            from ui_config import UIConfigDialog
            dialog = UIConfigDialog(self, self.ui_config, self._apply_ui_settings)
        except Exception as e:
            from ttkbootstrap.dialogs import Messagebox
            Messagebox.show_error("无法打开界面设置", str(e))

    def _apply_ui_settings(self):
        """应用界面设置"""
        try:
            # 防止重复更新
            if hasattr(self, '_updating_ui') and self._updating_ui:
                return

            self._updating_ui = True

            # 更新主题
            new_theme = self.ui_config.get("theme")
            if new_theme != self.style.theme.name:
                self.style.theme_use(new_theme)

            # 更新字体
            self._update_fonts()

            # 应用圆角样式
            self._apply_rounded_corners()

            # 应用自定义样式
            self._apply_custom_styles()

            # 同步更新配方配置页面
            if hasattr(self, 'recipe_editor_instance') and self.recipe_editor_instance:
                try:
                    self.recipe_editor_instance.update_theme(new_theme)
                    self.recipe_editor_instance.update_fonts()
                except Exception as e:
                    print(f"[DEBUG] 更新配方配置页面失败: {e}")

        except Exception as e:
            print(f"[ERROR] 应用界面设置失败: {e}")
        finally:
            self._updating_ui = False

    def _update_fonts(self):
        """更新字体设置"""
        try:
            font_family = self.ui_config.get("font_family", "微软雅黑")
            default_size = self.ui_config.get("default_font_size", 9)
            title_size = self.ui_config.get("title_font_size", 14)
            button_size = self.ui_config.get("button_font_size", 12)

            # 检查是否需要更新（避免重复更新）
            current_font_key = f"{font_family}_{default_size}_{title_size}_{button_size}"
            if hasattr(self, '_last_font_key') and self._last_font_key == current_font_key:
                return

            self._last_font_key = current_font_key

            # 更新侧边栏品牌标签字体
            if hasattr(self, 'brand_label'):
                self.brand_label.config(font=(font_family, title_size, "bold"))

            # 更新系统标题字体
            if hasattr(self, 'system_title'):
                self.system_title.config(font=(font_family, title_size, "bold"))

            # 更新全局样式字体
            self.style.configure("TLabel", font=(font_family, default_size))
            self.style.configure("TButton", font=(font_family, button_size))
            self.style.configure("TEntry", font=(font_family, default_size))
            self.style.configure("TCombobox", font=(font_family, default_size))
            self.style.configure("TCheckbutton", font=(font_family, default_size))
            self.style.configure("TRadiobutton", font=(font_family, default_size))
            self.style.configure("TLabelframe.Label", font=(font_family, default_size, "bold"))
            self.style.configure("TNotebook.Tab", font=(font_family, default_size))
            self.style.configure("Treeview", font=(font_family, default_size))
            self.style.configure("Treeview.Heading", font=(font_family, default_size, "bold"))

            # 特殊样式
            self.style.configure("Title.TLabel", font=(font_family, title_size, "bold"))
            self.style.configure("Brand.TLabel", font=(font_family, title_size, "bold"))

            print(f"[DEBUG] 字体已更新: {font_family}, 默认大小: {default_size}, 标题大小: {title_size}")

        except Exception as e:
            print(f"[DEBUG] 更新字体失败: {e}")

    def _apply_rounded_corners(self):
        """应用圆角样式"""
        try:
            # 检查是否需要更新（避免重复更新）
            use_rounded = self.ui_config.get("use_rounded_corners", True)
            if hasattr(self, '_last_rounded_state') and self._last_rounded_state == use_rounded:
                return

            self._last_rounded_state = use_rounded
            colors = self.style.colors

            if use_rounded:
                # 真正的圆角样式配置
                corner_radius = self.ui_config.get("corner_radius", 3)

                # 创建圆角样式
                self._create_rounded_styles(corner_radius)

                print(f"[DEBUG] 真正圆角样式已应用，强度: {corner_radius}")


            else:
                # 恢复默认样式
                self.style.configure("TButton",
                                   borderwidth=1,
                                   relief="raised",
                                   padding=(6, 4))
                self.style.configure("TEntry",
                                   borderwidth=1,
                                   relief="sunken",
                                   padding=(4, 2))
                self.style.configure("TCombobox",
                                   borderwidth=1,
                                   relief="sunken",
                                   padding=(4, 2))
                self.style.configure("TLabelframe",
                                   borderwidth=1,
                                   relief="groove")
                self.style.configure("TCheckbutton",
                                   focuscolor="",
                                   borderwidth=0,
                                   relief="flat")
                self.style.configure("TRadiobutton",
                                   focuscolor="",
                                   borderwidth=0,
                                   relief="flat")
                print("[DEBUG] 使用默认控件样式")
        except Exception as e:
            print(f"[DEBUG] 应用圆角样式失败: {e}")

    def _create_rounded_styles(self, corner_radius):
        """创建真正的圆角样式 - 使用自定义Canvas按钮"""
        try:
            # 直接应用增强的视觉效果
            self._apply_enhanced_visual_styles(corner_radius)

        except Exception as e:
            print(f"[DEBUG] 创建圆角样式失败: {e}")
            # 如果圆角创建失败，使用简单的视觉增强
            self._apply_simple_visual_enhancement(corner_radius)



    def _apply_rounded_widget_styles(self, radius, colors):
        """应用圆角样式到控件"""
        try:
            # 使用自定义样式名称
            style_name = f"Rounded{radius}"

            # 配置按钮样式
            self.style.element_create(f"{style_name}.Button.button", "image",
                                    self.rounded_button_normal,
                                    ("active", self.rounded_button_hover),
                                    border=8, sticky="ew")

            self.style.layout(f"{style_name}.TButton", [
                (f"{style_name}.Button.button", {"children": [
                    ("Button.focus", {"children": [
                        ("Button.label", {"sticky": "nswe"})
                    ]})
                ]})
            ])

            # 注册新的按钮样式
            self.style.configure(f"{style_name}.TButton")

            # 其他控件使用增强边框样式
            border_width = max(1, radius)
            self.style.configure("TEntry",
                               borderwidth=border_width,
                               relief="solid",
                               insertcolor=colors.primary)

            self.style.configure("TCombobox",
                               borderwidth=border_width,
                               relief="solid")

            self.style.configure("TLabelframe",
                               borderwidth=border_width,
                               relief="solid")

        except Exception as e:
            print(f"[DEBUG] 应用圆角控件样式失败: {e}")
            # 使用备选方案
            self._apply_enhanced_border_styles(radius)

    def _apply_enhanced_border_styles(self, radius):
        """应用增强边框样式作为圆角的备选方案"""
        try:
            colors = self.style.colors
            border_width = max(1, radius)
            padding_h = 6 + radius * 2
            padding_v = 4 + radius

            # 按钮样式
            self.style.configure("TButton",
                               borderwidth=border_width,
                               focuscolor="none",
                               relief="solid",
                               padding=(padding_h, padding_v))

            # 输入框样式
            self.style.configure("TEntry",
                               borderwidth=border_width,
                               relief="solid",
                               insertcolor=colors.primary,
                               padding=(padding_h-2, padding_v-2))

            # 下拉框样式
            self.style.configure("TCombobox",
                               borderwidth=border_width,
                               relief="solid",
                               padding=(padding_h-2, padding_v-2))

            # 框架样式
            self.style.configure("TLabelframe",
                               borderwidth=border_width,
                               relief="solid")

            print(f"[DEBUG] 增强边框样式已应用，强度: {radius}")

        except Exception as e:
            print(f"[DEBUG] 应用增强边框样式失败: {e}")

    def _apply_css_rounded_styles(self, radius):
        """使用CSS样式方法应用圆角效果"""
        try:
            colors = self.style.colors

            # 扩大圆角效果 - 1-10 映射到 5-50px
            corner_radius = radius * 5

            # 创建自定义样式名称
            rounded_style = f"Rounded{radius}"

            # 尝试使用ttkbootstrap的高级样式功能
            if hasattr(self.style, 'theme_use'):
                current_theme = self.style.theme.name

                # 按钮圆角样式
                self.style.configure(f"{rounded_style}.TButton",
                                   focuscolor="none",
                                   relief="flat",
                                   borderwidth=2,
                                   padding=(6, 4))

                # 输入框圆角样式
                self.style.configure(f"{rounded_style}.TEntry",
                                   relief="solid",
                                   borderwidth=2,
                                   insertcolor=colors.primary,
                                   padding=(4, 2))

                # 下拉框圆角样式
                self.style.configure(f"{rounded_style}.TCombobox",
                                   relief="solid",
                                   borderwidth=2,
                                   padding=(4, 2))

                # 框架圆角样式
                self.style.configure(f"{rounded_style}.TLabelframe",
                                   relief="solid",
                                   borderwidth=2)

                # 应用圆角样式到默认控件
                self.style.configure("TButton",
                                   focuscolor="none",
                                   relief="solid",
                                   borderwidth=max(2, radius),
                                   padding=(6, 4))

                self.style.configure("TEntry",
                                   relief="solid",
                                   borderwidth=max(2, radius),
                                   insertcolor=colors.primary,
                                   padding=(4, 2))

                self.style.configure("TCombobox",
                                   relief="solid",
                                   borderwidth=max(2, radius),
                                   padding=(4, 2))

                self.style.configure("TLabelframe",
                                   relief="solid",
                                   borderwidth=max(2, radius))

                print(f"[DEBUG] CSS圆角样式已应用，强度: {radius}, 圆角: {corner_radius}px")

        except Exception as e:
            print(f"[DEBUG] 应用CSS圆角样式失败: {e}")
            # 最终备选方案
            self._apply_simple_rounded_styles(radius)

    def _apply_simple_rounded_styles(self, radius):
        """应用简单的圆角模拟效果"""
        try:
            colors = self.style.colors
            border_width = max(1, radius)

            # 使用solid relief和适当的边框宽度来模拟圆角
            self.style.configure("TButton",
                               focuscolor="none",
                               relief="solid",
                               borderwidth=border_width,
                               padding=(6, 4))

            self.style.configure("TEntry",
                               relief="solid",
                               borderwidth=border_width,
                               insertcolor=colors.primary,
                               padding=(4, 2))

            self.style.configure("TCombobox",
                               relief="solid",
                               borderwidth=border_width,
                               padding=(4, 2))

            self.style.configure("TLabelframe",
                               relief="solid",
                               borderwidth=border_width)

            print(f"[DEBUG] 简单圆角模拟已应用，强度: {radius}, 边框: {border_width}px")

        except Exception as e:
            print(f"[DEBUG] 应用简单圆角样式失败: {e}")

    def _create_rounded_images(self, radius):
        """创建圆角背景图片"""
        try:
            from PIL import Image, ImageDraw
            import tkinter as tk
            import io

            colors = self.style.colors

            # 按钮尺寸
            btn_width, btn_height = 100, 30

            # 创建按钮背景图片
            def create_rounded_rect_image(width, height, radius, fill_color, border_color=None):
                # 创建透明背景
                img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
                draw = ImageDraw.Draw(img)

                # 绘制圆角矩形
                # 左上角
                draw.pieslice([0, 0, radius*2, radius*2], 180, 270, fill=fill_color)
                # 右上角
                draw.pieslice([width-radius*2, 0, width, radius*2], 270, 360, fill=fill_color)
                # 左下角
                draw.pieslice([0, height-radius*2, radius*2, height], 90, 180, fill=fill_color)
                # 右下角
                draw.pieslice([width-radius*2, height-radius*2, width, height], 0, 90, fill=fill_color)

                # 填充中间区域
                draw.rectangle([radius, 0, width-radius, height], fill=fill_color)
                draw.rectangle([0, radius, width, height-radius], fill=fill_color)

                # 添加边框（可选）
                if border_color:
                    # 绘制圆角边框
                    draw.arc([0, 0, radius*2, radius*2], 180, 270, fill=border_color, width=2)
                    draw.arc([width-radius*2, 0, width, radius*2], 270, 360, fill=border_color, width=2)
                    draw.arc([0, height-radius*2, radius*2, height], 90, 180, fill=border_color, width=2)
                    draw.arc([width-radius*2, height-radius*2, width, height], 0, 90, fill=border_color, width=2)

                    # 绘制直线边框
                    draw.line([radius, 0, width-radius, 0], fill=border_color, width=2)  # 上边
                    draw.line([radius, height-1, width-radius, height-1], fill=border_color, width=2)  # 下边
                    draw.line([0, radius, 0, height-radius], fill=border_color, width=2)  # 左边
                    draw.line([width-1, radius, width-1, height-radius], fill=border_color, width=2)  # 右边

                return img

            # 解析颜色
            def hex_to_rgba(hex_color, alpha=255):
                hex_color = hex_color.lstrip('#')
                return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4)) + (alpha,)

            # 创建不同状态的按钮图片
            primary_color = hex_to_rgba(colors.primary)
            secondary_color = hex_to_rgba(colors.secondary)

            # 正常状态按钮
            self.btn_normal_img = create_rounded_rect_image(
                btn_width, btn_height, radius, primary_color, secondary_color)

            # 悬停状态按钮
            self.btn_hover_img = create_rounded_rect_image(
                btn_width, btn_height, radius, secondary_color, primary_color)

            # 按下状态按钮
            self.btn_pressed_img = create_rounded_rect_image(
                btn_width, btn_height, radius, hex_to_rgba(colors.dark), primary_color)

            # 转换为PhotoImage
            def pil_to_photoimage(pil_img):
                bio = io.BytesIO()
                pil_img.save(bio, format='PNG')
                bio.seek(0)
                return tk.PhotoImage(data=bio.getvalue())

            self.btn_normal_photo = pil_to_photoimage(self.btn_normal_img)
            self.btn_hover_photo = pil_to_photoimage(self.btn_hover_img)
            self.btn_pressed_photo = pil_to_photoimage(self.btn_pressed_img)

            print(f"[DEBUG] 圆角图片已创建，半径: {radius}px")

        except Exception as e:
            print(f"[DEBUG] 创建圆角图片失败: {e}")
            raise e

    def _apply_rounded_image_styles(self, radius):
        """应用圆角图片样式到控件"""
        try:
            # 创建自定义按钮样式
            style_name = "RoundedButton"

            # 使用图片作为按钮背景
            self.style.element_create(f"{style_name}.button", "image",
                                    self.btn_normal_photo,
                                    ("active", self.btn_hover_photo),
                                    ("pressed", self.btn_pressed_photo),
                                    border=8, sticky="ew")

            # 设置按钮布局
            self.style.layout(f"{style_name}.TButton", [
                (f"{style_name}.button", {"children": [
                    ("Button.focus", {"children": [
                        ("Button.label", {"sticky": "nswe"})
                    ]})
                ]})
            ])

            # 配置按钮样式
            self.style.configure(f"{style_name}.TButton",
                               focuscolor="none")

            # 应用到侧边栏按钮
            if hasattr(self, '_nav_buttons'):
                for btn in self._nav_buttons:
                    btn.configure(style=f"{style_name}.TButton")

            # 其他控件使用简单的圆角模拟
            self._apply_other_rounded_styles(radius)

            print(f"[DEBUG] 圆角图片样式已应用到按钮")

        except Exception as e:
            print(f"[DEBUG] 应用圆角图片样式失败: {e}")
            # 不抛出异常，使用备选方案
            self._apply_simple_visual_enhancement(radius)

    def _apply_other_rounded_styles(self, radius):
        """为其他控件应用圆角模拟样式"""
        try:
            colors = self.style.colors

            # 输入框 - 使用solid边框模拟圆角
            self.style.configure("TEntry",
                               relief="solid",
                               borderwidth=2,
                               insertcolor=colors.primary,
                               padding=(4, 2))

            # 下拉框
            self.style.configure("TCombobox",
                               relief="solid",
                               borderwidth=2,
                               padding=(4, 2))

            # 框架
            self.style.configure("TLabelframe",
                               relief="solid",
                               borderwidth=2)

            print(f"[DEBUG] 其他控件圆角样式已应用")

        except Exception as e:
            print(f"[DEBUG] 应用其他控件圆角样式失败: {e}")

    def _apply_simple_visual_enhancement(self, radius):
        """应用简单的视觉增强作为备选方案"""
        try:
            colors = self.style.colors

            # 简单的边框增强
            self.style.configure("TButton",
                               focuscolor="none",
                               relief="solid",
                               borderwidth=2,
                               padding=(6, 4))

            self.style.configure("TEntry",
                               relief="solid",
                               borderwidth=2,
                               insertcolor=colors.primary,
                               padding=(4, 2))

            self.style.configure("TCombobox",
                               relief="solid",
                               borderwidth=2,
                               padding=(4, 2))

            self.style.configure("TLabelframe",
                               relief="solid",
                               borderwidth=2)

            print(f"[DEBUG] 简单视觉增强已应用，强度: {radius}")

        except Exception as e:
            print(f"[DEBUG] 应用简单视觉增强失败: {e}")

    def _create_rounded_logo(self, image, radius=8):
        """创建圆角logo"""
        try:
            from PIL import Image, ImageDraw

            # 创建圆角遮罩
            mask = Image.new('L', image.size, 0)
            draw = ImageDraw.Draw(mask)

            # 绘制圆角矩形遮罩
            width, height = image.size
            draw.rounded_rectangle([(0, 0), (width-1, height-1)], radius=radius, fill=255)

            # 应用遮罩
            output = Image.new('RGBA', image.size, (0, 0, 0, 0))
            output.paste(image, (0, 0))
            output.putalpha(mask)

            return output

        except Exception as e:
            print(f"[DEBUG] 创建圆角logo失败: {e}")
            return image

    def _apply_enhanced_visual_styles(self, radius):
        """应用默认样式 - 不使用圆角效果"""
        try:
            # 直接使用默认样式，不管圆角强度是多少
            self._apply_default_styles()
            print(f"[DEBUG] 使用默认TTK样式，圆角强度: {radius}")

        except Exception as e:
            print(f"[DEBUG] 应用默认样式失败: {e}")
            # 最终备选方案
            self._apply_default_styles()

    def _create_custom_rounded_buttons(self, radius):
        """创建自定义的圆角Canvas按钮来替换侧边栏按钮"""
        try:
            import tkinter as tk

            # 如果已经创建过自定义按钮，先清理
            if hasattr(self, '_custom_buttons'):
                for btn_info in self._custom_buttons:
                    btn_info['canvas'].destroy()

            self._custom_buttons = []

            # 获取主题颜色
            colors = self.style.colors

            # 按钮配置
            button_configs = [
                ("实时监控", self._show_preview),
                ("配置中心", self._open_config_center),
                ("流程配置", self._open_pipeline_editor),
                ("工位配置", self._open_workstation_cfg),
                ("相机标定", self._open_camera_calibrate),
                ("系统日志", self._show_log_tab),
                ("导入配方", self._import_recipe_dialog),
                ("配方配置", self._open_recipe_editor)
            ]

            # 隐藏原来的TTK按钮
            for btn in self._nav_buttons:
                btn.pack_forget()

            # 创建自定义圆角按钮
            for i, (text, command) in enumerate(button_configs):
                canvas_btn = self._create_rounded_canvas_button(
                    self.sidebar, text, command, radius, colors)
                canvas_btn.pack(pady=5, padx=10)  # 移除fill='x'，保持固定宽度

                self._custom_buttons.append({
                    'canvas': canvas_btn,
                    'text': text,
                    'command': command
                })

        except Exception as e:
            print(f"[DEBUG] 创建自定义圆角按钮失败: {e}")
            # 恢复原来的按钮
            for btn in self._nav_buttons:
                btn.pack(pady=5)

    def _create_rounded_canvas_button(self, parent, text, command, radius, colors):
        """创建单个圆角Canvas按钮"""
        import tkinter as tk

        # 创建Canvas - 设置固定宽度和高度
        canvas = tk.Canvas(parent, width=120, height=30, highlightthickness=0)

        # 绘制圆角矩形
        def draw_rounded_rect(canvas, x1, y1, x2, y2, radius, fill_color, outline_color=None):
            # 绘制圆角矩形的各个部分
            # 四个圆角
            canvas.create_arc(x1, y1, x1+radius*2, y1+radius*2,
                            start=90, extent=90, fill=fill_color, outline=outline_color, width=0)
            canvas.create_arc(x2-radius*2, y1, x2, y1+radius*2,
                            start=0, extent=90, fill=fill_color, outline=outline_color, width=0)
            canvas.create_arc(x1, y2-radius*2, x1+radius*2, y2,
                            start=180, extent=90, fill=fill_color, outline=outline_color, width=0)
            canvas.create_arc(x2-radius*2, y2-radius*2, x2, y2,
                            start=270, extent=90, fill=fill_color, outline=outline_color, width=0)

            # 填充中间区域
            canvas.create_rectangle(x1+radius, y1, x2-radius, y2,
                                  fill=fill_color, outline=outline_color, width=0)
            canvas.create_rectangle(x1, y1+radius, x2, y2-radius,
                                  fill=fill_color, outline=outline_color, width=0)

        def update_canvas(event=None):
            canvas.delete("all")
            width = 120  # 固定宽度
            height = 30  # 固定高度

            # 计算合理的圆角半径
            corner_radius = min(radius*2, 8, height//3)  # 限制圆角大小

            # 绘制圆角背景
            draw_rounded_rect(canvas, 1, 1, width-1, height-1,
                            corner_radius, colors.primary)

            # 添加文字
            canvas.create_text(width//2, height//2, text=text,
                             fill="white", font=("微软雅黑", 9))

        def on_enter(event):
            canvas.delete("all")
            width = 120  # 固定宽度
            height = 30  # 固定高度

            # 计算合理的圆角半径
            corner_radius = min(radius*2, 8, height//3)

            draw_rounded_rect(canvas, 1, 1, width-1, height-1,
                            corner_radius, colors.secondary)
            canvas.create_text(width//2, height//2, text=text,
                             fill="white", font=("微软雅黑", 9))

        def on_leave(event):
            update_canvas()

        def on_click(event):
            if command:
                command()

        # 绑定事件
        canvas.bind('<Configure>', update_canvas)
        canvas.bind('<Enter>', on_enter)
        canvas.bind('<Leave>', on_leave)
        canvas.bind('<Button-1>', on_click)

        # 设置Canvas背景色
        canvas.configure(bg=self.style.colors.bg)

        return canvas

    def _apply_subtle_enhancements(self):
        """为其他控件应用微妙的增强效果"""
        try:
            colors = self.style.colors

            # 保持控件原始大小，只做微妙的视觉增强
            self.style.configure("TEntry",
                               relief="solid",
                               borderwidth=1,
                               insertcolor=colors.primary,
                               padding=(4, 2))

            self.style.configure("TCombobox",
                               relief="solid",
                               borderwidth=1,
                               padding=(4, 2))

            self.style.configure("TLabelframe",
                               relief="solid",
                               borderwidth=1)

        except Exception as e:
            print(f"[DEBUG] 应用微妙增强失败: {e}")

    def _apply_border_optimization(self):
        """应用边框优化，隐藏或变淡不必要的边框线"""
        try:
            colors = self.style.colors
            bg = colors.bg

            # 1. 优化Panedwindow分割线 - 让侧边栏和主区域分割线更细更不显眼
            try:
                # 基本分割线样式
                self.style.configure("TPanedwindow",
                                   sashwidth=1,  # 最细分割线宽度
                                   sashrelief="flat",  # 平面效果，无立体感
                                   background=bg)  # 与背景色一致

                # 分割线手柄样式 - 让分割线几乎不可见
                self.style.configure("TPanedwindow.Sash",
                                   background=bg,  # 分割线背景色与主背景相同
                                   relief="flat",  # 平面效果
                                   borderwidth=0)  # 无边框

                # 设置分割线的状态映射，确保在各种状态下都不显眼
                self.style.map("TPanedwindow",
                             background=[("active", bg), ("!active", bg)],
                             relief=[("active", "flat"), ("!active", "flat")])

                self.style.map("TPanedwindow.Sash",
                             background=[("active", bg), ("!active", bg), ("pressed", bg)],
                             relief=[("active", "flat"), ("!active", "flat"), ("pressed", "flat")])

                print(f"[DEBUG] 分割线样式优化成功，背景色: {bg}")

            except Exception as e:
                print(f"[DEBUG] 分割线样式设置失败: {e}")
                # 使用最基本的设置
                try:
                    self.style.configure("TPanedwindow", sashwidth=1, sashrelief="flat")
                except:
                    pass

            # 2. 淡化预览窗口边框 - 减少视觉干扰
            self.style.configure("Preview.TFrame",
                               relief="flat",  # 平面效果
                               borderwidth=0,  # 无边框
                               background=bg)

            # 3. 简化状态栏边框 - 只保留顶部分割线
            self.style.configure("Status.TFrame",
                               relief="flat",  # 平面效果
                               borderwidth=0,  # 无边框
                               background=bg)

            # 为状态栏添加顶部细线分割效果（通过样式实现）
            try:
                # 尝试为状态栏添加顶部边框
                self.style.map("Status.TFrame",
                             relief=[("!focus", "solid")],
                             borderwidth=[("!focus", "1 0 0 0")])  # 只有顶部边框
            except:
                # 如果不支持，使用简单的实线边框
                self.style.configure("Status.TFrame",
                                   relief="solid",
                                   borderwidth=1)

            # 4. 优化头部边框 - 保持清洁外观
            self.style.configure("Header.TFrame",
                               relief="flat",  # 平面效果
                               borderwidth=0,  # 无边框
                               background=bg)

            # 5. 侧边栏边框优化 - 保持功能分区但减少视觉干扰
            self.style.configure("Sidebar.TFrame",
                               relief="flat",  # 平面效果
                               borderwidth=0,  # 无边框
                               background=bg)

            # 6. Notebook标签页边框优化
            self.style.configure("TNotebook",
                               borderwidth=0,  # 无外边框
                               background=bg)

            # 尝试设置标签页边距（如果支持）
            try:
                self.style.configure("TNotebook", tabmargins=[0, 0, 0, 0])
            except:
                pass

            self.style.configure("TNotebook.Tab",
                               borderwidth=0,  # 无边框
                               focuscolor="none",  # 无焦点颜色
                               padding=[8, 4])  # 适当的内边距

            # 7. 通用Frame边框优化 - 减少所有Frame的默认边框
            self.style.configure("TFrame",
                               relief="flat",  # 平面效果
                               borderwidth=0,  # 无边框
                               background=bg)

            print("[DEBUG] 边框优化样式已应用")

        except Exception as e:
            print(f"[DEBUG] 应用边框优化失败: {e}")

    def _apply_default_styles(self):
        """恢复默认样式"""
        try:
            colors = self.style.colors

            # 清理自定义按钮（如果存在）
            if hasattr(self, '_custom_buttons'):
                for btn_info in self._custom_buttons:
                    btn_info['canvas'].destroy()
                self._custom_buttons = []

                # 重新显示原来的TTK按钮
                for btn in self._nav_buttons:
                    btn.pack(pady=5)

            # 恢复默认控件样式 - 使用ttkbootstrap的默认样式
            self.style.configure("TButton",
                               focuscolor="none")

            self.style.configure("TEntry",
                               insertcolor=colors.primary)

            self.style.configure("TCombobox")

            self.style.configure("TLabelframe")

            # 清除所有自定义映射
            self.style.map("TButton")
            self.style.map("TEntry")
            self.style.map("TCombobox")

            print(f"[DEBUG] 已恢复默认TTK样式")

        except Exception as e:
            print(f"[DEBUG] 恢复默认样式失败: {e}")

    def _apply_enhanced_rounded_styles(self, radius):
        """应用真正的圆角视觉效果"""
        try:
            colors = self.style.colors

            # 使用ttkbootstrap的内置圆角功能
            # 通过设置特殊的relief和borderwidth组合来模拟圆角

            # 计算圆角效果参数 - 扩大数值范围
            corner_effect = radius * 3  # 将1-5扩大到3-15的效果范围

            # 按钮样式 - 真正的圆角效果
            self.style.configure("TButton",
                               focuscolor="none",
                               relief="flat",  # 平面效果
                               borderwidth=0,  # 无边框
                               padding=(6, 4))  # 保持原始大小

            # 使用ttkbootstrap的圆角样式
            if hasattr(self.style, 'configure'):
                try:
                    # 尝试使用ttkbootstrap的圆角配置
                    self.style.configure("TButton",
                                       borderradius=corner_effect,  # 圆角半径
                                       highlightthickness=0)
                except:
                    # 如果不支持borderradius，使用其他方法
                    pass

            # 输入框样式 - 圆角效果
            self.style.configure("TEntry",
                               relief="flat",
                               borderwidth=1,
                               insertcolor=colors.primary,
                               padding=(4, 2))  # 保持原始大小

            try:
                self.style.configure("TEntry", borderradius=corner_effect)
            except:
                pass

            # 下拉框样式
            self.style.configure("TCombobox",
                               relief="flat",
                               borderwidth=1,
                               padding=(4, 2))  # 保持原始大小

            try:
                self.style.configure("TCombobox", borderradius=corner_effect)
            except:
                pass

            # 框架样式
            self.style.configure("TLabelframe",
                               relief="flat",
                               borderwidth=1)

            try:
                self.style.configure("TLabelframe", borderradius=corner_effect)
            except:
                pass

            # 复选框和单选按钮 - 保持原始大小
            self.style.configure("TCheckbutton",
                               focuscolor="none",
                               borderwidth=0,
                               relief="flat")

            self.style.configure("TRadiobutton",
                               focuscolor="none",
                               borderwidth=0,
                               relief="flat")

            print(f"[DEBUG] 真正圆角效果已应用，强度: {radius}, 圆角半径: {corner_effect}px")

        except Exception as e:
            print(f"[DEBUG] 应用圆角样式失败: {e}")
            # 备选方案：使用CSS样式的方法
            self._apply_css_rounded_styles(radius)

    # ---------------- Theme change ------------------------------------
    def _on_theme_change(self, theme: str):
        """主题变化处理（保留兼容性）"""
        if theme == self.style.theme.name:
            return

        # 保存主题设置
        self.ui_config.set("theme", theme)

        # 应用主题
        self.style.theme_use(theme)
        self._apply_custom_styles()

        # 同步更新配方配置页面的主题
        if hasattr(self, 'recipe_editor_instance') and self.recipe_editor_instance:
            try:
                self.recipe_editor_instance.update_theme(theme)
            except Exception as e:
                print(f"[DEBUG] 更新配方配置页面主题失败: {e}")

    # ---------------- Styles ------------------------------------------
    def _apply_custom_styles(self):
        colors = self.style.colors
        bg = colors.bg
        brand = colors.primary
        font_family = self.ui_config.get("font_family", "微软雅黑")
        title_size = self.ui_config.get("title_font_size", 14)

        self.configure(background=bg)
        self.style.configure("TFrame", background=bg)
        self.style.configure("TNotebook", background=bg)

        # 应用边框优化样式（如果启用）
        if self.ui_config.get("optimize_borders", True):
            self._apply_border_optimization()

        for cls in [
            "Sidebar.TFrame",
            "Header.TFrame",
            "Status.TFrame",
            "Preview.TFrame",
        ]:
            self.style.configure(cls, background=bg)

        # 品牌标签样式
        self.style.configure("Brand.TLabel",
                           background=bg,
                           foreground=brand,
                           font=(font_family, title_size, "bold"))

        # 系统标题样式
        self.style.configure("Title.TLabel",
                           background=bg,
                           foreground=brand,
                           font=(font_family, title_size, "bold"))

        # 应用字体设置到所有控件
        self._update_fonts()

        # 应用圆角样式
        self._apply_rounded_corners()

    # ---------------- Exit --------------------------------------------
    def _on_close(self):
        # 保存窗口大小和状态
        try:
            geometry = self.geometry()
            self.ui_config.set("window_geometry", geometry)

            # 保存窗口状态
            window_state = self.state()
            self.ui_config.set("window_state", window_state)
            print(f"[DEBUG] 保存窗口状态: {window_state}, 几何: {geometry}")

        except Exception as e:
            print(f"[DEBUG] 保存窗口信息失败: {e}")

        # 停止相机
        if self._camera_mgr:
            try:
                self._camera_mgr.stop_all()
            except Exception as e:
                print(f"[DEBUG] 停止相机失败: {e}")

        # 停止系统清理服务
        try:
            stop_system_cleaner()
            print("[DEBUG] 系统清理服务已停止")
        except Exception as e:
            print(f"[DEBUG] 停止系统清理服务失败: {e}")

        # 记录系统关闭日志
        try:
            log_system("SY Vision 2025 系统关闭", "INFO")
            if hasattr(self, 'system_logger'):
                self.system_logger.stop()
        except Exception as e:
            print(f"[DEBUG] 记录关闭日志失败: {e}")

        # 销毁窗口
        self.destroy()


if __name__ == "__main__":
    # 初始化路径管理和目录结构
    print_path_info()
    ensure_data_directories()

    # 启动主程序
    MainWindow().mainloop()
