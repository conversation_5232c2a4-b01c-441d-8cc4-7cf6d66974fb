"""Circle 工具插件包装."""
from __future__ import annotations
from typing import Dict, Any
import importlib
from plugins.plugin_base import PluginBase

class CircleToolPlugin(PluginBase):
    name = "circle_tool"
    label = "圆形测量"
    category = "几何测量"
    # 本地模板坐标系参数（以模板中心为原点, 像素）
    params: Dict[str, Any] = {
        "x": 0.0,      # 圆心 X 偏移
        "y": 0.0,      # 圆心 Y 偏移
        "r": 10.0,     # 估计半径
        "canny_th1": 50,
        "canny_th2": 150,
        "debug_draw": True,
    }

    def _warp(self, x0: float, y0: float, pose: Dict[str, Any]):
        """将模板坐标 (x0,y0) 根据 pose 变换到图像坐标."""
        import math
        cx, cy = pose.get("center", (0, 0))
        angle_deg = pose.get("angle", 0.0)
        scale = pose.get("scale", 1.0)
        ang = math.radians(angle_deg)
        x = scale * (x0 * math.cos(ang) - y0 * math.sin(ang)) + cx
        y = scale * (x0 * math.sin(ang) + y0 * math.cos(ang)) + cy
        return x, y

    def process(self, img, ctx):
        from .circle_tool import measure_circle  # 延迟导入避免循环
        # 兼容旧字段名 template_match
        if "pose" in ctx:
            pose = ctx["pose"]
        elif "template_match" in ctx:
            pose = ctx["template_match"]
        else:
            raise RuntimeError("circle_tool 需要先执行模板匹配并写入 ctx['pose'] 或 ctx['template_match']")
        x0 = float(self.params.get("x", 0))
        y0 = float(self.params.get("y", 0))
        r0 = float(self.params.get("r", 10))
        cx_img, cy_img = self._warp(x0, y0, pose)
        r_est = r0 * float(pose.get("scale", 1.0))
        res = measure_circle(
            img,
            cx_img,
            cy_img,
            r_est,
            canny_th1=int(self.params.get("canny_th1", 50)),
            canny_th2=int(self.params.get("canny_th2", 150)),
            return_debug=False,
        )
        ctx.setdefault("results", {})[self.name] = res
        # 将圆形 ROI 写入 roi_dict 便于可视化/下游使用
        ctx.setdefault("roi_dict", {})[self.name] = {
            "center": res.get("center"),
            "radius": res.get("radius"),
        }

        # ---- 可视化 ROI ----
        if bool(self.params.get("debug_draw", True)) and res.get("radius"):
            import cv2
            center_int = tuple(map(int, res["center"]))
            rad_int = int(res["radius"])
            cv2.circle(img, center_int, rad_int, (0, 255, 0), 1)
            cv2.circle(img, center_int, 2, (0, 0, 255), -1)

        return img, ctx

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        """打开专属 UI。"""
        try:
            mod = importlib.import_module('plugins.ui.geometry_tools.circle_tool_ui')
            ui_cls = getattr(mod, 'CircleToolUI', None)
            if ui_cls:
                if preview_img is not None:
                    ui_cls(master, img=preview_img)
                else:
                    ui_cls(master)
            else:
                from tkinter import messagebox
                messagebox.showerror('错误', '未找到 CircleToolUI')
        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror('错误', f'打开圆形测量工具失败: {str(e)}')
            import traceback
            traceback.print_exc()

_ = CircleToolPlugin()
