#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统日志模块
统一管理系统运行日志、检测日志、操作日志等
"""

import logging
import threading
import time
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from collections import deque
import queue

class SystemLogger:
    """系统日志管理器"""
    
    def __init__(self, log_dir: str = "logs", max_memory_logs: int = 1000):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 内存中的日志缓存（用于实时显示）
        self.memory_logs = deque(maxlen=max_memory_logs)
        self.log_queue = queue.Queue()
        
        # 日志监听器（用于UI更新）
        self.listeners = []
        
        # 设置日志记录器
        self._setup_loggers()
        
        # 启动日志处理线程
        self.running = True
        self.log_thread = threading.Thread(target=self._log_processor, daemon=True)
        self.log_thread.start()
    
    def _setup_loggers(self):
        """设置各种日志记录器"""
        
        # 主系统日志
        self.system_logger = self._create_logger(
            "system", 
            self.log_dir / "system.log",
            level=logging.INFO
        )
        
        # 检测日志
        self.detection_logger = self._create_logger(
            "detection",
            self.log_dir / "detection.log",
            level=logging.INFO
        )
        
        # 操作日志
        self.operation_logger = self._create_logger(
            "operation",
            self.log_dir / "operation.log",
            level=logging.INFO
        )
        
        # 错误日志
        self.error_logger = self._create_logger(
            "error",
            self.log_dir / "error.log",
            level=logging.ERROR
        )
        
        # 性能日志
        self.performance_logger = self._create_logger(
            "performance",
            self.log_dir / "performance.log",
            level=logging.INFO
        )
    
    def _create_logger(self, name: str, log_file: Path, level=logging.INFO):
        """创建日志记录器"""
        logger = logging.getLogger(f"SYVision.{name}")
        logger.setLevel(level)
        
        # 避免重复添加handler
        if not logger.handlers:
            # 文件处理器
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                log_file, 
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            
            # 格式化器
            formatter = logging.Formatter(
                '[%(asctime)s] [%(name)s] %(levelname)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _log_processor(self):
        """日志处理线程"""
        while self.running:
            try:
                # 处理队列中的日志
                try:
                    log_entry = self.log_queue.get(timeout=1.0)
                    self._process_log_entry(log_entry)
                except queue.Empty:
                    continue
                    
            except Exception as e:
                print(f"[LOG_PROCESSOR] 错误: {e}")
    
    def _process_log_entry(self, log_entry: Dict):
        """处理单个日志条目"""
        # 添加到内存缓存
        self.memory_logs.append(log_entry)
        
        # 通知监听器
        for listener in self.listeners:
            try:
                listener(log_entry)
            except Exception as e:
                print(f"[LOG_LISTENER] 错误: {e}")
    
    def add_listener(self, callback):
        """添加日志监听器"""
        self.listeners.append(callback)
    
    def remove_listener(self, callback):
        """移除日志监听器"""
        if callback in self.listeners:
            self.listeners.remove(callback)
    
    def log_system(self, message: str, level: str = "INFO", **kwargs):
        """记录系统日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "category": "SYSTEM",
            "level": level,
            "message": message,
            "details": kwargs
        }
        
        # 写入文件
        if level == "ERROR":
            self.error_logger.error(message)
            self.system_logger.error(message)
        elif level == "WARNING":
            self.system_logger.warning(message)
        else:
            self.system_logger.info(message)
        
        # 添加到队列
        self.log_queue.put(log_entry)
    
    def log_detection(self, workstation: str, result: str, details: Dict = None):
        """记录检测日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "category": "DETECTION",
            "level": "INFO",
            "workstation": workstation,
            "result": result,
            "message": f"工位 {workstation} 检测结果: {result}",
            "details": details or {}
        }
        
        # 写入文件
        self.detection_logger.info(
            f"工位:{workstation} 结果:{result} 详情:{json.dumps(details, ensure_ascii=False)}"
        )
        
        # 添加到队列
        self.log_queue.put(log_entry)
    
    def log_operation(self, user: str, operation: str, target: str = "", details: Dict = None):
        """记录操作日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "category": "OPERATION",
            "level": "INFO",
            "user": user,
            "operation": operation,
            "target": target,
            "message": f"用户 {user} 执行操作: {operation} {target}",
            "details": details or {}
        }
        
        # 写入文件
        self.operation_logger.info(
            f"用户:{user} 操作:{operation} 目标:{target} 详情:{json.dumps(details, ensure_ascii=False)}"
        )
        
        # 添加到队列
        self.log_queue.put(log_entry)
    
    def log_performance(self, metric: str, value: float, unit: str = "", details: Dict = None):
        """记录性能日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "category": "PERFORMANCE",
            "level": "INFO",
            "metric": metric,
            "value": value,
            "unit": unit,
            "message": f"性能指标 {metric}: {value} {unit}",
            "details": details or {}
        }
        
        # 写入文件
        self.performance_logger.info(
            f"指标:{metric} 值:{value} 单位:{unit} 详情:{json.dumps(details, ensure_ascii=False)}"
        )
        
        # 添加到队列
        self.log_queue.put(log_entry)
    
    def log_error(self, error: Exception, context: str = "", details: Dict = None):
        """记录错误日志"""
        import traceback
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "category": "ERROR",
            "level": "ERROR",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "message": f"错误: {type(error).__name__}: {str(error)}",
            "traceback": traceback.format_exc(),
            "details": details or {}
        }
        
        # 写入文件
        self.error_logger.error(
            f"上下文:{context} 错误:{type(error).__name__}: {str(error)}\n{traceback.format_exc()}"
        )
        
        # 添加到队列
        self.log_queue.put(log_entry)
    
    def get_recent_logs(self, count: int = 100, category: str = None, level: str = None) -> List[Dict]:
        """获取最近的日志"""
        logs = list(self.memory_logs)
        
        # 过滤
        if category:
            logs = [log for log in logs if log.get("category") == category]
        if level:
            logs = [log for log in logs if log.get("level") == level]
        
        # 返回最近的N条
        return logs[-count:] if count > 0 else logs
    
    def get_logs_by_time_range(self, start_time: datetime, end_time: datetime, category: str = None) -> List[Dict]:
        """根据时间范围获取日志"""
        logs = []
        
        for log in self.memory_logs:
            log_time = datetime.fromisoformat(log["timestamp"])
            if start_time <= log_time <= end_time:
                if not category or log.get("category") == category:
                    logs.append(log)
        
        return logs
    
    def get_detection_statistics(self, hours: int = 24) -> Dict:
        """获取检测统计信息"""
        start_time = datetime.now() - timedelta(hours=hours)
        detection_logs = self.get_logs_by_time_range(start_time, datetime.now(), "DETECTION")
        
        stats = {
            "total_detections": len(detection_logs),
            "ok_count": 0,
            "ng_count": 0,
            "workstation_stats": {},
            "hourly_stats": {}
        }
        
        for log in detection_logs:
            result = log.get("result", "")
            workstation = log.get("workstation", "unknown")
            
            # 总体统计
            if result == "OK":
                stats["ok_count"] += 1
            elif result == "NG":
                stats["ng_count"] += 1
            
            # 工位统计
            if workstation not in stats["workstation_stats"]:
                stats["workstation_stats"][workstation] = {"ok": 0, "ng": 0, "total": 0}
            
            stats["workstation_stats"][workstation]["total"] += 1
            if result == "OK":
                stats["workstation_stats"][workstation]["ok"] += 1
            elif result == "NG":
                stats["workstation_stats"][workstation]["ng"] += 1
            
            # 按小时统计
            hour_key = log["timestamp"][:13]  # YYYY-MM-DDTHH
            if hour_key not in stats["hourly_stats"]:
                stats["hourly_stats"][hour_key] = {"ok": 0, "ng": 0, "total": 0}
            
            stats["hourly_stats"][hour_key]["total"] += 1
            if result == "OK":
                stats["hourly_stats"][hour_key]["ok"] += 1
            elif result == "NG":
                stats["hourly_stats"][hour_key]["ng"] += 1
        
        return stats
    
    def export_logs(self, file_path: str, start_time: datetime = None, end_time: datetime = None, category: str = None):
        """导出日志到文件"""
        if start_time and end_time:
            logs = self.get_logs_by_time_range(start_time, end_time, category)
        else:
            logs = self.get_recent_logs(category=category)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(logs, f, indent=2, ensure_ascii=False)
    
    def clear_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for log_file in self.log_dir.glob("*.log*"):
            try:
                if log_file.stat().st_mtime < cutoff_time.timestamp():
                    log_file.unlink()
                    self.log_system(f"删除旧日志文件: {log_file}")
            except Exception as e:
                self.log_error(e, f"删除日志文件失败: {log_file}")
    
    def stop(self):
        """停止日志处理"""
        self.running = False
        if self.log_thread.is_alive():
            self.log_thread.join(timeout=2.0)


# 全局日志管理器实例
_system_logger = None

def get_system_logger() -> SystemLogger:
    """获取系统日志管理器实例"""
    global _system_logger
    if _system_logger is None:
        _system_logger = SystemLogger()
    return _system_logger

def log_system(message: str, level: str = "INFO", **kwargs):
    """记录系统日志"""
    logger = get_system_logger()
    logger.log_system(message, level, **kwargs)

def log_detection(workstation: str, result: str, details: Dict = None):
    """记录检测日志"""
    logger = get_system_logger()
    logger.log_detection(workstation, result, details)

def log_operation(user: str, operation: str, target: str = "", details: Dict = None):
    """记录操作日志"""
    logger = get_system_logger()
    logger.log_operation(user, operation, target, details)

def log_performance(metric: str, value: float, unit: str = "", details: Dict = None):
    """记录性能日志"""
    logger = get_system_logger()
    logger.log_performance(metric, value, unit, details)

def log_error(error: Exception, context: str = "", details: Dict = None):
    """记录错误日志"""
    logger = get_system_logger()
    logger.log_error(error, context, details)
