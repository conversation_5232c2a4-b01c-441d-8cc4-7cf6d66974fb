"""
测量结果验证和质量控制模块
提供置信度评估、异常值检测和结果合理性检查
"""

import numpy as np
import logging
from typing import Dict, List, Any, Tuple, Optional

_log = logging.getLogger(__name__)


class MeasurementValidator:
    """测量结果验证器"""
    
    @staticmethod
    def validate_result(result: Dict[str, Any], roi_info: Dict[str, Any] = None, 
                       params: Dict[str, Any] = None) -> Dict[str, Any]:
        """验证单个测量结果
        
        Args:
            result: 测量结果
            roi_info: ROI信息
            params: 验证参数
            
        Returns:
            验证后的结果（包含置信度和质量评分）
        """
        if params is None:
            params = {}
            
        validated_result = result.copy()
        
        try:
            # 1. 基础有效性检查
            basic_score = MeasurementValidator._check_basic_validity(result)
            
            # 2. 几何合理性检查
            geometry_score = MeasurementValidator._check_geometry_validity(result, roi_info)
            
            # 3. 数据一致性检查
            consistency_score = MeasurementValidator._check_data_consistency(result)
            
            # 4. 边缘质量评估
            edge_score = MeasurementValidator._assess_edge_quality(result)
            
            # 5. 计算综合置信度
            confidence = MeasurementValidator._calculate_confidence(
                basic_score, geometry_score, consistency_score, edge_score
            )
            
            # 6. 质量等级评定
            quality_grade = MeasurementValidator._grade_quality(confidence)
            
            # 更新结果
            validated_result.update({
                'confidence': confidence,
                'quality_grade': quality_grade,
                'validation_scores': {
                    'basic': basic_score,
                    'geometry': geometry_score,
                    'consistency': consistency_score,
                    'edge': edge_score
                },
                'validated': True
            })
            
            # 7. 异常值检测
            if confidence < params.get('min_confidence', 0.3):
                validated_result['warning'] = 'Low confidence measurement'
                validated_result['ok'] = False
            
        except Exception as e:
            _log.error(f"Result validation failed: {e}")
            validated_result.update({
                'confidence': 0.0,
                'quality_grade': 'F',
                'validation_error': str(e),
                'validated': False
            })
        
        return validated_result
    
    @staticmethod
    def _check_basic_validity(result: Dict[str, Any]) -> float:
        """检查基础有效性"""
        score = 0.0
        
        # 检查必要字段
        if result.get('ok', False):
            score += 0.3
        
        # 检查测量类型
        if result.get('type') in ['cross_caliper', 'rect_caliper', 'circle', 'angle', 'distance']:
            score += 0.2
        
        # 检查数值合理性
        if result.get('type') in ['cross_caliper', 'rect_caliper']:
            width = result.get('width', 0)
            height = result.get('height', 0)
            if width > 0 and height > 0:
                score += 0.3
                # 检查宽高比是否合理
                ratio = max(width, height) / min(width, height)
                if ratio < 10:  # 宽高比不超过10:1
                    score += 0.2
        
        return min(score, 1.0)
    
    @staticmethod
    def _check_geometry_validity(result: Dict[str, Any], roi_info: Dict[str, Any] = None) -> float:
        """检查几何合理性"""
        score = 0.0
        
        if roi_info is None:
            return 0.5  # 无ROI信息时给中等分数
        
        try:
            result_type = result.get('type')
            
            if result_type in ['cross_caliper', 'rect_caliper']:
                width = result.get('width', 0)
                height = result.get('height', 0)
                roi_width = roi_info.get('len_x', 0)
                roi_height = roi_info.get('len_y', 0)
                
                if roi_width > 0 and roi_height > 0:
                    # 测量结果应该在ROI范围内
                    if width <= roi_width * 1.2 and height <= roi_height * 1.2:
                        score += 0.4
                    
                    # 测量结果不应该太小
                    if width >= roi_width * 0.1 and height >= roi_height * 0.1:
                        score += 0.3
                    
                    # 中心点应该在ROI附近
                    result_center = result.get('center')
                    roi_center = roi_info.get('center')
                    if result_center and roi_center:
                        distance = np.sqrt((result_center[0] - roi_center[0])**2 + 
                                         (result_center[1] - roi_center[1])**2)
                        max_distance = max(roi_width, roi_height) * 0.5
                        if distance <= max_distance:
                            score += 0.3
            
            elif result_type == 'circle':
                radius = result.get('radius', 0)
                roi_size = min(roi_info.get('len_x', 0), roi_info.get('len_y', 0))
                if roi_size > 0 and 0 < radius <= roi_size * 0.6:
                    score += 0.7
                    
        except Exception as e:
            _log.warning(f"Geometry validation error: {e}")
            score = 0.3
        
        return min(score, 1.0)
    
    @staticmethod
    def _check_data_consistency(result: Dict[str, Any]) -> float:
        """检查数据一致性"""
        score = 0.0
        
        try:
            result_type = result.get('type')
            
            if result_type in ['cross_caliper', 'rect_caliper']:
                # 检查边缘点数量
                edge_pts1 = result.get('edge_pts1', [])
                edge_pts2 = result.get('edge_pts2', [])
                scan_lines = result.get('scan_lines', [])
                
                if len(edge_pts1) > 0 and len(edge_pts2) > 0:
                    score += 0.3
                    
                    # 边缘点数量应该合理
                    if len(edge_pts1) >= 3 and len(edge_pts2) >= 3:
                        score += 0.2
                    
                    # 扫描线数量应该与边缘点数量相关
                    if len(scan_lines) > 0:
                        score += 0.2
                        
                        # 检查边缘点分布的一致性
                        if len(edge_pts1) == len(edge_pts2):
                            score += 0.3
            
            elif result_type == 'circle':
                radius = result.get('radius', 0)
                diameter = result.get('diameter', 0)
                if abs(diameter - 2 * radius) < 0.1:
                    score += 0.5
                
                area = result.get('area', 0)
                expected_area = np.pi * radius * radius
                if abs(area - expected_area) < expected_area * 0.1:
                    score += 0.5
                    
        except Exception as e:
            _log.warning(f"Consistency check error: {e}")
            score = 0.3
        
        return min(score, 1.0)
    
    @staticmethod
    def _assess_edge_quality(result: Dict[str, Any]) -> float:
        """评估边缘质量"""
        score = 0.0
        
        try:
            edge_pts1 = result.get('edge_pts1', [])
            edge_pts2 = result.get('edge_pts2', [])
            
            if edge_pts1 and edge_pts2:
                # 边缘点数量评分
                total_points = len(edge_pts1) + len(edge_pts2)
                if total_points >= 10:
                    score += 0.3
                elif total_points >= 5:
                    score += 0.2
                else:
                    score += 0.1
                
                # 边缘点分布均匀性
                if len(edge_pts1) >= 3:
                    pts = np.array(edge_pts1)
                    distances = np.sqrt(np.sum(np.diff(pts, axis=0)**2, axis=1))
                    if len(distances) > 0:
                        cv = np.std(distances) / np.mean(distances) if np.mean(distances) > 0 else 1
                        if cv < 0.5:  # 变异系数小于0.5表示分布较均匀
                            score += 0.4
                        elif cv < 1.0:
                            score += 0.2
                
                # 边缘点对称性（对于矩形测量）
                if len(edge_pts1) == len(edge_pts2) and len(edge_pts1) >= 3:
                    score += 0.3
                    
        except Exception as e:
            _log.warning(f"Edge quality assessment error: {e}")
            score = 0.3
        
        return min(score, 1.0)
    
    @staticmethod
    def _calculate_confidence(basic: float, geometry: float, consistency: float, edge: float) -> float:
        """计算综合置信度"""
        # 加权平均
        weights = [0.2, 0.3, 0.3, 0.2]  # 基础、几何、一致性、边缘质量的权重
        scores = [basic, geometry, consistency, edge]
        
        confidence = sum(w * s for w, s in zip(weights, scores))
        return min(max(confidence, 0.0), 1.0)
    
    @staticmethod
    def _grade_quality(confidence: float) -> str:
        """质量等级评定"""
        if confidence >= 0.9:
            return 'A+'
        elif confidence >= 0.8:
            return 'A'
        elif confidence >= 0.7:
            return 'B+'
        elif confidence >= 0.6:
            return 'B'
        elif confidence >= 0.5:
            return 'C+'
        elif confidence >= 0.4:
            return 'C'
        elif confidence >= 0.3:
            return 'D'
        else:
            return 'F'


class OutlierDetector:
    """异常值检测器"""
    
    @staticmethod
    def detect_outliers(results: List[Dict[str, Any]], method: str = 'iqr') -> List[bool]:
        """检测异常值
        
        Args:
            results: 测量结果列表
            method: 检测方法 ('iqr', 'zscore', 'isolation')
            
        Returns:
            布尔列表，True表示异常值
        """
        if len(results) < 3:
            return [False] * len(results)
        
        try:
            if method == 'iqr':
                return OutlierDetector._iqr_outliers(results)
            elif method == 'zscore':
                return OutlierDetector._zscore_outliers(results)
            else:
                return [False] * len(results)
        except Exception as e:
            _log.error(f"Outlier detection failed: {e}")
            return [False] * len(results)
    
    @staticmethod
    def _iqr_outliers(results: List[Dict[str, Any]]) -> List[bool]:
        """使用IQR方法检测异常值"""
        # 提取数值特征
        features = []
        for result in results:
            if result.get('type') in ['cross_caliper', 'rect_caliper']:
                width = result.get('width', 0)
                height = result.get('height', 0)
                features.append([width, height])
            elif result.get('type') == 'circle':
                radius = result.get('radius', 0)
                features.append([radius, radius])
            else:
                features.append([0, 0])
        
        features = np.array(features)
        outliers = [False] * len(results)
        
        for i in range(features.shape[1]):
            values = features[:, i]
            q1, q3 = np.percentile(values, [25, 75])
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            for j, value in enumerate(values):
                if value < lower_bound or value > upper_bound:
                    outliers[j] = True
        
        return outliers
    
    @staticmethod
    def _zscore_outliers(results: List[Dict[str, Any]], threshold: float = 3.0) -> List[bool]:
        """使用Z-score方法检测异常值"""
        # 提取置信度分数
        confidences = [result.get('confidence', 0.5) for result in results]
        confidences = np.array(confidences)
        
        if len(confidences) < 3:
            return [False] * len(results)
        
        mean_conf = np.mean(confidences)
        std_conf = np.std(confidences)
        
        if std_conf == 0:
            return [False] * len(results)
        
        z_scores = np.abs((confidences - mean_conf) / std_conf)
        return (z_scores > threshold).tolist()
