from abc import ABC, abstractmethod

class CameraBase(ABC):
    """
    相机基类，所有品牌相机驱动需继承并实现以下接口。
    """
    def __init__(self, camera_id, config):
        self.camera_id = camera_id
        self.config = config
        self.status = 'closed'

    @abstractmethod
    def open(self):
        """打开相机"""
        pass

    @abstractmethod
    def close(self):
        """关闭相机"""
        pass

    @abstractmethod
    def grab(self):
        """采集一帧图像，返回图像数据"""
        pass

    @abstractmethod
    def reconnect(self):
        """自动重连"""
        pass

    @abstractmethod
    def get_status(self):
        """获取当前状态"""
        return self.status
