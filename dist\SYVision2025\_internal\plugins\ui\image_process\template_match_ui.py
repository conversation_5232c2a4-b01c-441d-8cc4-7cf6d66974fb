import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2, yaml, numpy as np, sys, os
from PIL import Image, ImageTk
from pathlib import Path
# 保证根目录在 sys.path 以支持直接运行该脚本
ROOT = Path(__file__).resolve().parents[3]
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

# 配置文件目录: <项目根>/configs/template_match
CFG_DIR = ROOT / 'configs' / 'template_match'
CFG_DIR.mkdir(parents=True, exist_ok=True)
# 模板默认保存目录: <项目根>/configs/template_match/templates
TPL_DIR = CFG_DIR / 'templates'
TPL_DIR.mkdir(parents=True, exist_ok=True)

from plugins.core.image_process.template_match import TemplateMatcher

class TemplateMatchFrame(tk.Frame):
    from typing import Optional
    def __init__(self, master, preview: Optional[dict]=None):
        super().__init__(master)
        if isinstance(master,(tk.Tk, tk.Toplevel)):
            master.geometry('1200x600')
        self.matcher = TemplateMatcher()
        self.tpl_path: str | None = None  # 记录当前模板路径
        
        # 兼容处理preview参数：可能是字典{'img': array}或直接是numpy数组
        if preview is None:
            self.img_src = None
        elif isinstance(preview, dict):
            self.img_src = preview.get('img')
        elif hasattr(preview, 'ndim'):  # numpy数组
            self.img_src = preview
        else:
            self.img_src = None
            
        self.img_out = None
        self.preview_max = 450
        self.zoom_var = tk.DoubleVar(value=1.0)
        # 新增：ROI 外扩比例
        self.expand_ratio = tk.DoubleVar(value=1.0)
        self.roi_rect = None
        self.poly_pts = []
        self.roi_mode = tk.StringVar(value='rect')
        self._build_ui()
        if self.img_src is not None:
            self._show_image(self.canvas_src, self.img_src, is_src=True)

    # ---------------- UI -----------------
    def _build_ui(self):
        pfrm = ttk.LabelFrame(self, text='参数')
        pfrm.grid(row=0, column=0, sticky='ns')
        ttk.Button(pfrm, text='加载图片', command=self._load_image).grid(row=0, column=0, columnspan=2, sticky='we')
        self.load_btn = ttk.Button(pfrm, text='加载模板', command=self._load_tpl)
        self.load_btn.grid(row=1, column=0, columnspan=2, sticky='we')
        ttk.Button(pfrm, text='保存模板', command=self._save_tpl).grid(row=2, column=0, columnspan=2, sticky='we')
        ttk.Button(pfrm, text='ROI抓模板', command=self._grab_tpl).grid(row=3, column=0, columnspan=2, sticky='we')

        # 外扩比例输入
        ttk.Label(pfrm, text='外扩比例').grid(row=4, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.expand_ratio, width=6).grid(row=4, column=1, sticky='w')

        ttk.Button(pfrm, text='清除ROI', command=self._clear_roi).grid(row=5, column=0, columnspan=2, sticky='we')
        ttk.Label(pfrm, text='ROI模式').grid(row=6, column=0, sticky='e')
        ttk.OptionMenu(pfrm, self.roi_mode, 'rect', 'rect', 'poly').grid(row=6, column=1)
        ttk.Button(pfrm, text='完成多边形', command=self._poly_finish).grid(row=7, column=0, columnspan=2, sticky='we')

        ttk.Label(pfrm, text='方法').grid(row=8, column=0, sticky='e')
        self.method_var = tk.StringVar(value='相关系数')
        self.METHOD_CN = {
            '相关系数':'TM_CCOEFF_NORMED',
            '相关匹配':'TM_CCORR_NORMED',
            '差值匹配':'TM_SQDIFF_NORMED',
            '形状匹配':'SHAPE'
        }
        ttk.OptionMenu(pfrm, self.method_var, '相关系数', *self.METHOD_CN.keys()).grid(row=8, column=1, sticky='w')
        ttk.Label(pfrm, text='阈值').grid(row=9, column=0, sticky='e')
        self.thr_var = tk.DoubleVar(value=0.7)
        ttk.Entry(pfrm, textvariable=self.thr_var, width=6).grid(row=9, column=1)

        # rotation params
        self.rot_enable = tk.BooleanVar(value=False)
        self.angle_from = tk.DoubleVar(value=-20)
        self.angle_to = tk.DoubleVar(value=20)
        self.angle_step = tk.DoubleVar(value=5)
        ttk.Checkbutton(pfrm, text='旋转搜索', variable=self.rot_enable).grid(row=10, column=0, sticky='w')
        angfrm = ttk.Frame(pfrm); angfrm.grid(row=11, column=0, columnspan=2)
        ttk.Label(angfrm, text='角度').pack(side='left')
        ttk.Entry(angfrm, textvariable=self.angle_from, width=4).pack(side='left')
        ttk.Label(angfrm, text='~').pack(side='left')
        ttk.Entry(angfrm, textvariable=self.angle_to, width=4).pack(side='left')
        ttk.Label(angfrm, text='步').pack(side='left')
        ttk.Entry(angfrm, textvariable=self.angle_step, width=3).pack(side='left')

        row_run = 12
        ttk.Button(pfrm, text='运行', command=self._run).grid(row=row_run, column=0, columnspan=2, sticky='we')
        ttk.Button(pfrm, text='保存结果', command=self._save_res).grid(row=row_run + 1, column=0, columnspan=2, sticky='we')
        ttk.Button(pfrm, text='保存配置', command=lambda: self._save_yaml(False)).grid(row=row_run + 2, column=0, sticky='we')
        ttk.Button(pfrm, text='加载配置', command=self._load_yaml).grid(row=row_run + 2, column=1, sticky='we')

        self.info_var = tk.StringVar(value='score:- pos:-,-')
        ttk.Label(self, textvariable=self.info_var).grid(row=1, column=0, columnspan=3, sticky='w')

        self.canvas_src = tk.Canvas(self, width=self.preview_max, height=self.preview_max, bg='black')
        self.canvas_res = tk.Canvas(self, width=self.preview_max, height=self.preview_max, bg='black')
        self.canvas_src.grid(row=0, column=1)
        self.canvas_res.grid(row=0, column=2)

        # bind events
        self.canvas_src.bind('<MouseWheel>', self._on_wheel)
        self.canvas_src.bind('<Button-1>', self._roi_start)
        self.canvas_src.bind('<B1-Motion>', self._roi_drag)
        self.canvas_src.bind('<ButtonRelease-1>', self._roi_end)
        self.canvas_src.bind('<Button-3>', self._poly_add)

    def _draw_roi(self, closed=False):
        self._show_image(self.canvas_src, self.img_src, is_src=True)
        if self.roi_mode.get() == 'rect' and self.roi_rect:
            x,y,w,h = self.roi_rect
            x1,y1,x2,y2 = x,y,x+w,y+h
            # to display coords
            x1,y1,x2,y2 = [v * self._src_zoom for v in (x1,y1,x2,y2)]
            self.canvas_src.create_rectangle(x1,y1,x2,y2, outline='red', width=2)
        elif self.roi_mode.get() == 'poly' and self.poly_pts:
            pts_disp = [(x*self._src_zoom, y*self._src_zoom) for x,y in self.poly_pts]
            self.canvas_src.create_line(pts_disp, fill='red', width=2)
            if closed:
                self.canvas_src.create_line([pts_disp[-1], pts_disp[0]], fill='red', width=2)

    def _poly_add(self, event):
        if self.roi_mode.get() != 'poly' or self.img_src is None: return
        x, y = int(event.x / self._src_zoom), int(event.y / self._src_zoom)
        self.poly_pts.append((x,y))
        self._draw_roi()

    def _poly_finish(self):
        if self.roi_mode.get() != 'poly' or len(self.poly_pts) < 3: return
        self._draw_roi(closed=True)
        messagebox.showinfo('多边形', '多边形定义完成')

    def _get_poly_roi(self):
        mask = np.zeros(self.img_src.shape[:2], dtype=np.uint8)
        pts = np.array(self.poly_pts, dtype=np.int32)
        cv2.fillPoly(mask, [pts], 255)
        x,y,w,h = cv2.boundingRect(pts)
        masked_img = cv2.bitwise_and(self.img_src, self.img_src, mask=mask)
        return masked_img[y:y+h, x:x+w]

    # ---------------- actions -----------------
    def _load_tpl(self):
        path = filedialog.askopenfilename(filetypes=[('image', '*.png;*.jpg;*.bmp')])
        if not path:
            return
        self.matcher.load_template(path)
        self.tpl_path = path
        messagebox.showinfo('模板', f'模板已加载: {Path(path).name}')

    def _save_tpl(self):
        """保存当前模板到固定目录，自动给出默认文件名"""
        # 确保已有模板
        tpl = getattr(self.matcher, 'tpl', None)
        if tpl is None:
            messagebox.showwarning('警告', '尚未设置模板'); return
        # 如果已有模板路径（如全局模板），直接覆盖，无需询问
        if self.tpl_path:
            path = self.tpl_path
        else:
            # 默认文件名
            import datetime
            from pathlib import Path as _P
            fname = datetime.datetime.now().strftime('tpl_%Y%m%d_%H%M%S.png')
            path = filedialog.asksaveasfilename(
                defaultextension='.png',
                initialdir=TPL_DIR,
                initialfile=fname,
                filetypes=[('PNG', '*.png')],
                title='保存模板'
            )
            if not path:
                return
            # 若未带扩展名，自动补 .png
            if _P(path).suffix.lower() != '.png':
                path += '.png'
        try:
            self.matcher.save_template(path)
            self.tpl_path = path
            messagebox.showinfo('模板', '已保存')
        except Exception as e:
            messagebox.showerror('错误', f'保存失败: {e}')



    def _grab_tpl(self):
        if self.img_src is None:
            messagebox.showwarning('警告', '请先加载图片'); return

        if self.roi_mode.get() == 'rect':
            if self.roi_rect is None:
                messagebox.showwarning('警告', '请先框选矩形ROI'); return
            x, y, w, h = self.roi_rect
            tpl = self.img_src[y:y + h, x:x + w]
        else: # poly
            if len(self.poly_pts) < 3:
                messagebox.showwarning('警告', '请先定义至少3个点的多边形ROI'); return
            tpl = self._get_poly_roi()
        
        self.matcher.set_template(tpl)
        messagebox.showinfo('模板', '已从ROI抓取模板')

    def _run(self):
        if self.img_src is None:
            self._load_image()
            if self.img_src is None:
                return
        sel_method_cn=self.method_var.get()
        self.matcher.method_name = self.METHOD_CN[sel_method_cn]
        if self.matcher.method_name==TemplateMatcher.SHAPE_METHOD:
            self.matcher.method=None
        else:
            self.matcher.method = TemplateMatcher.METHODS[self.matcher.method_name]
        self.matcher.min_conf = self.thr_var.get()
        angles=None
        if self.rot_enable.get():
            f,t,s=self.angle_from.get(), self.angle_to.get(), self.angle_step.get()
            angles=[round(a,1) for a in np.arange(f, t+1e-6, s)]
        val = float(self.expand_ratio.get())
        ratio = val/100 + 1 if val > 3 else val  # >3 视作百分比，如20 -> 1.2
        draw_flag = abs(ratio - 1.0) < 1e-3
        res = self.matcher.match(self.img_src, angles=angles, draw=draw_flag)
        info = res['info']
        import cv2, numpy as _np
        img_out = res['img_out'].copy()
        if not draw_flag and 'corners' in info:
            pts = _np.array(info['corners'], dtype=float)
            c = pts.mean(axis=0)
            pts = (pts - c) * ratio + c
            pts_int = _np.round(pts).astype(int).reshape(-1,1,2)
            cv2.polylines(img_out, [pts_int], True, (0,255,255), 2)
            cx, cy = map(int, info.get('center', (0,0)))
            cv2.drawMarker(img_out, (cx,cy), (0,255,0), markerType=cv2.MARKER_CROSS, thickness=1)
        self.img_out = img_out
        self._show_image(self.canvas_res, self.img_out)
        ok_txt = '合格' if info['ok'] else '不合格'
        self.info_var.set(f"得分:{info['score']} 阈值:{self.thr_var.get()} 角度:{info.get('angle',0)} 结果:{ok_txt}")
        # cache last info for saving
        self.last_info = info

    def _load_image(self):
        path = filedialog.askopenfilename(filetypes=[('image', '*.png;*.jpg;*.bmp')])
        if not path:
            return
        self.img_src = cv2.imdecode(np.fromfile(path, dtype=np.uint8), cv2.IMREAD_COLOR)
        self._show_image(self.canvas_src, self.img_src, is_src=True)

    def _save_res(self):
        if self.img_out is None:
            messagebox.showwarning('警告', '请先运行')
            return
        path = filedialog.asksaveasfilename(defaultextension='.png')
        if path:
            cv2.imencode(Path(path).suffix, self.img_out)[1].tofile(path)

    # ---------------- YAML -----------------
    def _save_yaml(self, ask=True):
        # ------ 组装参数 ------
        cfg = {
            'method': self.method_var.get(),
            'thr': float(self.thr_var.get()),
            'rot_enable': bool(self.rot_enable.get()),
            'angle_from': float(self.angle_from.get()),
            'angle_to': float(self.angle_to.get()),
            'angle_step': float(self.angle_step.get()),
        'expand_ratio': float(self.expand_ratio.get()),
        }
        # include last detection info if available
        if hasattr(self, 'last_info'):
            # convert numpy types to native python for YAML
            def _to_py(o):
                import numpy as _np
                if isinstance(o, dict):
                    return {k: _to_py(v) for k, v in o.items()}
                if isinstance(o, (list, tuple)):
                    return [_to_py(v) for v in o]
                if isinstance(o, _np.generic):
                    return o.item()
                return o

            cfg['detect_info'] = _to_py(self.last_info)

        # ------ 确定保存路径 ------
        if ask:
            path = filedialog.asksaveasfilename(
                defaultextension='.yml',
                initialdir=CFG_DIR,
                initialfile='default.yml',
                filetypes=[('YAML', '*.yml;*.yaml')],
                title='保存配置'
            )
            if not path:
                return
        else:
            path = CFG_DIR / 'default.yml'

        try:
            with open(path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(cfg, f, allow_unicode=True)
            messagebox.showinfo('配置', f'保存完毕:\n{path}')
        except Exception as e:
            messagebox.showerror('错误', f'保存失败:\n{e}')

    def _load_yaml(self):
        path = filedialog.askopenfilename(
            initialdir=CFG_DIR,
            filetypes=[('YAML', '*.yml;*.yaml')],
            title='加载配置'
        )
        if not path:
            return
        cfg = yaml.safe_load(open(path, 'r', encoding='utf-8'))
        self.method_var.set(cfg.get('method', '相关系数'))
        self.thr_var.set(cfg.get('thr', 0.7))
        # rotation params
        self.rot_enable.set(cfg.get('rot_enable', False))
        self.angle_from.set(cfg.get('angle_from', -20))
        self.angle_to.set(cfg.get('angle_to', 20))
        self.angle_step.set(cfg.get('angle_step', 5))
        self.expand_ratio.set(cfg.get('expand_ratio', 1.0))
        messagebox.showinfo('配置', '加载完成')

    # ---------------- utils -----------------
    def _show_image(self, canvas: tk.Canvas, img_bgr: np.ndarray, *, is_src=False):
        if img_bgr is None:
            return
        # 确保输入是有效的numpy数组
        if not hasattr(img_bgr, 'shape') or not hasattr(img_bgr, 'ndim'):
            return
        if img_bgr.ndim < 2:
            return
        zoom_display = self.zoom_var.get()
        h, w = img_bgr.shape[:2]
        disp_w, disp_h = int(w * zoom_display), int(h * zoom_display)
        if max(disp_w, disp_h) > self.preview_max:
            scale = self.preview_max / max(disp_w, disp_h)
            disp_w, disp_h = int(disp_w * scale), int(disp_h * scale)
            zoom_display *= scale
        img_disp = cv2.resize(img_bgr, (disp_w, disp_h)) if zoom_display != 1 else img_bgr.copy()
        img_rgb = cv2.cvtColor(img_disp, cv2.COLOR_BGR2RGB)
        pil = Image.fromarray(img_rgb)
        photo = ImageTk.PhotoImage(pil)
        canvas.delete('all')
        canvas.create_image(0, 0, anchor='nw', image=photo)
        canvas.image = photo
        if is_src:
            self._src_zoom = zoom_display
            # draw ROI overlays
            if self.roi_rect:
                x,y,w,h=self.roi_rect
                x0,y0=x*zoom_display, y*zoom_display
                x1,y1=(x+w)*zoom_display, (y+h)*zoom_display
                canvas.create_rectangle(x0,y0,x1,y1, outline='lime', dash=(3,2))
            if self.poly_pts:
                disp_pts=[(px*zoom_display, py*zoom_display) for px,py in self.poly_pts]
                if len(disp_pts)>1:
                    canvas.create_line(*sum(disp_pts,()), fill='lime', width=2)
                for px,py in disp_pts:
                    canvas.create_oval(px-3,py-3,px+3,py+3, fill='lime')

    # ---------- ROI & Zoom helpers (from contour_ui) ----------
    def _on_wheel(self, event):
        step = 0.1 if event.delta > 0 else -0.1
        new_val = max(0.25, min(2.0, self.zoom_var.get() + step))
        self.zoom_var.set(round(new_val, 2))
        self._refresh_images()

    def _roi_start(self, event):
        if self.roi_mode.get() == 'rect':
            self._roi_start_pt = (event.x, event.y)
            if hasattr(self, '_roi_canvas_id') and self._roi_canvas_id:
                self.canvas_src.delete(self._roi_canvas_id)
                self._roi_canvas_id = None
        else:
            z = getattr(self, '_src_zoom', self.zoom_var.get())
            self.poly_pts.append((int(event.x / z), int(event.y / z)))
            self._refresh_images()

    def _roi_drag(self, event):
        if self.roi_mode.get() == 'rect' and hasattr(self, '_roi_start_pt'):
            x0, y0 = self._roi_start_pt
            x1, y1 = event.x, event.y
            if hasattr(self, '_roi_canvas_id') and self._roi_canvas_id:
                self.canvas_src.coords(self._roi_canvas_id, x0, y0, x1, y1)
            else:
                self._roi_canvas_id = self.canvas_src.create_rectangle(x0, y0, x1, y1, outline='lime', dash=(3, 2))

    def _roi_end(self, event):
        if self.roi_mode.get() == 'rect' and hasattr(self, '_roi_start_pt'):
            x0, y0 = self._roi_start_pt
            x1, y1 = event.x, event.y
            del self._roi_start_pt
            z = getattr(self, '_src_zoom', self.zoom_var.get())
            xi, yi = int(min(x0, x1) / z), int(min(y0, y1) / z)
            wi, hi = int(abs(x1 - x0) / z), int(abs(y1 - y0) / z)
            self.roi_rect = (xi, yi, wi, hi)
            self.poly_pts = []
            self._refresh_images()

    def _poly_add(self, event):
        if self.roi_mode.get() == 'poly':
            z = getattr(self, '_src_zoom', self.zoom_var.get())
            self.poly_pts.append((int(event.x / z), int(event.y / z)))
            self._refresh_images()

    def _poly_finish(self):
        self._refresh_images()

    def _refresh_images(self):
        self._show_image(self.canvas_src, self.img_src, is_src=True)
        self._show_image(self.canvas_res, self.img_out)

    def _clear_roi(self):
        self.roi_rect = None
        self.poly_pts = []
        if hasattr(self, '_roi_canvas_id') and self._roi_canvas_id:
            self.canvas_src.delete(self._roi_canvas_id)
            self._roi_canvas_id=None
        self._refresh_images()

if __name__=='__main__':
    root=tk.Tk(); root.title('模板匹配测试'); TemplateMatchFrame(root).pack(); root.mainloop()
