; SY Vision 2025 安装脚本
; 使用 Inno Setup 编译器编译此脚本

#define MyAppName "SY Vision 2025"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "SY Technology"
#define MyAppURL "https://www.sy-tech.com"
#define MyAppExeName "SYVision2025.exe"
#define MyAppDescription "专业机器视觉检测系统"

[Setup]
; 注意: AppId的值唯一标识此应用程序
; 不要为其他安装程序使用相同的AppId值
AppId={{A1B2C3D4-E5F6-7890-ABCD-123456789012}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
DisableProgramGroupPage=yes
; 移除以下行以在管理安装模式下运行（为所有用户安装）
PrivilegesRequired=lowest
OutputDir=installer
OutputBaseFilename=SYVision2025_Setup_v{#MyAppVersion}
SetupIconFile=assets\sy_vision_app_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
; 设置安装程序图标和横幅
WizardImageFile=assets\installer_banner.bmp
WizardSmallImageFile=assets\installer_icon.bmp

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; 主程序文件
Source: "dist\SYVision2025\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; 注意: 不要在任何共享系统文件上使用"Flags: ignoreversion"

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\_internal\assets\sy_vision_app_icon.ico"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\_internal\assets\sy_vision_app_icon.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\_internal\assets\sy_vision_app_icon.ico"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}\config"
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\captures"
Type: filesandordirs; Name: "{app}\recipes"

[Code]
// 检查是否已安装旧版本
function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

// 检查是否已安装
function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

// 卸载旧版本
function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

// 安装前检查
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

// 自定义安装页面消息
procedure InitializeWizard;
begin
  WizardForm.WelcomeLabel1.Caption := '欢迎使用 SY Vision 2025 安装向导';
  WizardForm.WelcomeLabel2.Caption := '这将在您的计算机上安装 SY Vision 2025 专业机器视觉检测系统。' + #13#13 + 
    '建议您在继续之前关闭所有其他应用程序。' + #13#13 + 
    '点击"下一步"继续，或点击"取消"退出安装程序。';
end;

// 检查系统要求
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // 检查Windows版本 (Windows 7及以上)
  if Version.Major < 6 then begin
    MsgBox('此程序需要 Windows 7 或更高版本的操作系统。', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  // 检查是否为64位系统
  if not Is64BitInstallMode then begin
    if MsgBox('检测到您使用的是32位系统。此程序针对64位系统优化，可能无法正常运行。是否继续安装？', 
              mbConfirmation, MB_YESNO) = IDNO then begin
      Result := False;
      Exit;
    end;
  end;
  
  Result := True;
end;
