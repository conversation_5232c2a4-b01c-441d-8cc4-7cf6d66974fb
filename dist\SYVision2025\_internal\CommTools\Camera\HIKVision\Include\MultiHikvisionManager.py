# -*- coding: utf-8 -*-

"""
u6d77u5eb7u76f8u673au7ba1u7406u5668u6a21u5757
u4e25u683cu6309u7167C++u6e90u7801u7ffbu8bd1
"""

from PyQt5.QtCore import QObject, pyqtSignal
import datetime
import json
import os
import time

# u6a21u62df MV_CC_DEVICE_INFO u7ed3u6784u4f53
class MV_CC_DEVICE_INFO:
    def __init__(self):
        self.nMajorVer = 1
        self.nMinorVer = 0
        self.nMacAddrHigh = 0
        self.nMacAddrLow = 0
        self.nTLayerType = 0

# u6a21u62df MV_FRAME_OUT_INFO_EX u7ed3u6784u4f53
class MV_FRAME_OUT_INFO_EX:
    def __init__(self):
        self.nWidth = 0
        self.nHeight = 0
        self.enPixelType = 0
        self.nFrameNum = 0
        self.nDevTimeStampHigh = 0
        self.nDevTimeStampLow = 0
        self.nReserved = [0] * 16

"""
u6d77u5eb7u76f8u673au7ba1u7406u7c7b
u652fu6301u591au76f8u673au540cu65f6u5de5u4f5cuff0cu7528u4e8eu7ba1u7406u6d77u5eb7u76f8u673au8bbeu5907u3001u914du7f6eu53c2u6570u3001u83b7u53d6u56feu50cfu7b49
"""
class MultiHikvisionManager(QObject):
    # u4fe1u53f7
    connectionStatusChanged = pyqtSignal(str, bool)  # u76f8u673aID, u8fdeu63a5u72b6u6001
    errorOccurred = pyqtSignal(str, str)  # u76f8u673aID, u9519u8befu4fe1u606f
    frameGrabbed = pyqtSignal(str, object, int, int, str)  # u76f8u673aID, u56feu50cfu6570u636e, u5bbdu5ea6, u9ad8u5ea6, u683cu5f0f
    
    # u5355u4f8bu6a21u5f0f
    _instance = None
    
    """
    u76f8u673au4fe1u606fu7ed3u6784u4f53
    """
    class CameraInfo:
        def __init__(self):
            self.deviceInfo = MV_CC_DEVICE_INFO()  # u8bbeu5907u4fe1u606f
            self.deviceName = ""            # u8bbeu5907u540du79f0
            self.serialNumber = ""          # u5e8fu5217u53f7
            self.modelName = ""             # u578bu53f7u540du79f0
            self.ipAddress = ""             # IPu5730u5740(GigEu76f8u673a)
            self.isGigE = False              # u662fu5426u4e3aGigEu76f8u673a
            self.isUsb3 = False              # u662fu5426u4e3aUSB3u76f8u673a
            self.deviceIndex = ""            # u8bbeu5907u7d22u5f15
    
    """
    u76f8u673au72b6u6001u7ed3u6784u4f53
    """
    class CameraStatus:
        def __init__(self):
            self.handle = None                  # u76f8u673au53e5u67c4
            self.frameBuffer = None            # u56feu50cfu7f13u51b2u533a
            self.bufferSize = 0                # u7f13u51b2u533au5927u5c0f
            self.isConnected = False           # u662fu5426u5df2u8fdeu63a5
            self.isGrabbing = False            # u662fu5426u6b63u5728u91c7u96c6
            self.frameInfo = MV_FRAME_OUT_INFO_EX()  # u6700u540eu4e00u5e27u56feu50cfu4fe1u606f
            self.isLastGrabSuccess = False     # u6700u540eu4e00u6b21u91c7u96c6u662fu5426u6210u529f
            self.lastError = ""                # u6700u540eu4e00u6b21u9519u8befu4fe1u606f
    
    @staticmethod
    def getInstance():
        if MultiHikvisionManager._instance is None:
            MultiHikvisionManager._instance = MultiHikvisionManager()
        return MultiHikvisionManager._instance
    
    def __init__(self):
        super().__init__()
        self._devices = []  # u8bbeu5907u5217u8868
        self._cameraStatuses = {}  # u76f8u673au72b6u6001u8868
        self._lastError = ""  # u6700u540eu4e00u6b21u9519u8bef
    
    def enumDevices(self):
        """
        u679au4e3eu8bbeu5907
        u8fd4u56deuff1a
            u8bbeu5907u5217u8868
        """
        # u6a21u62dfu76f8u673au8bbeu5907u5217u8868
        self._devices = [
            {"deviceIndex": "0", "deviceName": "GigE Camera 1", "deviceType": "GigE", "serialNumber": "SN001", "ipAddress": "*************"},
            {"deviceIndex": "1", "deviceName": "USB3 Camera 1", "deviceType": "USB3", "serialNumber": "SN002", "ipAddress": ""},
        ]
        return self._devices
    
    def getConnectedCameraIds(self):
        """
        u83b7u53d6u5df2u8fdeu63a5u7684u76f8u673aIDu5217u8868
        u8fd4u56deuff1a
            u5df2u8fdeu63a5u7684u76f8u673aIDu5217u8868
        """
        return list(self._cameraStatuses.keys())
    
    def connectCamera(self, deviceIndex, alias=""):
        """
        u8fdeu63a5u76f8u673a
        u53c2u6570uff1a
            deviceIndex: u8bbeu5907u7d22u5f15
            alias: u522bu540d
        u8fd4u56deuff1a
            u76f8u673aIDu6216u7a7au5b57u7b26u4e32uff08u5931u8d25u65f6uff09
        """
        # u751fu6210u76f8u673aID
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        cameraId = f"CAM_{deviceIndex}_{timestamp}"
        
        # u521bu5efau76f8u673au72b6u6001
        status = self.CameraStatus()
        status.isConnected = True
        status.handle = f"HANDLE_{cameraId}"  # u6a21u62dfu53e5u67c4
        
        # u5b58u50a8u76f8u673au72b6u6001
        self._cameraStatuses[cameraId] = status
        
        # u53d1u9001u8fdeu63a5u72b6u6001u4fe1u53f7
        self.connectionStatusChanged.emit(cameraId, True)
        
        return cameraId
    
    def disconnectCamera(self, cameraId):
        """
        u65adu5f00u76f8u673a
        u53c2u6570uff1a
            cameraId: u76f8u673aID
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        # u5982u679cu6b63u5728u91c7u96c6uff0cu5148u505cu6b62u91c7u96c6
        if self._cameraStatuses[cameraId].isGrabbing:
            self.stopGrabbing(cameraId)
        
        # u5220u9664u76f8u673au72b6u6001
        del self._cameraStatuses[cameraId]
        
        # u53d1u9001u8fdeu63a5u72b6u6001u4fe1u53f7
        self.connectionStatusChanged.emit(cameraId, False)
        
        return True
    
    def startGrabbing(self, cameraId):
        """
        u5f00u59cbu91c7u96c6
        u53c2u6570uff1a
            cameraId: u76f8u673aID
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        if not self._cameraStatuses[cameraId].isConnected:
            self._lastError = "u76f8u673au672au8fdeu63a5"
            return False
        
        # u8bbeu7f6eu91c7u96c6u72b6u6001
        self._cameraStatuses[cameraId].isGrabbing = True
        
        return True
    
    def stopGrabbing(self, cameraId):
        """
        u505cu6b62u91c7u96c6
        u53c2u6570uff1a
            cameraId: u76f8u673aID
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        if not self._cameraStatuses[cameraId].isConnected:
            self._lastError = "u76f8u673au672au8fdeu63a5"
            return False
        
        # u8bbeu7f6eu91c7u96c6u72b6u6001
        self._cameraStatuses[cameraId].isGrabbing = False
        
        return True
    
    def isGrabbing(self, cameraId):
        """
        u662fu5426u6b63u5728u91c7u96c6
        u53c2u6570uff1a
            cameraId: u76f8u673aID
        u8fd4u56deuff1a
            u662fu5426u6b63u5728u91c7u96c6
        """
        if cameraId not in self._cameraStatuses:
            return False
        
        return self._cameraStatuses[cameraId].isGrabbing
    
    def executeCommand(self, cameraId, command):
        """
        u6267u884cu547du4ee4
        u53c2u6570uff1a
            cameraId: u76f8u673aID
            command: u547du4ee4
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        if not self._cameraStatuses[cameraId].isConnected:
            self._lastError = "u76f8u673au672au8fdeu63a5"
            return False
        
        # u5904u7406u8f6fu89e6u53d1u547du4ee4
        if command == "TriggerSoftware":
            # u6a21u62dfu8f6fu89e6u53d1
            # u53d1u9001u4e00u4e2au7a7au5e27
            if self._cameraStatuses[cameraId].isGrabbing:
                # u53d1u9001u5e27u91c7u96c6u4fe1u53f7
                self.frameGrabbed.emit(cameraId, None, 640, 480, "RGB")
            return True
        
        return False
    
    def getLastError(self, cameraId=""):
        """
        u83b7u53d6u6700u540eu4e00u6b21u9519u8bef
        u53c2u6570uff1a
            cameraId: u76f8u673aIDuff08u5982u679cu4e3au7a7auff0cu5219u8fd4u56deu5168u5c40u9519u8befuff09
        u8fd4u56deuff1a
            u9519u8befu4fe1u606f
        """
        if not cameraId:
            return self._lastError
        
        if cameraId in self._cameraStatuses:
            return self._cameraStatuses[cameraId].lastError
        
        return "u76f8u673au4e0du5b58u5728"
    
    def setTriggerMode(self, cameraId, mode):
        """
        u8bbeu7f6eu89e6u53d1u6a21u5f0f
        u53c2u6570uff1a
            cameraId: u76f8u673aID
            mode: u6a21u5f0fuff080=u8fdeu7eedu6a21u5f0f, 1=u8f6fu89e6u53d1u6a21u5f0f, 2=u786cu89e6u53d1u6a21u5f0fuff09
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        if not self._cameraStatuses[cameraId].isConnected:
            self._lastError = "u76f8u673au672au8fdeu63a5"
            return False
        
        # u5728u5b9eu9645u5e94u7528u4e2duff0cu8fd9u91ccu4f1au8c03u7528u6d77u5eb7u76f8u673aSDKu8bbeu7f6eu89e6u53d1u6a21u5f0f
        
        return True
    
    def setExposureTime(self, cameraId, exposureTime):
        """
        u8bbeu7f6eu66ddu5149u65f6u95f4
        u53c2u6570uff1a
            cameraId: u76f8u673aID
            exposureTime: u66ddu5149u65f6u95f4uff08u5faeu79d2uff09
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        if not self._cameraStatuses[cameraId].isConnected:
            self._lastError = "u76f8u673au672au8fdeu63a5"
            return False
        
        # u5728u5b9eu9645u5e94u7528u4e2duff0cu8fd9u91ccu4f1au8c03u7528u6d77u5eb7u76f8u673aSDKu8bbeu7f6eu66ddu5149u65f6u95f4
        
        return True
    
    def setGain(self, cameraId, gain):
        """
        u8bbeu7f6eu589eu76ca
        u53c2u6570uff1a
            cameraId: u76f8u673aID
            gain: u589eu76cau503c
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        if not self._cameraStatuses[cameraId].isConnected:
            self._lastError = "u76f8u673au672au8fdeu63a5"
            return False
        
        # u5728u5b9eu9645u5e94u7528u4e2duff0cu8fd9u91ccu4f1au8c03u7528u6d77u5eb7u76f8u673aSDKu8bbeu7f6eu589eu76ca
        
        return True
    
    def setGamma(self, cameraId, gamma):
        """
        u8bbeu7f6eu4f3du9a6cu503c
        u53c2u6570uff1a
            cameraId: u76f8u673aID
            gamma: u4f3du9a6cu503c
        u8fd4u56deuff1a
            u662fu5426u6210u529f
        """
        if cameraId not in self._cameraStatuses:
            self._lastError = "u76f8u673au4e0du5b58u5728"
            return False
        
        if not self._cameraStatuses[cameraId].isConnected:
            self._lastError = "u76f8u673au672au8fdeu63a5"
            return False
        
        # u5728u5b9eu9645u5e94u7528u4e2duff0cu8fd9u91ccu4f1au8c03u7528u6d77u5eb7u76f8u673aSDKu8bbeu7f6eu4f3du9a6cu503c
        
        return True
