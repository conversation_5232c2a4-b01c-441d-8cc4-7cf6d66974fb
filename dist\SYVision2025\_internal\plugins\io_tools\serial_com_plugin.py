"""SerialCom 流水线插件
提供串口打开/关闭及数据发送能力，主要用于流程中与外部设备通讯。
UI 采用 `serial_com_ui.SerialComFrame`。
"""
from __future__ import annotations
from typing import Dict, Any
from pathlib import Path
import importlib
import threading, time

try:
    import serial
except ImportError:
    serial = None

from plugins.plugin_base import PluginBase


class SerialComPlugin(PluginBase):
    name = "serial_com"
    label = "串口通信"
    category = "IO"

    params: Dict[str, Any] = {
        "port": "",          # COM3
        "baud": 9600,
        "open_on_start": False,
    }

    param_labels = {
        "port": "端口",
        "baud": "波特率",
        "open_on_start": "启动即打开",
    }

    def setup(self, params: Dict[str, Any]):
        self.ser = None
        if serial is None:
            return
        if params.get("open_on_start") and params.get("port"):
            try:
                self.ser = serial.Serial(params["port"], int(params["baud"]), timeout=0.1)
            except Exception:
                self.ser = None

    def process(self, img, ctx):
        # 串口插件一般不对图像做处理，只透传
        ctx[self.name] = {
            "is_open": bool(self.ser and self.ser.is_open),
        }
        return img, ctx

    def close(self):
        if self.ser and self.ser.is_open:
            try: self.ser.close()
            except Exception: pass

    # ---------------- UI ----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change):
        import tkinter as tk
        from tkinter import messagebox
        try:
            FrameCls = importlib.import_module('plugins.ui.io_tools.serial_com_ui').SerialComFrame
        except Exception as e:
            messagebox.showerror('错误', f'加载 UI 失败: {e}')
            return
        win = tk.Toplevel(master); win.title('串口调试')
        frame = FrameCls(win); frame.pack(fill=tk.BOTH, expand=True)
        win.grab_set(); win.transient(master)


_ = SerialComPlugin
