"""Pipeline plugin wrapper for EdgeProcessor."""
from __future__ import annotations
from typing import Dict, Any
import numpy as np

from plugins.plugin_base import PluginBase
from plugins.core.image_process.edge import EdgeProcessor

__all__ = ["Edge"]

class Edge(PluginBase):
    """边缘检测流水线插件 (Canny/Sobel/Laplacian)"""

    name = "edge"
    label = "边缘检测"

    # 默认参数（与 EdgeProcessor 保持字段一致）
    params: Dict[str, Any] = {
        "mode": "canny",       # canny|sobel|laplacian
        "thresh1": 100,
        "thresh2": 200,
        "kernel_size": 3,
        "sobel_dir": "xy",     # x|y|xy
    }

    param_labels = {
        "mode": "模式",
        "thresh1": "阈值1",
        "thresh2": "阈值2",
        "kernel_size": "核大小",
        "sobel_dir": "方向",
    }

    def __init__(self, **params):
        super().__init__(**params)
        self._proc = EdgeProcessor(**self.params)

    # -------------------------------------------------------------
    def setup(self, params: Dict[str, Any]):
        self._proc = EdgeProcessor(**params)

    # -------------------------------------------------------------
    def process(self, img, ctx):
        if img is None:
            return img, ctx
        res = self._proc.process(img)
        ctx[self.name] = {}
        return res["output"], ctx

    # ---------------- UI -----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        """Launch EdgeFrame UI and sync parameters."""
        import importlib, tkinter as tk
        mod = importlib.import_module('plugins.ui.image_process.edge_ui')
        if hasattr(mod, 'EdgeFrame'):
            win = tk.Toplevel(master)
            frame = mod.EdgeFrame(win)
            frame.pack(fill='both', expand=True)

            # 预览图像
            if preview_img is not None and hasattr(frame, 'load_image'):
                try:
                    frame.load_image(preview_img)
                except Exception:
                    pass

            # ------- 写入 UI 参数 -------
            ui_params = {
                'mode': params.get('mode', 'canny'),
                'th1': params.get('thresh1', 100),
                'th2': params.get('thresh2', 200),
                'kernel': params.get('kernel_size', 3),
                'sobel_dir': params.get('sobel_dir', 'xy'),
            }
            frame.set_params(ui_params)

            def _ok():
                p = frame.get_params()
                new_params = {
                    'mode': p['mode'],
                    'thresh1': p['th1'],
                    'thresh2': p['th2'],
                    'kernel_size': p['kernel'],
                    'sobel_dir': p['sobel_dir'],
                }
                on_change(new_params)
                win.destroy()
            tk.Button(win, text='确定', command=_ok).pack(pady=2)
        else:
            from tkinter import messagebox
            messagebox.showerror('错误', '未找到 EdgeFrame')


# instantiate to ensure registration when module is imported
_ = Edge()