{"enabled": true, "cleanup_rules": {"captured_images": {"enabled": true, "keep_days": 7, "max_files": 1000, "max_size_mb": 500, "path": "captured_images"}, "logs": {"enabled": true, "keep_days": 30, "max_files": 50, "max_file_size_mb": 10, "path": "logs"}, "test_outputs": {"enabled": true, "keep_days": 3, "max_files": 100, "path": "test_outputs"}, "cache": {"enabled": true, "keep_hours": 24, "paths": ["__pycache__", "*.pyc", "*.pyo"]}, "temp_configs": {"enabled": true, "keep_days": 1, "path": "recipe_editor/tmp"}}, "schedule": {"startup_cleanup": true, "daily_cleanup": "02:00", "shutdown_cleanup": true, "auto_cleanup_on_low_space": true, "low_space_threshold_gb": 1.0}, "notifications": {"show_cleanup_results": true, "log_cleanup_actions": true}}