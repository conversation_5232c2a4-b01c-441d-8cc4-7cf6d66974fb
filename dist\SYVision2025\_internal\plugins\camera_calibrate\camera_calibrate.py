"""Camera calibration plugin (self-contained).
Provides CalibrateEngine class that handles chessboard calibration, result persistence, and basic utilities.
"""
from __future__ import annotations
import cv2, numpy as np, yaml
from pathlib import Path
from dataclasses import dataclass
from typing import List, Tuple

__all__ = ['CalibrateEngine', 'CalibResult', 'load_calibration']


def _to_list(arr: np.ndarray):
    return arr.astype(float).tolist()


@dataclass
class CalibResult:
    rms: float
    K: np.ndarray  # 3x3
    dist: np.ndarray  # (5,) or (8,)
    img_size: Tuple[int, int]

    def to_yaml(self) -> dict:
        return {
            'rms': float(self.rms),
            'K': _to_list(self.K),
            'dist': _to_list(self.dist),
            'img_size': list(self.img_size),
        }

    @staticmethod
    def from_yaml(d: dict) -> 'CalibResult':
        return CalibResult(
            rms=float(d['rms']),
            K=np.array(d['K'], dtype=float),
            dist=np.array(d['dist'], dtype=float),
            img_size=tuple(int(v) for v in d['img_size']),
        )


class CalibrateEngine:
    """Chessboard calibration helper.

    Parameters
    ----------
    board_size : (cols, rows) inner corners. Default (9,6)
    square_size_mm : float. Square edge length in mm. Default 25.0
    flags : additional cv2.calibrateCamera flags. Some sensible defaults set.
    """

    def __init__(self, board_size: Tuple[int, int] = (9, 6), square_size_mm: float = 25.0, *, flags: int | None = None):
        self.board_size = board_size
        self.square_size = square_size_mm / 1000.0  # metres
        self.flags = flags or (cv2.CALIB_RATIONAL_MODEL | cv2.CALIB_ZERO_TANGENT_DIST)

        self._obj_pts: List[np.ndarray] = []
        self._img_pts: List[np.ndarray] = []
        self._img_size: Tuple[int, int] | None = None
        self.result: CalibResult | None = None

        objp = np.zeros((board_size[0] * board_size[1], 3), np.float32)
        objp[:, :2] = np.mgrid[0:board_size[0], 0:board_size[1]].T.reshape(-1, 2)
        objp *= self.square_size
        self._objp_single = objp

    # ---------------- data collection ----------------
    def add_view(self, img: np.ndarray, *, refine: bool = True, vis: bool = False) -> bool:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim == 3 else img
        # 先尝试更鲁棒的函数 (OpenCV ≥4.5)
        found = False; corners = None
        if hasattr(cv2, 'findChessboardCornersSB'):
            flags = cv2.CALIB_CB_NORMALIZE_IMAGE | cv2.CALIB_CB_EXHAUSTIVE
            found, corners = cv2.findChessboardCornersSB(gray, self.board_size, flags)
        if not found:
            # 回退经典算法，带预处理 flags
            flags = cv2.CALIB_CB_ADAPTIVE_THRESH | cv2.CALIB_CB_NORMALIZE_IMAGE
            found, corners = cv2.findChessboardCorners(gray, self.board_size, flags)
        if not found:
            return False
        if refine:
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
            cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
        self._img_pts.append(corners.reshape(-1, 2))
        self._obj_pts.append(self._objp_single.copy())
        if self._img_size is None:
            self._img_size = (gray.shape[1], gray.shape[0])
        if vis:
            disp = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
            cv2.drawChessboardCorners(disp, self.board_size, corners, found)
            cv2.imshow('pattern', disp)
            cv2.waitKey(1)
        return True

    # ---------------- calibration ----------------
    def calibrate(self) -> Tuple[bool, float]:
        if len(self._img_pts) < 3:
            raise RuntimeError('Need at least 3 valid views')
        ret, K, dist, *_ = cv2.calibrateCamera(
            self._obj_pts,
            self._img_pts,
            self._img_size,
            None,
            None,
            flags=self.flags,
        )
        self.result = CalibResult(ret, K, dist.squeeze(), self._img_size)
        return True, ret

    # ---------------- utilities ----------------
    def undistort(self, img: np.ndarray) -> np.ndarray:
        if self.result is None:
            raise RuntimeError('Not calibrated yet')
        return cv2.undistort(img, self.result.K, self.result.dist)

    def save(self, path: str | Path):
        if self.result is None:
            raise RuntimeError('No calibration result')
        with open(Path(path), 'w', encoding='utf-8') as fp:
            yaml.safe_dump(self.result.to_yaml(), fp, allow_unicode=True)

    # convenience
    @staticmethod
    def load(path: str | Path) -> 'CalibrateEngine':
        res = load_calibration(path)
        eng = CalibrateEngine()
        eng.result = res
        return eng


def load_calibration(path: str | Path) -> CalibResult:
    with open(Path(path), 'r', encoding='utf-8') as fp:
        data = yaml.safe_load(fp)
    return CalibResult.from_yaml(data)


# ---------------- CLI test ----------------
if __name__ == '__main__':
    import argparse, glob, sys
    parser = argparse.ArgumentParser(description='Chessboard calibration CLI')
    parser.add_argument('imgs', nargs='+', help='Image patterns')
    parser.add_argument('--cols', type=int, default=9)
    parser.add_argument('--rows', type=int, default=6)
    parser.add_argument('--square', type=float, default=25.0, help='square mm')
    parser.add_argument('-o', '--out', default='intrinsics.yml')
    args = parser.parse_args()

    patterns = []
    for p in args.imgs:
        patterns.extend(glob.glob(p))
    if not patterns:
        print('No images found'); sys.exit(1)

    engine = CalibrateEngine((args.cols, args.rows), args.square)
    count = 0
    for f in patterns:
        img = cv2.imread(f)
        if img is None:
            continue
        if engine.add_view(img):
            count += 1
    print('Collected', count, 'views')
    engine.calibrate()
    print('RMS:', engine.result.rms)
    engine.save(args.out)
    print('Saved to', args.out)
