# -*- coding: utf-8 -*-
"""直线卡尺测量的核心算法。
提供 `measure_distance` 和 `measure_multi` 函数，用于单线和多线卡尺测量。
"""
from __future__ import annotations
import numpy as np
import cv2
import scipy.ndimage
from typing import Literal, Tuple, Dict, Any

__all__ = [
    'measure_distance',
    'measure_multi',
    'draw_caliper_visualization',
]

def _get_scan_line_points(p0: tuple[float, float], p1: tuple[float, float], width: float, n_lines: int) -> list[tuple[tuple[float, float], tuple[float, float]]]:
    """计算多线卡尺的所有扫描线段的起点和终点。"""
    (x0, y0), (x1, y1) = p0, p1
    dx, dy = x1 - x0, y1 - y0
    length = np.hypot(dx, dy)
    if length < 1e-6:
        return []

    # 卡尺方向的单位向量 (ux, uy)
    ux, uy = dx / length, dy / length
    # 卡尺法线方向的单位向量 (vx, vy)
    vx, vy = -uy, ux

    half_width = width / 2.0
    lines = []
    
    # 确保 n_lines > 1 时，扫描线均匀分布在宽度内
    if n_lines <= 1:
        positions = [0.0] # 只有一条线，在中心
    else:
        positions = np.linspace(-half_width, half_width, n_lines)

    for pos in positions:
        # 计算每条扫描线的起点和终点
        # 这条扫描线是沿着 (p0, p1) 的法线方向平移 pos 距离得到的
        line_p0 = (x0 + pos * vx, y0 + pos * vy)
        line_p1 = (x1 + pos * vx, y1 + pos * vy)
        lines.append((line_p0, line_p1))
            
    return lines


def _calculate_roi_boxes(line_p0: tuple[float, float], line_p1: tuple[float, float], roi_size: float, n_rois: int = 8) -> list:
    """计算扫描线上多个小ROI矩形框，类似参考图片效果"""
    x0, y0 = line_p0
    x1, y1 = line_p1

    # 计算线段长度和方向
    dx = x1 - x0
    dy = y1 - y0
    length = np.hypot(dx, dy)

    if length < 1e-6:
        # 如果线段太短，返回一个小的正方形ROI
        half_size = roi_size / 2
        return [{
            'center': ((x0 + x1) / 2, (y0 + y1) / 2),
            'corners': [
                ((x0 + x1) / 2 - half_size, (y0 + y1) / 2 - half_size),
                ((x0 + x1) / 2 + half_size, (y0 + y1) / 2 - half_size),
                ((x0 + x1) / 2 + half_size, (y0 + y1) / 2 + half_size),
                ((x0 + x1) / 2 - half_size, (y0 + y1) / 2 + half_size)
            ]
        }]

    # 计算单位向量
    ux = dx / length  # 线段方向
    uy = dy / length
    vx = -uy  # 垂直方向
    vy = ux

    # 沿扫描线分布多个小ROI
    roi_boxes = []
    half_roi = roi_size / 2

    # 在扫描线上均匀分布ROI中心点
    for i in range(n_rois):
        t = i / (n_rois - 1) if n_rois > 1 else 0.5
        center_x = x0 + t * dx
        center_y = y0 + t * dy

        # 计算小矩形的四个角点
        corners = [
            (center_x - half_roi * ux - half_roi * vx, center_y - half_roi * uy - half_roi * vy),
            (center_x + half_roi * ux - half_roi * vx, center_y + half_roi * uy - half_roi * vy),
            (center_x + half_roi * ux + half_roi * vx, center_y + half_roi * uy + half_roi * vy),
            (center_x - half_roi * ux + half_roi * vx, center_y - half_roi * uy + half_roi * vy)
        ]

        roi_boxes.append({
            'center': (center_x, center_y),
            'corners': corners
        })

    return roi_boxes


def _calculate_roi_box(line_p0: tuple[float, float], line_p1: tuple[float, float], roi_size: float) -> dict:
    """向后兼容的单个ROI框计算函数"""
    roi_boxes = _calculate_roi_boxes(line_p0, line_p1, roi_size, n_rois=1)
    return roi_boxes[0] if roi_boxes else {}


def measure_distance(image: np.ndarray,
                     p0: tuple[float, float],
                     p1: tuple[float, float],
                     *,
                     stride: float = 0.5,
                     sigma: float = 1.0,
                     polarity: Literal['auto','light_to_dark','dark_to_light']='auto',
                     return_debug: bool = False,
                     subpixel: bool = True,
                     roi_size: float = 8.0) -> Dict[str, Any]:
    """
    在单条线段上测量边缘对之间的距离。

    参数:
    - image: 输入的灰度图 (np.uint8 or np.float32)。
    - p0, p1: 定义采样线段的起点和终点。
    - stride: 采样步距（像素）。
    - sigma: 高斯滤波的 sigma 值（以像素为单位）。
    - return_debug: 是否返回调试信息。

    返回:
    一个字典，包含:
    - 'distance_px': 像素距离 (float)。
    - 'p_rising', 'p_falling': 边缘点的坐标。
    - (可选) 'samples', 'gradient': 一维采样和梯度数据。
    """
    if image.ndim == 3:
        img_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        img_gray = image

    img_gray = img_gray.astype(np.float32)

    (x0, y0), (x1, y1) = p0, p1
    length = np.hypot(x1 - x0, y1 - y0)
    if length < 1e-6:
        return {'error': 'Points are too close'}

    nsamples = int(round(length / stride))
    if nsamples < 3: # 需要至少3个点来计算梯度
        return {'error': 'Not enough samples for gradient calculation'}

    # 生成采样点坐标
    x_coords = np.linspace(x0, x1, nsamples)
    y_coords = np.linspace(y0, y1, nsamples)
    
    # 使用 map_coordinates 进行亚像素插值采样
    samples = scipy.ndimage.map_coordinates(img_gray, [y_coords, x_coords], order=1, mode='nearest')

    if sigma > 0:
        samples = scipy.ndimage.gaussian_filter1d(samples, sigma / stride)

    # 计算梯度
    gradient = np.gradient(samples)

    # 根据极性选择边缘
    if polarity == 'light_to_dark':
        idx_falling = int(np.argmax(-gradient))  # 最大负梯度
        idx_rising = int(np.argmax(gradient))    # 最大正梯度
    elif polarity == 'dark_to_light':
        idx_falling = int(np.argmax(gradient))   # 正梯度视为左边
        idx_rising = int(np.argmax(-gradient))   # 负梯度视为右边
    else:  # auto 根据幅值选取最大正、最大负
        idx_pos = int(np.argmax(gradient))
        idx_neg = int(np.argmin(gradient))
        # 保证 idx_falling 在左、idx_rising 在右（沿采样顺序）
        if idx_pos < idx_neg:
            idx_rising, idx_falling = idx_pos, idx_neg
        else:
            idx_rising, idx_falling = idx_neg, idx_pos

    # 亚像素边缘定位
    if subpixel:
        # 对falling边缘进行亚像素拟合
        if 0 < idx_falling < len(gradient) - 1:
            # 使用抛物线拟合找到亚像素峰值位置
            y1_f, y2_f, y3_f = gradient[idx_falling-1], gradient[idx_falling], gradient[idx_falling+1]
            if y1_f != y3_f:  # 避免除零
                subpix_offset_f = 0.5 * (y1_f - y3_f) / (y1_f - 2*y2_f + y3_f)
                idx_falling_subpix = idx_falling + subpix_offset_f
            else:
                idx_falling_subpix = idx_falling
        else:
            idx_falling_subpix = idx_falling

        # 对rising边缘进行亚像素拟合
        if 0 < idx_rising < len(gradient) - 1:
            y1_r, y2_r, y3_r = gradient[idx_rising-1], gradient[idx_rising], gradient[idx_rising+1]
            if y1_r != y3_r:  # 避免除零
                subpix_offset_r = 0.5 * (y1_r - y3_r) / (y1_r - 2*y2_r + y3_r)
                idx_rising_subpix = idx_rising + subpix_offset_r
            else:
                idx_rising_subpix = idx_rising
        else:
            idx_rising_subpix = idx_rising

        # 将亚像素索引转换回图像坐标
        t_falling = idx_falling_subpix / (nsamples - 1)
        t_rising = idx_rising_subpix / (nsamples - 1)
    else:
        # 将索引转换回图像坐标
        t_falling = idx_falling / (nsamples - 1)
        t_rising = idx_rising / (nsamples - 1)

    p_falling = (x0 * (1 - t_falling) + x1 * t_falling,
                 y0 * (1 - t_falling) + y1 * t_falling)
    p_rising = (x0 * (1 - t_rising) + x1 * t_rising,
                y0 * (1 - t_rising) + y1 * t_rising)

    distance_px = np.hypot(p_rising[0] - p_falling[0], p_rising[1] - p_falling[1])

    result: Dict[str, Any] = {
        'distance_px': float(distance_px),
        'p_rising': p_rising,
        'p_falling': p_falling,
        'polarity': polarity,
    }
    if return_debug:
        result.update({'samples': samples, 'gradient': gradient})
    return result


def measure_multi(image: np.ndarray,
                  p0: tuple[float, float],
                  p1: tuple[float, float],
                  *,
                  width: float = 20.0,
                  n_lines: int = 10,
                  stride: float = 0.5,
                  sigma: float = 1.0,
                  polarity: Literal['auto','light_to_dark','dark_to_light']='auto',
                  return_debug: bool = False,
                  roi_size: float = 8.0,
                  subpixel: bool = True) -> Dict[str, Any]:
    """
    多线卡尺，在 p0-p1 定义的矩形区域内进行测量，通过线拟合提高鲁棒性。

    参数:
    - p0, p1: 定义卡尺的中心线和方向。
    - width: 卡尺的宽度（矩形的短边）。
    - n_lines: 扫描线的数量。
    - 其他参数同 `measure_distance`。

    返回:
    一个字典，包含:
    - 'distance_px': 最终拟合计算出的距离。
    - (可选) 'edge_pts1', 'edge_pts2': 找到的所有边缘点。
    - (可选) 'fitted_line1', 'fitted_line2': 拟合出的两条直线。
    """
    if image.ndim == 3:
        img_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        img_gray = image

    scan_lines = _get_scan_line_points(p0, p1, width, n_lines)
    if not scan_lines:
        return {'error': 'Failed to generate scan lines'}

    edge_pts_rising = []
    edge_pts_falling = []
    roi_boxes = []  # 存储ROI框信息

    for line_p0, line_p1 in scan_lines:
        # 计算每条扫描线的多个小ROI框
        line_roi_boxes = _calculate_roi_boxes(line_p0, line_p1, roi_size, n_rois=8)
        roi_boxes.extend(line_roi_boxes)

        res = measure_distance(img_gray, line_p0, line_p1, stride=stride, sigma=sigma, polarity=polarity, subpixel=subpixel, roi_size=roi_size)
        if 'error' not in res:
            edge_pts_rising.append(res['p_rising'])
            edge_pts_falling.append(res['p_falling'])

    if len(edge_pts_rising) < 2 or len(edge_pts_falling) < 2:
        return {'error': 'Not enough valid edge points found to fit lines'}

    # 对找到的边缘点集进行直线拟合
    pts_rising = np.array(edge_pts_rising, dtype=np.float32).reshape(-1, 1, 2)
    pts_falling = np.array(edge_pts_falling, dtype=np.float32).reshape(-1, 1, 2)

    line_rising = cv2.fitLine(pts_rising, cv2.DIST_L2, 0, 0.01, 0.01)
    line_falling = cv2.fitLine(pts_falling, cv2.DIST_L2, 0, 0.01, 0.01)

    # 计算两条拟合出的平行线之间的距离
    # 从一条线上取一个点，计算它到另一条线的距离
    vx, vy, x0, y0 = line_rising.flatten()
    p_on_rising_line = np.array([x0, y0])
    
    vx2, vy2, x0_2, y0_2 = line_falling.flatten()
    p_on_falling_line = np.array([x0_2, y0_2])
    line_dir_falling = np.array([vx2, vy2])

    # 使用向量投影计算距离
    vec = p_on_rising_line - p_on_falling_line
    dist = np.abs(np.cross(line_dir_falling, vec))

    result = {'distance_px': float(dist)}
    if return_debug:
        result.update({
            'edge_pts1': edge_pts_rising,
            'edge_pts2': edge_pts_falling,
            'fitted_line1': line_rising,
            'fitted_line2': line_falling,
            'scan_lines': scan_lines,
            'roi_boxes': roi_boxes,  # 添加ROI框信息
        })
    return result


def draw_caliper_visualization(image: np.ndarray, result: Dict[str, Any]) -> np.ndarray:
    """
    绘制卡尺测量的可视化效果，包括ROI框、边缘点和拟合直线

    参数:
    - image: 原始图像
    - result: measure_multi返回的结果（需要包含调试信息）

    返回:
    - 绘制了可视化效果的图像
    """
    if 'roi_boxes' not in result:
        return image.copy()

    # 转换为BGR格式以便绘制彩色
    if image.ndim == 2:
        vis_img = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
    else:
        vis_img = image.copy()

    # 1. 绘制ROI框（蓝绿色矩形）
    roi_color = (255, 255, 0)  # 青色 (BGR格式)
    for roi_box in result['roi_boxes']:
        corners = roi_box['corners']
        # 绘制矩形框
        pts = np.array(corners, dtype=np.int32).reshape((-1, 1, 2))
        cv2.polylines(vis_img, [pts], True, roi_color, 1)

    # 2. 绘制边缘点（红色十字）
    cross_color = (0, 0, 255)  # 红色
    cross_size = 3

    if 'edge_pts1' in result:
        for pt in result['edge_pts1']:
            x, y = int(pt[0]), int(pt[1])
            # 绘制十字
            cv2.line(vis_img, (x-cross_size, y), (x+cross_size, y), cross_color, 1)
            cv2.line(vis_img, (x, y-cross_size), (x, y+cross_size), cross_color, 1)

    if 'edge_pts2' in result:
        for pt in result['edge_pts2']:
            x, y = int(pt[0]), int(pt[1])
            # 绘制十字
            cv2.line(vis_img, (x-cross_size, y), (x+cross_size, y), cross_color, 1)
            cv2.line(vis_img, (x, y-cross_size), (x, y+cross_size), cross_color, 1)

    # 3. 绘制拟合直线（绿色）
    line_color = (0, 255, 0)  # 绿色
    line_thickness = 2

    if 'fitted_line1' in result and 'fitted_line2' in result:
        h, w = vis_img.shape[:2]

        # 绘制第一条拟合直线
        vx1, vy1, x01, y01 = result['fitted_line1'].flatten()
        # 计算直线在图像边界的交点
        t1 = max(-x01/vx1 if vx1 != 0 else float('inf'),
                 -y01/vy1 if vy1 != 0 else float('inf'),
                 (w-x01)/vx1 if vx1 != 0 else float('inf'),
                 (h-y01)/vy1 if vy1 != 0 else float('inf'))
        t2 = min((w-x01)/vx1 if vx1 != 0 else float('inf'),
                 (h-y01)/vy1 if vy1 != 0 else float('inf'),
                 -x01/vx1 if vx1 != 0 else float('inf'),
                 -y01/vy1 if vy1 != 0 else float('inf'))

        if abs(t1) < 1000 and abs(t2) < 1000:  # 避免过大的值
            pt1 = (int(x01 + vx1 * t1), int(y01 + vy1 * t1))
            pt2 = (int(x01 + vx1 * t2), int(y01 + vy1 * t2))
            cv2.line(vis_img, pt1, pt2, line_color, line_thickness)

        # 绘制第二条拟合直线
        vx2, vy2, x02, y02 = result['fitted_line2'].flatten()
        t1 = max(-x02/vx2 if vx2 != 0 else float('inf'),
                 -y02/vy2 if vy2 != 0 else float('inf'),
                 (w-x02)/vx2 if vx2 != 0 else float('inf'),
                 (h-y02)/vy2 if vy2 != 0 else float('inf'))
        t2 = min((w-x02)/vx2 if vx2 != 0 else float('inf'),
                 (h-y02)/vy2 if vy2 != 0 else float('inf'),
                 -x02/vx2 if vx2 != 0 else float('inf'),
                 -y02/vy2 if vy2 != 0 else float('inf'))

        if abs(t1) < 1000 and abs(t2) < 1000:
            pt1 = (int(x02 + vx2 * t1), int(y02 + vy2 * t1))
            pt2 = (int(x02 + vx2 * t2), int(y02 + vy2 * t2))
            cv2.line(vis_img, pt1, pt2, line_color, line_thickness)

    return vis_img
