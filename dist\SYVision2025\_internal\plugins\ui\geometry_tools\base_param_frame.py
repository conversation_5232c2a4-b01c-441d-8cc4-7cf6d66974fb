"""BaseParamFrame: 参数面板基类

将 Tkinter 变量与 dict 互转，供各测量工具参数面板继承。"""
from __future__ import annotations
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any


class BaseParamFrame(ttk.Frame):
    """所有参数面板的基类，封装 Tk 变量 ⇄ 字典 的同步逻辑。"""

    def __init__(self, master: tk.Widget, params: Dict[str, Any] | None = None):
        super().__init__(master)
        self._vars: Dict[str, tk.Variable] = {}
        self._build()  # 由子类实现具体 UI
        if params:
            self.load_params(params)

    # ---------------- 子类必须实现 ----------------
    def _build(self):
        raise NotImplementedError

    # ---------------- 通用方法 ----------------
    def load_params(self, params: Dict[str, Any]):
        """把 dict 数据写入 Tk 变量"""
        for k, v in params.items():
            var = self._vars.get(k)
            if var is None:
                continue
            if isinstance(var, tk.BooleanVar):
                var.set(bool(v))
            else:
                var.set(v)

    def get_params(self) -> Dict[str, Any]:
        """读取当前 Tk 变量为 dict"""
        data: Dict[str, Any] = {}
        for k, var in self._vars.items():
            if isinstance(var, tk.BooleanVar):
                data[k] = bool(var.get())
            elif isinstance(var, (tk.IntVar, tk.DoubleVar)):
                data[k] = var.get()
            else:
                data[k] = var.get()
        return data
