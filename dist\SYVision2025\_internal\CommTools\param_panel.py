"""ParamPanel - 封装右侧相机参数表单及滚动区域

后续 CameraManagerTk 通过 panel.get_params() / panel.set_params(CameraParams)
来与 UI 交互，避免在主窗口里管理大量Tk变量。
"""
from __future__ import annotations

import tkinter as tk
from tkinter import ttk
from typing import Dict

from core.camera_params import CameraParams

__all__ = ["ParamPanel"]


class ParamPanel(tk.Frame):
    def __init__(self, master: tk.Misc, *, width: int = 185, **kwargs):
        super().__init__(master, width=width, **kwargs)
        self.param_vars: Dict[str, tk.Variable] = {}
        self._build_ui()

    # -------------------- public API --------------------
    def get_params(self) -> CameraParams:
        """从控件读取数据，返回 CameraParams 实例"""
        data = {k: v.get() for k, v in self.param_vars.items()}
        return CameraParams.from_dict(data)

    def set_params(self, params: CameraParams | Dict):
        """把 CameraParams 或 dict 数据写回控件"""
        if isinstance(params, dict):
            params = CameraParams.from_dict(params)
        mapping = params.to_dict()
        for k, var in self.param_vars.items():
            if k in mapping:
                var.set(mapping[k])

    # -------------------- internal helpers --------------------
    def _build_ui(self):
        # 使用 Canvas + Frame 支持滚动
        canvas = tk.Canvas(self, borderwidth=0, highlightthickness=0, bg="#f8f8f8")
        vsb = ttk.Scrollbar(self, orient="vertical", command=canvas.yview)
        canvas.configure(yscrollcommand=vsb.set)
        vsb.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        inner = tk.Frame(canvas, bg="#f8f8f8")
        canvas.create_window((0, 0), window=inner, anchor="nw")

        def _on_frame_configure(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        inner.bind("<Configure>", _on_frame_configure)
        self._populate_form(inner)

    def _populate_form(self, parent: tk.Frame):
        row = 0
        def add_label(text):
            nonlocal row
            tk.Label(parent, text=text, anchor="w", bg="#f8f8f8").grid(row=row, column=0, sticky="w", pady=2)
        def add_widget(widget):
            nonlocal row
            widget.grid(row=row, column=1, sticky="we", pady=2, padx=(4,0))
            row += 1

        # —— 曝光时间 ——
        self.param_vars['ExposureTime'] = tk.DoubleVar(value=10000.0)
        add_label('曝光(us):')
        sp_exp = tk.Spinbox(parent, from_=1, to=1000000, increment=100, textvariable=self.param_vars['ExposureTime'], width=10)
        add_widget(sp_exp)

        # —— 增益 ——
        self.param_vars['Gain'] = tk.DoubleVar(value=0.0)
        add_label('增益:')
        sp_gain = tk.Spinbox(parent, from_=0, to=24, increment=0.1, textvariable=self.param_vars['Gain'], width=10)
        add_widget(sp_gain)

        # —— 自动增益 ——
        self.param_vars['GainAuto'] = tk.BooleanVar(value=True)
        add_label('自动增益:')
        cb_gain_auto = ttk.Checkbutton(parent, variable=self.param_vars['GainAuto'])
        add_widget(cb_gain_auto)

        # —— 采集模式 ——
        self.param_vars['AcquisitionMode'] = tk.StringVar(value='连续采集')
        add_label('采集模式:')
        cb_mode = ttk.Combobox(parent, values=['连续采集', '单帧', '多帧'], state='readonly', width=12, textvariable=self.param_vars['AcquisitionMode'])
        add_widget(cb_mode)

        # —— 触发源 ——
        self.param_vars['TriggerSource'] = tk.StringVar(value='')
        add_label('触发源:')
        cb_trig = ttk.Combobox(parent, values=['', '软件', '线路0', '线路1', '线路2', '线路3'], state='readonly', width=12, textvariable=self.param_vars['TriggerSource'])
        add_widget(cb_trig)

        # —— 触发沿 ——
        self.param_vars['TriggerActivation'] = tk.StringVar(value='Rising')
        add_label('触发沿:')
        cb_act = ttk.Combobox(parent, values=['Rising', 'Falling'], state='readonly', width=12, textvariable=self.param_vars['TriggerActivation'])
        add_widget(cb_act)

        # —— 触发延迟 ——
        self.param_vars['TriggerDelay'] = tk.DoubleVar(value=0.0)
        add_label('触发延时(us):')
        sp_delay = tk.Spinbox(parent, from_=0, to=1000000, increment=10, textvariable=self.param_vars['TriggerDelay'], width=10)
        add_widget(sp_delay)

        # 让第二列自动扩展
        parent.grid_columnconfigure(1, weight=1)
