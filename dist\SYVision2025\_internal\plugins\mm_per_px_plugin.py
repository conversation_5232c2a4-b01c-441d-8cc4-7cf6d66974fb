"""像素比例 (mm_per_px) 标定插件

提供交互界面计算棋盘格像素比例，结果写入 `params['mm_per_px']`，
运行时注入到 `ctx['mm_per_px']` 供下游插件使用，并同步回主 pipeline.yaml。
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, Any, Optional
import importlib
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk

from plugins.plugin_base import PluginBase
from plugins.ui.geometry_tools._recipe_sync import update_mm_in_recipe  # 复用现有写回函数

# 导入纯算法脚本
try:
    calc_mod = importlib.import_module("scripts.calc_mm_per_px")
    calc_mm_per_px = getattr(calc_mod, "calc_mm_per_px")
except Exception as _e:  # pragma: no cover
    def calc_mm_per_px(*_a, **_kw):
        raise RuntimeError("calc_mm_per_px 脚本未找到，请检查 scripts 目录")

__all__ = ["MmPerPxPlugin"]


class MmPerPxPlugin(PluginBase):
    """像素比例标定插件 (mm/px)"""

    name = "mm_per_px_calib"
    label = "像素比例标定"
    category = "标定工具"

    # 类变量：存储当前流程图像
    _current_pipeline_image = None

    @classmethod
    def set_pipeline_image(cls, image):
        """供其他插件调用，设置流程图像"""
        cls._current_pipeline_image = image.copy() if image is not None else None
        print(f"[CALIB] 外部设置流程图像: {image.shape if image is not None else 'None'}")
        print(f"[CALIB] 当前存储的流程图像: {cls._current_pipeline_image.shape if cls._current_pipeline_image is not None else 'None'}")

    # 默认参数
    params: Dict[str, Any] = {
        "mm_per_px": 0.0,
        "calibration_method": "chessboard",  # 标定方法：chessboard, standard_product, multi_validation

        # 棋盘格标定参数（保持兼容）
        "square_size_mm": 1.0,
        "cols": 11,
        "rows": 8,
        "img_path": "",  # 棋盘格图片

        # 标准产品标定参数（新增）
        "product_length_mm": 25.0,
        "product_width_mm": 15.0,
        "product_img_path": "",
        "product_name": "标准零件",

        # 多重验证参数（新增）
        "use_multi_validation": True,
        "min_samples": 3,
        "max_deviation_percent": 2.0,
        "confidence_threshold": 0.95,

        # 高级参数
        "use_subpixel": True,
        "edge_threshold": 50,
        "min_contour_area": 1000,
    }

    param_labels = {
        "mm_per_px": "mm/px",
        "calibration_method": "标定方法",

        # 棋盘格相关
        "square_size_mm": "单格尺寸(mm)",
        "cols": "列数",
        "rows": "行数",
        "img_path": "棋盘格图片",

        # 标准产品相关
        "product_length_mm": "产品长度(mm)",
        "product_width_mm": "产品宽度(mm)",
        "product_img_path": "产品图片",
        "product_name": "产品名称",

        # 验证相关
        "use_multi_validation": "多重验证",
        "min_samples": "最少样本数",
        "max_deviation_percent": "最大偏差(%)",
        "confidence_threshold": "置信度阈值",

        # 高级参数
        "use_subpixel": "亚像素精度",
        "edge_threshold": "边缘阈值",
        "min_contour_area": "最小轮廓面积",
    }

    # -------------------------------------------------- Pipeline 生命周期
    def setup(self, params: Dict[str, Any]):  # type: ignore[override]
        # 尝试从工位配置加载参数
        enhanced_params = self._load_workstation_config(params)

        self._mm_per_px = float(enhanced_params.get("mm_per_px", 0.0))
        self._calibration_method = enhanced_params.get("calibration_method", "chessboard")
        self._use_subpixel = enhanced_params.get("use_subpixel", True)

        # 标准产品标定参数（仅用于显示和记录）
        self._product_length_mm = float(enhanced_params.get("product_length_mm", 25.0))
        self._product_width_mm = float(enhanced_params.get("product_width_mm", 15.0))
        self._product_name = enhanced_params.get("product_name", "标准零件")

        # 存储标定参数供后续使用
        self._calibration_params = {
            'method': self._calibration_method,
            'mm_per_px': self._mm_per_px,
            'subpixel': self._use_subpixel,
            'confidence': enhanced_params.get("confidence_threshold", 0.95),
            'product_dimensions': {
                'length_mm': self._product_length_mm,
                'width_mm': self._product_width_mm,
                'name': self._product_name
            }
        }

        print(f"[CONFIG] 像素标定插件已加载配置: mm_per_px={self._mm_per_px:.6f}, 方法={self._calibration_method}")

    def _load_workstation_config(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """从工位配置加载参数，与其他插件保持一致"""
        try:
            import yaml
            from pathlib import Path
            import os

            # 方法1: 尝试从环境变量获取当前工位路径
            workstation_path = os.environ.get('CURRENT_WORKSTATION_PATH')
            if workstation_path:
                config_file = Path(workstation_path) / "mm_per_px_calib.yml"
                if config_file.exists():
                    print(f"[CONFIG] 从环境变量找到工位配置: {config_file}")
                    config_data = yaml.safe_load(config_file.read_text(encoding="utf-8"))
                    if config_data:
                        merged_params = base_params.copy()
                        merged_params.update(config_data)
                        return merged_params

            # 方法2: 尝试从当前工作目录找到工位配置文件
            cwd = Path.cwd()
            config_file = cwd / "mm_per_px_calib.yml"
            if config_file.exists():
                print(f"[CONFIG] 从当前目录找到工位配置: {config_file}")
                config_data = yaml.safe_load(config_file.read_text(encoding="utf-8"))
                if config_data:
                    merged_params = base_params.copy()
                    merged_params.update(config_data)
                    return merged_params

            # 方法3: 尝试从全局配置目录加载默认配置
            global_config_dir = Path(__file__).resolve().parents[1] / 'configs' / 'mm_per_px_calib'
            default_config_file = global_config_dir / 'default.yml'
            if default_config_file.exists():
                print(f"[CONFIG] 从全局配置加载默认配置: {default_config_file}")
                config_data = yaml.safe_load(default_config_file.read_text(encoding="utf-8"))
                if config_data:
                    merged_params = base_params.copy()
                    merged_params.update(config_data)
                    return merged_params

            print(f"[CONFIG] 未找到工位配置，使用基础参数")
            return base_params

        except Exception as e:
            print(f"[ERROR] 加载工位配置失败: {e}")
            return base_params

    def process(self, img, ctx):  # type: ignore[override]
        """
        处理图像并注入像素比例到上下文

        重要：此插件应该放在图像处理插件之后，接收处理后的图像
        功能：将预先标定好的像素比例注入到上下文，供下游测量插件使用
        """

        # 保存当前流程图像供标定界面使用
        MmPerPxPlugin._current_pipeline_image = img.copy() if img is not None else None

        # 添加像素比例信息到上下文
        if self._mm_per_px > 0:
            ctx["mm_per_px"] = self._mm_per_px

            # 添加详细的标定信息
            ctx["pixel_calibration"] = {
                "mm_per_px": self._mm_per_px,
                "method": self._calibration_method,
                "subpixel_enabled": self._use_subpixel,
                "calibration_params": self._calibration_params,
                "processed_image_used": True,  # 标记使用了处理后图像
                "pipeline_stage": "post_processing"  # 标记在图像处理后运行
            }

            print(f"[CALIB] 注入像素比例到上下文: {self._mm_per_px:.6f} mm/px")
        else:
            print("[CALIB] 警告：未设置像素比例，测量结果将只显示像素值")
            ctx["calibration_warning"] = "未设置像素比例"

        return img, ctx

    # -------------------------------------------------- 参数对话框
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, *, recipe_path: Optional[str | Path] = None, **_extra):
        """打开增强版像素比例标定对话框。"""

        dlg = tk.Toplevel(master)
        dlg.title("像素比例标定 - 增强版")
        dlg.geometry("900x700")
        dlg.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(dlg)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧：参数控制区域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 右侧：图像预览区域
        right_frame = ttk.LabelFrame(main_frame, text="图像预览", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建标签页控件（在左侧）
        notebook = ttk.Notebook(left_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # ---------------- 数据绑定 ----------------
        # 棋盘格标定变量
        img_path_var = tk.StringVar(value=params.get("img_path", ""))
        square_var = tk.DoubleVar(value=params.get("square_size_mm", 1.0))
        cols_var = tk.IntVar(value=params.get("cols", 11))
        rows_var = tk.IntVar(value=params.get("rows", 8))

        # 标准产品标定变量
        product_img_var = tk.StringVar(value=params.get("product_img_path", ""))
        product_length_var = tk.DoubleVar(value=params.get("product_length_mm", 25.0))
        product_width_var = tk.DoubleVar(value=params.get("product_width_mm", 15.0))
        product_name_var = tk.StringVar(value=params.get("product_name", "标准零件"))

        # 通用结果变量
        result_var = tk.StringVar(value=str(params.get("mm_per_px", "-")))
        method_var = tk.StringVar(value=params.get("calibration_method", "chessboard"))

        # 图像来源变量
        image_source_var = tk.StringVar(value="file")  # "file" 或 "pipeline"

        # 预览相关变量
        preview_image = None
        preview_label = None

        # ---------------- UI 构建 ----------------

        # 标签页1：棋盘格标定（保持兼容）
        chessboard_frame = ttk.Frame(notebook)
        notebook.add(chessboard_frame, text="棋盘格标定")
        MmPerPxPlugin._build_chessboard_tab(chessboard_frame, img_path_var, square_var, cols_var, rows_var)

        # 标签页2：标准产品标定（新增）
        product_frame = ttk.Frame(notebook)
        notebook.add(product_frame, text="标准产品标定")
        MmPerPxPlugin._build_product_tab(product_frame, product_img_var, product_length_var, product_width_var, product_name_var, image_source_var)

        # 构建图像预览区域
        preview_canvas, status_label = MmPerPxPlugin._build_preview_area(right_frame, image_source_var)

        # 结果显示区域（在左侧底部）
        result_frame = ttk.LabelFrame(left_frame, text="标定结果", padding=10)
        result_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(result_frame, text="mm/px:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(result_frame, textvariable=result_var, font=("Consolas", 12, "bold"), foreground="blue").grid(row=0, column=1, sticky=tk.W, padx=10)

        ttk.Label(result_frame, text="标定方法:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        ttk.Label(result_frame, textvariable=method_var, font=("Arial", 10)).grid(row=0, column=3, sticky=tk.W, padx=10)

        # ---------------- 事件 ----------------
        def calc_chessboard_action():
            """棋盘格标定"""
            img_p = img_path_var.get()
            if not img_p:
                messagebox.showwarning("提示", "请先选择棋盘格图片")
                return
            try:
                mm_val, px_per_sq = calc_mm_per_px(img_p, square_var.get(), (cols_var.get(), rows_var.get()))
                result_var.set(f"{mm_val:.6f}")
                method_var.set("chessboard")
                messagebox.showinfo("完成", f"平均每格像素: {px_per_sq:.2f}px\nmm_per_px = {mm_val:.6f} mm/px")
            except Exception as e:
                messagebox.showerror("错误", str(e))

        def calc_product_action():
            """标准产品标定"""
            try:
                # 根据图像来源选择图像
                source = image_source_var.get()

                if source == "pipeline":
                    # 使用流程图像
                    if MmPerPxPlugin._current_pipeline_image is None:
                        messagebox.showwarning("提示", "流程图像未就绪，请先运行流程或选择文件图像")
                        return

                    image_input = MmPerPxPlugin._current_pipeline_image
                    print(f"[INFO] 使用流程图像进行标定，图像尺寸: {image_input.shape}")

                else:
                    # 使用文件图像
                    img_p = product_img_var.get()
                    if not img_p:
                        messagebox.showwarning("提示", "请先选择产品图片")
                        return
                    image_input = img_p
                    print(f"[INFO] 使用文件图像进行标定: {img_p}")

                # 首先获取图像信息
                from plugins.enhanced_calibration import get_image_info, calibrate_from_clean_binary_image

                img_info = get_image_info(image_input)
                if not img_info['success']:
                    messagebox.showerror("错误", img_info['error'])
                    return

                # 显示图像信息
                img_props = img_info['image_properties']
                print(f"[INFO] 图像信息: {img_props['width']}×{img_props['height']} 像素")
                print(f"[INFO] 宽高比: {img_props['aspect_ratio']}, 分辨率等级: {img_info['resolution_category']}")

                # 执行标定
                reference_dims = {
                    'length_mm': product_length_var.get(),
                    'width_mm': product_width_var.get()
                }

                result = calibrate_from_clean_binary_image(image_input, reference_dims)

                if result['success']:
                    mm_val = result['mm_per_px']
                    confidence = result['confidence']
                    result_var.set(f"{mm_val:.6f}")
                    method_var.set("standard_product")

                    # 生成详细报告
                    report_text = f"标定成功！\n\n"
                    report_text += f"mm_per_px = {mm_val:.6f}\n"
                    report_text += f"置信度 = {confidence:.1%}\n"
                    report_text += f"使用特征: {', '.join(result['used_features'])}\n\n"

                    # 添加图像信息
                    report_text += f"图像信息:\n"
                    report_text += f"分辨率: {img_props['width']}×{img_props['height']} 像素\n"
                    report_text += f"宽高比: {img_props['aspect_ratio']}\n"

                    # 添加检测详情
                    if 'features_detected' in result:
                        features = result['features_detected']
                        report_text += f"\n检测详情:\n"
                        report_text += f"产品长度: {features['total_length_px']:.2f} 像素\n"
                        report_text += f"产品宽度: {features['total_width_px']:.2f} 像素\n"
                        report_text += f"旋转角度: {features['rotation_angle']:.1f}°\n"
                        report_text += f"二值化方法: {features.get('binary_method', 'unknown')}\n"

                    # 添加精度评估
                    if 'uncertainty' in result:
                        uncertainty = result['uncertainty']
                        error_per_100px = uncertainty['absolute_uncertainty'] * 100 * 1000  # 转换为微米
                        report_text += f"\n精度评估:\n"
                        report_text += f"相对不确定度: ±{uncertainty['relative_uncertainty_percent']:.3f}%\n"
                        report_text += f"100像素测量误差: ±{error_per_100px:.2f}μm\n"

                    # 显示详细报告
                    MmPerPxPlugin._show_calibration_report(dlg, "标定报告", report_text)

                else:
                    error_msg = result['error']
                    if 'image_info' in result:
                        img_info_result = result['image_info']
                        error_msg += f"\n\n图像信息:\n分辨率: {img_info_result['width_px']}×{img_info_result['height_px']}"
                    messagebox.showerror("标定失败", error_msg)

            except Exception as e:
                import traceback
                error_detail = traceback.format_exc()
                print(f"[ERROR] 标定异常: {error_detail}")
                messagebox.showerror("错误", f"标定过程出错: {str(e)}")

        def save_action():
            val = result_var.get()
            if not val or val == "-":
                messagebox.showwarning("提示", "请先计算 mm_per_px")
                return
            mm_val = float(val)

            # 保存所有参数
            new_params = {
                "mm_per_px": mm_val,
                "calibration_method": method_var.get(),

                # 棋盘格参数
                "square_size_mm": square_var.get(),
                "cols": cols_var.get(),
                "rows": rows_var.get(),
                "img_path": img_path_var.get(),

                # 标准产品参数
                "product_length_mm": product_length_var.get(),
                "product_width_mm": product_width_var.get(),
                "product_img_path": product_img_var.get(),
                "product_name": product_name_var.get(),
            }

            # 使用统一的配置保存机制
            MmPerPxPlugin._save_config_unified(new_params, recipe_path)

            on_change(new_params)
            dlg.destroy()

        # 按钮区域（在左侧底部）
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(btn_frame, text="棋盘格标定", command=lambda: calc_chessboard_action()).pack(side=tk.LEFT, padx=4)
        ttk.Button(btn_frame, text="产品标定", command=lambda: calc_product_action()).pack(side=tk.LEFT, padx=4)
        ttk.Button(btn_frame, text="刷新预览", command=lambda: MmPerPxPlugin._refresh_preview(image_source_var, preview_canvas, status_label)).pack(side=tk.LEFT, padx=4)
        ttk.Button(btn_frame, text="保存", command=save_action).pack(side=tk.LEFT, padx=4)
        ttk.Button(btn_frame, text="取消", command=dlg.destroy).pack(side=tk.LEFT, padx=4)

        dlg.grab_set()
        dlg.transient(master)

    @staticmethod
    def _build_chessboard_tab(parent, img_path_var, square_var, cols_var, rows_var):
        """构建棋盘格标定标签页"""
        frame = ttk.LabelFrame(parent, text="棋盘格标定参数", padding=10)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(frame, text="棋盘格图片:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=img_path_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=5)
        ttk.Button(frame, text="浏览...", command=lambda: _choose_img(img_path_var)).grid(row=0, column=2, padx=5)

        ttk.Label(frame, text="单格尺寸(mm):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=square_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5)

        ttk.Label(frame, text="内角点 列×行:").grid(row=2, column=0, sticky=tk.W, pady=5)
        pattern_frame = ttk.Frame(frame)
        pattern_frame.grid(row=2, column=1, sticky=tk.W, padx=5)
        ttk.Entry(pattern_frame, textvariable=cols_var, width=4).pack(side=tk.LEFT)
        ttk.Label(pattern_frame, text="×").pack(side=tk.LEFT, padx=2)
        ttk.Entry(pattern_frame, textvariable=rows_var, width=4).pack(side=tk.LEFT)

        # 说明文本
        info_text = "使用标准棋盘格进行标定，需要准确的单格尺寸。\n推荐使用高质量打印的棋盘格标定板。"
        ttk.Label(frame, text=info_text, font=("Arial", 9), foreground="gray").grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=10)

    @staticmethod
    def _build_product_tab(parent, product_img_var, product_length_var, product_width_var, product_name_var, image_source_var):
        """构建标准产品标定标签页"""
        frame = ttk.LabelFrame(parent, text="标准产品标定参数", padding=10)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(frame, text="产品名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=product_name_var, width=20).grid(row=0, column=1, sticky=tk.W, padx=5)

        # 图像来源选择
        ttk.Label(frame, text="图像来源:").grid(row=1, column=0, sticky=tk.W, pady=5)
        source_frame = ttk.Frame(frame)
        source_frame.grid(row=1, column=1, sticky=tk.W, padx=5)
        ttk.Radiobutton(source_frame, text="文件", variable=image_source_var, value="file").pack(side=tk.LEFT)
        ttk.Radiobutton(source_frame, text="流程图像", variable=image_source_var, value="pipeline").pack(side=tk.LEFT, padx=(10, 0))

        ttk.Label(frame, text="产品图片:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=product_img_var, width=40).grid(row=2, column=1, sticky=tk.W, padx=5)
        ttk.Button(frame, text="浏览...", command=lambda: _choose_product_img(product_img_var)).grid(row=2, column=2, padx=5)

        ttk.Label(frame, text="产品长度(mm):").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=product_length_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=5)

        ttk.Label(frame, text="产品宽度(mm):").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(frame, textvariable=product_width_var, width=10).grid(row=4, column=1, sticky=tk.W, padx=5)

        # 图像信息按钮
        ttk.Button(frame, text="查看图像信息",
                  command=lambda: MmPerPxPlugin._show_image_info_enhanced(image_source_var.get(), product_img_var.get())).grid(row=5, column=0, sticky=tk.W, pady=5)

        # 图像验证按钮
        ttk.Button(frame, text="验证图像一致性",
                  command=lambda: MmPerPxPlugin._validate_image_consistency_enhanced(image_source_var.get(), product_img_var.get())).grid(row=5, column=1, sticky=tk.W, padx=5)

        # 说明文本
        info_text = "使用标准产品进行标定，需要在其他精密设备上\n测得的准确尺寸。可选择文件图像或当前流程图像。"
        ttk.Label(frame, text=info_text, font=("Arial", 9), foreground="gray").grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=10)

    @staticmethod
    def _build_preview_area(parent, image_source_var):
        """构建图像预览区域"""
        # 图像来源状态
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        status_label = ttk.Label(status_frame, text="图像状态: 未加载")
        status_label.pack(side=tk.LEFT)

        # 图像预览画布
        canvas_frame = ttk.Frame(parent)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        preview_canvas = tk.Canvas(canvas_frame, bg='gray90', width=400, height=300)
        preview_canvas.pack(fill=tk.BOTH, expand=True)

        # 刷新按钮（需要在canvas创建后定义）
        refresh_btn = ttk.Button(status_frame, text="刷新预览",
                               command=lambda: MmPerPxPlugin._refresh_preview(image_source_var, preview_canvas, status_label))
        refresh_btn.pack(side=tk.RIGHT)

        # 图像来源变化时自动刷新
        def on_source_change(*args):
            MmPerPxPlugin._refresh_preview(image_source_var, preview_canvas, status_label)

        image_source_var.trace('w', on_source_change)

        # 初始显示
        MmPerPxPlugin._refresh_preview(image_source_var, preview_canvas, status_label)

        return preview_canvas, status_label

    @staticmethod
    def _refresh_preview(image_source_var, canvas, status_label):
        """刷新图像预览"""
        try:
            source = image_source_var.get()
            print(f"[PREVIEW] 刷新预览，图像来源: {source}")
            print(f"[PREVIEW] 当前类变量状态: {MmPerPxPlugin._current_pipeline_image is not None}")

            if source == "pipeline":
                # 使用流程图像
                if MmPerPxPlugin._current_pipeline_image is not None:
                    img = MmPerPxPlugin._current_pipeline_image
                    img_shape = img.shape
                    status_label.config(text=f"图像状态: 流程图像已加载 ({img_shape[1]}×{img_shape[0]})")
                    print(f"[PREVIEW] 显示流程图像，尺寸: {img_shape}")
                else:
                    canvas.delete("all")
                    canvas.create_text(200, 150, text="流程图像未就绪\n请先运行流程",
                                     font=("Arial", 12), fill="gray")
                    status_label.config(text="图像状态: 流程图像未就绪")
                    print("[PREVIEW] 流程图像未就绪，类变量为None")
                    return
            else:
                # 使用文件图像（暂时显示提示）
                canvas.delete("all")
                canvas.create_text(200, 150, text="请在左侧选择图像文件\n然后点击刷新预览",
                                 font=("Arial", 12), fill="gray")
                status_label.config(text="图像状态: 等待选择文件")
                print("[PREVIEW] 等待选择文件图像")
                return

            # 显示图像
            MmPerPxPlugin._display_image_on_canvas(img, canvas)
            print(f"[PREVIEW] 图像显示完成")

        except Exception as e:
            canvas.delete("all")
            canvas.create_text(200, 150, text=f"预览失败:\n{str(e)}",
                             font=("Arial", 10), fill="red")
            status_label.config(text="图像状态: 预览失败")
            print(f"[PREVIEW] 预览失败: {e}")

    @staticmethod
    def _display_image_on_canvas(img, canvas):
        """在画布上显示图像"""
        if img is None:
            return

        # 获取画布尺寸
        canvas.update()
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        # 转换图像格式
        if len(img.shape) == 3:
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        else:
            img_rgb = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)

        # 计算缩放比例
        img_height, img_width = img_rgb.shape[:2]
        scale_w = canvas_width / img_width
        scale_h = canvas_height / img_height
        scale = min(scale_w, scale_h, 1.0)  # 不放大，只缩小

        # 缩放图像
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        img_resized = cv2.resize(img_rgb, (new_width, new_height))

        # 转换为PIL图像
        pil_img = Image.fromarray(img_resized)
        photo = ImageTk.PhotoImage(pil_img)

        # 清除画布并显示图像
        canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        canvas.create_image(x, y, anchor=tk.NW, image=photo)

        # 保持引用防止垃圾回收
        canvas.image = photo

    @staticmethod
    def _show_calibration_report(parent, title: str, report_text: str):
        """显示标定报告对话框"""
        report_dlg = tk.Toplevel(parent)
        report_dlg.title(title)
        report_dlg.geometry("500x600")
        report_dlg.resizable(True, True)

        # 创建滚动文本框
        frame = ttk.Frame(report_dlg, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)

        # 文本框和滚动条
        text_frame = ttk.Frame(frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 插入报告内容
        text_widget.insert(tk.END, report_text)
        text_widget.config(state=tk.DISABLED)  # 只读

        # 按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(btn_frame, text="复制到剪贴板",
                  command=lambda: MmPerPxPlugin._copy_to_clipboard(report_text)).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=report_dlg.destroy).pack(side=tk.RIGHT, padx=5)

        report_dlg.transient(parent)
        report_dlg.grab_set()

    @staticmethod
    def _copy_to_clipboard(text: str):
        """复制文本到剪贴板"""
        try:
            import tkinter as tk
            root = tk._default_root
            root.clipboard_clear()
            root.clipboard_append(text)
            messagebox.showinfo("提示", "报告已复制到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")

    @staticmethod
    def _show_image_info(image_path: str):
        """显示图像信息"""
        if not image_path:
            messagebox.showwarning("提示", "请先选择图像")
            return

        try:
            from plugins.enhanced_calibration import get_image_info

            info = get_image_info(image_path)

            if not info['success']:
                messagebox.showerror("错误", info['error'])
                return

            # 构建信息文本
            file_info = info['file_info']
            img_props = info['image_properties']

            info_text = f"图像文件信息\n{'='*40}\n\n"
            info_text += f"文件路径: {file_info['path']}\n"
            info_text += f"文件大小: {file_info['size_mb']} MB\n"
            info_text += f"文件格式: {file_info['extension']}\n\n"

            info_text += f"图像属性\n{'='*40}\n\n"
            info_text += f"分辨率: {img_props['width']} × {img_props['height']} 像素\n"
            info_text += f"总像素数: {img_props['total_pixels']:,}\n"
            info_text += f"宽高比: {img_props['aspect_ratio']}\n"
            info_text += f"颜色通道: {img_props['channels']}\n"
            info_text += f"数据类型: {img_props['dtype']}\n"
            info_text += f"分辨率等级: {info['resolution_category']}\n\n"

            # 分辨率建议
            if info['resolution_category'] == 'low_resolution':
                info_text += "⚠️ 建议: 图像分辨率较低，可能影响标定精度\n"
            elif info['resolution_category'] == 'high_resolution':
                info_text += "✅ 建议: 图像分辨率较高，有利于精确标定\n"
            else:
                info_text += "✅ 建议: 图像分辨率适中，适合标定使用\n"

            MmPerPxPlugin._show_calibration_report(None, "图像信息", info_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取图像信息失败: {str(e)}")

    @staticmethod
    def _show_image_info_enhanced(image_source: str, image_path: str):
        """显示图像信息（增强版，支持流程图像）"""
        try:
            if image_source == "pipeline":
                # 使用流程图像
                if MmPerPxPlugin._current_pipeline_image is None:
                    messagebox.showwarning("提示", "流程图像未就绪，请先运行流程")
                    return

                image_input = MmPerPxPlugin._current_pipeline_image
                print(f"[INFO] 查看流程图像信息，图像尺寸: {image_input.shape}")

                # 构建流程图像信息
                img_shape = image_input.shape
                info_text = f"流程图像信息\n{'='*40}\n\n"
                info_text += f"图像来源: 流程处理结果\n"
                info_text += f"分辨率: {img_shape[1]} × {img_shape[0]} 像素\n"
                info_text += f"总像素数: {img_shape[0] * img_shape[1]:,}\n"
                info_text += f"宽高比: {img_shape[1] / img_shape[0]:.3f}\n"
                info_text += f"颜色通道: {img_shape[2] if len(img_shape) > 2 else 1}\n"
                info_text += f"数据类型: {image_input.dtype}\n"

                # 分辨率评估
                total_pixels = img_shape[0] * img_shape[1]
                if total_pixels < 100000:
                    info_text += f"分辨率等级: 低分辨率\n"
                    info_text += "⚠️ 建议: 图像分辨率较低，可能影响标定精度\n"
                elif total_pixels > 1000000:
                    info_text += f"分辨率等级: 高分辨率\n"
                    info_text += "✅ 建议: 图像分辨率较高，有利于精确标定\n"
                else:
                    info_text += f"分辨率等级: 中等分辨率\n"
                    info_text += "✅ 建议: 图像分辨率适中，适合标定使用\n"

                MmPerPxPlugin._show_calibration_report(None, "流程图像信息", info_text)

            else:
                # 使用文件图像，调用原有方法
                MmPerPxPlugin._show_image_info(image_path)

        except Exception as e:
            messagebox.showerror("错误", f"获取图像信息失败: {str(e)}")

    @staticmethod
    def _validate_image_consistency_enhanced(image_source: str, image_path: str):
        """验证图像一致性（增强版，支持流程图像）"""
        try:
            if image_source == "pipeline":
                # 流程图像模式
                if MmPerPxPlugin._current_pipeline_image is None:
                    messagebox.showwarning("提示", "流程图像未就绪，请先运行流程")
                    return

                # 让用户选择标定时的参考图像进行比较
                reference_image_path = filedialog.askopenfilename(
                    title="选择标定时使用的参考图像进行比较",
                    filetypes=[("Image", "*.png;*.jpg;*.bmp;*.tif")]
                )

                if not reference_image_path:
                    return

                # 比较流程图像与参考图像
                current_img = MmPerPxPlugin._current_pipeline_image
                import cv2
                reference_img = cv2.imread(reference_image_path)

                if reference_img is None:
                    messagebox.showerror("错误", "无法加载参考图像")
                    return

                # 尺寸比较
                curr_shape = current_img.shape[:2]
                ref_shape = reference_img.shape[:2]

                info_text = f"图像一致性验证\n{'='*40}\n\n"
                info_text += f"当前流程图像: {curr_shape[1]} × {curr_shape[0]} 像素\n"
                info_text += f"参考标定图像: {ref_shape[1]} × {ref_shape[0]} 像素\n\n"

                if curr_shape == ref_shape:
                    info_text += "✅ 图像尺寸一致，标定参数可以直接使用\n"
                else:
                    info_text += "⚠️ 图像尺寸不一致，建议重新标定\n"
                    scale_x = curr_shape[1] / ref_shape[1]
                    scale_y = curr_shape[0] / ref_shape[0]
                    info_text += f"尺寸比例: X轴 {scale_x:.3f}, Y轴 {scale_y:.3f}\n"

                MmPerPxPlugin._show_calibration_report(None, "一致性验证结果", info_text)

            else:
                # 文件图像模式，调用原有方法
                MmPerPxPlugin._validate_image_consistency(image_path)

        except Exception as e:
            messagebox.showerror("错误", f"验证图像一致性失败: {str(e)}")

    @staticmethod
    def _save_config_unified(params: Dict[str, Any], recipe_path: Optional[str] = None):
        """统一的配置保存机制，与其他插件保持一致"""
        try:
            import yaml
            from pathlib import Path

            # 1. 保存到全局配置目录
            global_config_dir = Path(__file__).resolve().parents[1] / 'configs' / 'mm_per_px_calib'
            global_config_dir.mkdir(parents=True, exist_ok=True)
            global_config_file = global_config_dir / 'default.yml'

            with open(global_config_file, 'w', encoding='utf-8') as f:
                yaml.safe_dump(params, f, default_flow_style=False, allow_unicode=True)
            print(f"[CONFIG] 全局配置已保存到: {global_config_file}")

            # 2. 如果有工位路径，同时保存到工位配置目录
            if recipe_path:
                recipe_path = Path(recipe_path)
                if recipe_path.is_file():
                    # 从pipeline.yaml路径推导工位目录
                    ws_dir = recipe_path.parent
                    ws_config_file = ws_dir / 'mm_per_px_calib.yml'

                    with open(ws_config_file, 'w', encoding='utf-8') as f:
                        yaml.safe_dump(params, f, default_flow_style=False, allow_unicode=True)
                    print(f"[CONFIG] 工位配置已保存到: {ws_config_file}")

                    # 3. 更新工位的pipeline.yaml中的mm_per_px值
                    MmPerPxPlugin._update_pipeline_mm_per_px(recipe_path, params.get("mm_per_px", 0.0))

            messagebox.showinfo("保存成功", f"配置已保存\n全局配置: {global_config_file}\n工位配置: {ws_config_file if recipe_path else '无'}")

        except Exception as e:
            print(f"[ERROR] 保存配置失败: {e}")
            messagebox.showerror("保存失败", f"配置保存失败: {str(e)}")

    @staticmethod
    def _update_pipeline_mm_per_px(pipeline_path: Path, mm_per_px: float):
        """更新pipeline.yaml中的mm_per_px值"""
        try:
            import yaml

            if not pipeline_path.exists():
                print(f"[WARNING] Pipeline文件不存在: {pipeline_path}")
                return

            # 读取pipeline配置
            pipeline_data = yaml.safe_load(pipeline_path.read_text(encoding="utf-8")) or []

            # 查找并更新mm_per_px_calib插件的参数
            updated = False
            for step in pipeline_data:
                if isinstance(step, dict) and "mm_per_px_calib" in step:
                    step["mm_per_px_calib"]["mm_per_px"] = mm_per_px
                    updated = True
                    print(f"[CONFIG] 已更新pipeline中的mm_per_px: {mm_per_px}")
                    break

            if updated:
                # 写回pipeline文件
                with open(pipeline_path, 'w', encoding='utf-8') as f:
                    yaml.safe_dump(pipeline_data, f, default_flow_style=False, allow_unicode=True)
                print(f"[CONFIG] Pipeline配置已更新: {pipeline_path}")
            else:
                print(f"[WARNING] Pipeline中未找到mm_per_px_calib插件")

        except Exception as e:
            print(f"[ERROR] 更新pipeline配置失败: {e}")

    @staticmethod
    def _validate_image_consistency(calibration_image_path: str):
        """验证图像一致性"""
        if not calibration_image_path:
            messagebox.showwarning("提示", "请先选择标定图像")
            return

        # 让用户选择当前采集的图像进行比较
        current_image_path = filedialog.askopenfilename(
            title="选择当前采集的图像进行比较",
            filetypes=[("Image", "*.png;*.jpg;*.bmp;*.tif")]
        )

        if not current_image_path:
            return

        try:
            from plugins.enhanced_calibration import validate_image_consistency

            result = validate_image_consistency(calibration_image_path, current_image_path)

            if not result['success']:
                messagebox.showerror("错误", result['error'])
                return

            # 构建验证报告
            calib_img = result['calibration_image']
            current_img = result['current_image']
            comparison = result['comparison']

            report_text = f"图像一致性验证报告\n{'='*50}\n\n"

            report_text += f"标定图像:\n"
            report_text += f"  分辨率: {calib_img['width']} × {calib_img['height']}\n"
            report_text += f"  宽高比: {calib_img['aspect_ratio']}\n"
            report_text += f"  总像素: {calib_img['total_pixels']:,}\n\n"

            report_text += f"当前图像:\n"
            report_text += f"  分辨率: {current_img['width']} × {current_img['height']}\n"
            report_text += f"  宽高比: {current_img['aspect_ratio']}\n"
            report_text += f"  总像素: {current_img['total_pixels']:,}\n\n"

            report_text += f"差异分析:\n"
            report_text += f"  宽度差异: {comparison['width_diff']} 像素\n"
            report_text += f"  高度差异: {comparison['height_diff']} 像素\n"
            report_text += f"  宽高比差异: {comparison['aspect_ratio_diff_percent']:.3f}%\n"
            report_text += f"  像素数差异: {comparison['pixel_count_diff']:,}\n\n"

            report_text += f"一致性评估: {result['consistency']}\n"
            report_text += f"建议: {result['recommendation']}\n"

            # 根据一致性显示不同的图标
            if result['consistency'] == 'perfect':
                report_text = "✅ " + report_text
            elif result['consistency'] == 'acceptable':
                report_text = "⚠️ " + report_text
            else:
                report_text = "❌ " + report_text

            MmPerPxPlugin._show_calibration_report(None, "图像一致性验证", report_text)

        except Exception as e:
            messagebox.showerror("错误", f"图像一致性验证失败: {str(e)}")


# ---------------- 工具函数 ----------------

def _choose_img(var: tk.StringVar):
    """选择棋盘格图片"""
    path = filedialog.askopenfilename(title="选择棋盘格图片",
                                      filetypes=[("Image", "*.png;*.jpg;*.bmp;*.tif")])
    if path:
        var.set(path)

def _choose_product_img(var: tk.StringVar):
    """选择产品图片"""
    path = filedialog.askopenfilename(title="选择产品图片",
                                      filetypes=[("Image", "*.png;*.jpg;*.bmp;*.tif")])
    if path:
        var.set(path)


# 注册实例，供插件系统自动发现
_ = MmPerPxPlugin()
