"""Tkinter UI for EdgeProcessor (Canny/Sobel/Laplacian)."""
from __future__ import annotations
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2, numpy as np, yaml, pathlib, sys

ROOT_DIR = pathlib.Path(__file__).resolve().parents[3]
if str(ROOT_DIR) not in sys.path:
    sys.path.insert(0, str(ROOT_DIR))

CONFIG_DIR = ROOT_DIR / 'configs' / 'edge'
CONFIG_DIR.mkdir(parents=True, exist_ok=True)

from plugins.core.image_process.edge import EdgeProcessor

class EdgeFrame(tk.Frame):
    MODES = {
        'Canny': 'canny',
        'Sobel': 'sobel',
        'Laplacian': 'laplacian',
    }
    SOBEL_DIRS = {'X': 'x', 'Y': 'y', 'XY': 'xy'}

    def __init__(self, master: tk.Widget|None=None):
        super().__init__(master)
        if isinstance(master, (tk.Tk, tk.Toplevel)):
            master.geometry('1200x600')
        self.proc = EdgeProcessor()
        self.img_src: np.ndarray|None = None
        self.img_out: np.ndarray|None = None
        self.preview_max = 450
        self.roi_rect=None
        self.poly_pts=[]
        self._build_ui()

    # 公共接口，供插件自动加载上游图片
    def load_image(self, img: 'np.ndarray'):
        self.img_src = img.copy()
        self._show_image(self.canvas_src, self.img_src, is_src=True)
        self._auto_run()

    # ----------------- UI -----------------
    def _build_ui(self):
        param = tk.LabelFrame(self, text='参数', padx=4, pady=4)
        param.grid(row=0,column=0,sticky='nsw')

        ttk.Label(param, text='模式').grid(row=0,column=0,sticky='e')
        default_mode = next(k for k,v in self.MODES.items() if v==self.proc.mode)
        self.mode_var=tk.StringVar(value=default_mode)
        ttk.OptionMenu(param, self.mode_var, default_mode, *self.MODES.keys(),
                       command=lambda *_: self._on_mode_change()).grid(row=0,column=1)

        # Canny thresholds
        self.th1=tk.IntVar(value=self.proc.thresh1)
        self.th2=tk.IntVar(value=self.proc.thresh2)
        ttk.Label(param,text='阈值1').grid(row=1,column=0,sticky='e')
        ttk.Spinbox(param,from_=0,to=500,textvariable=self.th1,width=6,command=self._auto_run).grid(row=1,column=1)
        ttk.Label(param,text='阈值2').grid(row=2,column=0,sticky='e')
        ttk.Spinbox(param,from_=0,to=500,textvariable=self.th2,width=6,command=self._auto_run).grid(row=2,column=1)

        # Sobel/Lap kernel
        ttk.Label(param,text='Kernel').grid(row=3,column=0,sticky='e')
        self.ks=tk.IntVar(value=self.proc.kernel_size)
        ttk.Spinbox(param,from_=1,to=7,increment=2,textvariable=self.ks,width=6,command=self._auto_run).grid(row=3,column=1)

        ttk.Label(param,text='Sobel方向').grid(row=4,column=0,sticky='e')
        default_dir='XY'
        self.dir_var=tk.StringVar(value=default_dir)
        ttk.OptionMenu(param,self.dir_var,default_dir,*self.SOBEL_DIRS.keys(),command=lambda *_: self._auto_run()).grid(row=4,column=1)

        # Buttons
        ttk.Button(param,text='载入图片',command=self._load_image).grid(row=5,column=0,pady=4)
        ttk.Button(param,text='执行',command=self._run).grid(row=5,column=1,pady=4)
        ttk.Button(param,text='保存配置',command=self._save_cfg).grid(row=6,column=0,pady=4)
        ttk.Button(param,text='加载配置',command=self._load_cfg).grid(row=6,column=1,pady=4)
        ttk.Button(param,text='保存结果',command=self._save_result).grid(row=7,column=0,columnspan=2,sticky='we',pady=(0,4))

        # Zoom slider
        self.zoom_var=tk.DoubleVar(value=1.0)
        ttk.Label(param,text='缩放').grid(row=8,column=0,sticky='e')
        ttk.Scale(param,from_=0.25,to=2.0,orient='horizontal',variable=self.zoom_var,
                  command=lambda e:self._refresh_images(),length=120).grid(row=8,column=1,columnspan=2,sticky='w')

        # ROI options
        self.roi_mode=tk.StringVar(value='rect')
        ttk.Label(param,text='ROI模式').grid(row=9,column=0,sticky='e')
        ttk.OptionMenu(param,self.roi_mode,'rect','rect','poly').grid(row=9,column=1)
        ttk.Button(param,text='完成多边形',command=self._poly_finish).grid(row=9,column=2)

        # Info label
        self.info_var=tk.StringVar(value='坐标:(-, -) 值:-,-,- 分辨率:-x-')
        ttk.Label(self,textvariable=self.info_var).grid(row=1,column=0,columnspan=2,sticky='w')

        # canvases
        self.canvas_src=tk.Canvas(self,bg='#202020',width=self.preview_max,height=self.preview_max)
        self.canvas_res=tk.Canvas(self,bg='#202020',width=self.preview_max,height=self.preview_max)
        self.canvas_src.grid(row=0,column=1)
        self.canvas_res.grid(row=0,column=2)
        self.canvas_src.bind('<MouseWheel>',self._on_wheel)
        self.canvas_src.bind('<Button-1>',self._roi_start)
        self.canvas_src.bind('<B1-Motion>',self._roi_drag)
        self.canvas_src.bind('<ButtonRelease-1>',self._roi_end)
        self.canvas_src.bind('<Button-3>',self._poly_add)

        self._on_mode_change()

    # ----------------- helpers -----------------
    def _on_mode_change(self):
        # 仅在模式切换时自动执行，无需操作 Tk 内部变量
        self._auto_run()

    def _load_image(self):
        p=filedialog.askopenfilename(filetypes=[('Images','*.bmp *.png *.jpg *.jpeg *.tif *.tiff')])
        if not p: return
        img=cv2.imdecode(np.fromfile(p,dtype=np.uint8),cv2.IMREAD_COLOR)
        if img is None:
            messagebox.showerror('错误','无法读取图片');return
        self.img_src=img
        self._refresh_images()

    def _run(self):
        if self.img_src is None:
            messagebox.showinfo('提示','请先加载图像');return
        # update proc params
        self.proc.mode=self.MODES[self.mode_var.get()]
        self.proc.thresh1=self.th1.get()
        self.proc.thresh2=self.th2.get()
        self.proc.kernel_size=self.ks.get()
        self.proc.sobel_dir=self.SOBEL_DIRS[self.dir_var.get()]

        img_in=self.img_src
        if self.roi_mode.get()=='poly' and self.poly_pts:
            mask=np.zeros(img_in.shape[:2],np.uint8)
            cv2.fillPoly(mask,np.array([self.poly_pts],np.int32),255)
            proc_in=cv2.bitwise_and(img_in,img_in,mask=mask)
            dst=self.proc.process(proc_in)['output']
            dst=cv2.bitwise_and(dst,dst,mask=mask)
        elif self.roi_rect:
            x,y,w,h=self.roi_rect
            if w<=0 or h<=0:
                messagebox.showwarning('警告','ROI尺寸无效');return
            crop=img_in[y:y+h,x:x+w]
            if crop.size==0:
                messagebox.showwarning('警告','ROI裁剪为空');return
            dst=self.proc.process(crop)['output']
        else:
            dst=self.proc.process(img_in)['output']
        self.img_out = dst
        self._show_image(self.canvas_res,cv2.cvtColor(dst,cv2.COLOR_GRAY2BGR))
        self._show_image(self.canvas_src,self.img_src,is_src=True)

    def _auto_run(self):
        if self.img_src is not None:
            self._run()

    # ROI handlers
    def _roi_start(self, event):
        if self.roi_mode.get()=='rect':
            self._roi_start_pt=(event.x,event.y)
            if hasattr(self,'_roi_canvas_id') and self._roi_canvas_id:
                self.canvas_src.delete(self._roi_canvas_id)
                self._roi_canvas_id=None
        else:
            z=getattr(self,'_src_zoom', self.zoom_var.get())
            self.poly_pts.append((int(event.x/z), int(event.y/z)))
            self._refresh_images()

    def _roi_drag(self, event):
        if self.roi_mode.get()=='rect' and hasattr(self,'_roi_start_pt'):
            x0,y0=self._roi_start_pt
            x1,y1=event.x,event.y
            if hasattr(self,'_roi_canvas_id') and self._roi_canvas_id:
                self.canvas_src.coords(self._roi_canvas_id,x0,y0,x1,y1)
            else:
                self._roi_canvas_id=self.canvas_src.create_rectangle(x0,y0,x1,y1, outline='lime', dash=(3,2))

    def _roi_end(self, event):
        if self.roi_mode.get()=='rect' and hasattr(self,'_roi_start_pt'):
            x0,y0=self._roi_start_pt
            x1,y1=event.x,event.y
            del self._roi_start_pt
            z=getattr(self,'_src_zoom', self.zoom_var.get())
            xi,yi=int(min(x0,x1)/z), int(min(y0,y1)/z)
            wi,hi=int(abs(x1-x0)/z), int(abs(y1-y0)/z)
            self.roi_rect=(xi,yi,wi,hi)
            self.poly_pts=[]
            self._auto_run()

    def _poly_add(self, event):
        if self.roi_mode.get()=='poly':
            z=getattr(self,'_src_zoom', self.zoom_var.get())
            self.poly_pts.append((int(event.x/z), int(event.y/z)))
            self._refresh_images()

    def _poly_finish(self):
        self._auto_run()

    # Zoom
    def _on_wheel(self, event):
        step=0.1 if event.delta>0 else -0.1
        new_val=max(0.25, min(2.0, self.zoom_var.get()+step))
        self.zoom_var.set(round(new_val,2))
        self._refresh_images()

    # Image display
    def _refresh_images(self):
        self._show_image(self.canvas_src, self.img_src, is_src=True)
        self._show_image(self.canvas_res, None)

    def _show_image(self, canvas: tk.Canvas, img_bgr: np.ndarray|None, *, is_src=False):
        if img_bgr is None:
            canvas.delete('all');return
        z=self.zoom_var.get() if is_src else getattr(self,'_src_zoom', self.zoom_var.get())
        h,w=img_bgr.shape[:2]
        disp_w,disp_h=int(w*z), int(h*z)
        if max(disp_w,disp_h)>self.preview_max:
            s=self.preview_max/max(disp_w,disp_h)
            disp_w,disp_h=int(disp_w*s), int(disp_h*s)
            z*=s
        img_disp=cv2.resize(img_bgr,(disp_w,disp_h)) if z!=1 else img_bgr.copy()
        img_rgb=cv2.cvtColor(img_disp, cv2.COLOR_BGR2RGB)
        photo=ImageTk.PhotoImage(Image.fromarray(img_rgb))
        canvas.delete('all')
        canvas.create_image(0,0,anchor='nw',image=photo)
        canvas.image=photo
        if is_src:
            self._src_zoom=z
            canvas.bind('<Motion>', lambda e,zz=z: self._update_info(e,zz))
            self.info_var.set(f'坐标:(-, -) 值:-,-,- 分辨率:{w}x{h}')
        if is_src and self.roi_rect:
            x,y,w1,h1=self.roi_rect
            x0,y0=x*z, y*z
            x1,y1=(x+w1)*z, (y+h1)*z
            canvas.create_rectangle(x0,y0,x1,y1, outline='lime', dash=(3,2))
        if is_src and self.poly_pts:
            disp_pts=[(px*z,py*z) for px,py in self.poly_pts]
            if len(disp_pts)>1:
                canvas.create_line(*sum(disp_pts,()), fill='lime', width=2)
            for px,py in disp_pts:
                canvas.create_oval(px-3,py-3,px+3,py+3, fill='lime')

    def _update_info(self, event, z):
        if self.img_src is None: return
        x=int(event.x/z); y=int(event.y/z)
        h,w=self.img_src.shape[:2]
        if 0<=x<w and 0<=y<h:
            b,g,r=self.img_src[y,x]
            self.info_var.set(f'坐标:({x}, {y}) 值:{r},{g},{b} 分辨率:{w}x{h}')

    # cfg
    def get_params(self):
        return {
            'mode': self.MODES[self.mode_var.get()],
            'th1': self.th1.get(),
            'th2': self.th2.get(),
            'kernel': self.ks.get(),
            'sobel_dir': self.SOBEL_DIRS[self.dir_var.get()],
            'roi': self.roi_rect,
            'poly_pts': self.poly_pts,
        }

    def set_params(self, p:dict):
        self.mode_var.set(next(k for k,v in self.MODES.items() if v==p.get('mode',self.proc.mode)))
        self.th1.set(p.get('th1',self.proc.thresh1))
        self.th2.set(p.get('th2',self.proc.thresh2))
        self.ks.set(p.get('kernel',self.proc.kernel_size))
        self.dir_var.set(next(k for k,v in self.SOBEL_DIRS.items() if v==p.get('sobel_dir',self.proc.sobel_dir)))
        self.roi_rect=p.get('roi')
        self.poly_pts=p.get('poly_pts',[])
        self._auto_run()

    def _save_cfg(self):
        init_file=CONFIG_DIR/'default.yml'
        path=filedialog.asksaveasfilename(initialdir=CONFIG_DIR, initialfile=init_file.name,
                                          defaultextension='.yml', filetypes=[('YAML','*.yml;*.yaml')])
        if not path:return
        with open(path,'w',encoding='utf-8') as f:
            yaml.safe_dump(self.get_params(), f, allow_unicode=True)

    def _load_cfg(self):
        path=filedialog.askopenfilename(initialdir=CONFIG_DIR, filetypes=[('YAML','*.yml;*.yaml')])
        if not path:return
        try:
            with open(path,'r',encoding='utf-8') as f:
                p=yaml.safe_load(f)
            self.set_params(p or {})
        except Exception as e:
            messagebox.showerror('错误', f'加载失败: {e}')

    # ---------------- 保存结果 -----------------
    def _save_result(self):
        if getattr(self,'img_out',None) is None:
            messagebox.showinfo('提示','请先执行处理');return
        path = filedialog.asksaveasfilename(defaultextension='.png', filetypes=[('PNG','*.png'),('JPG','*.jpg')])
        if not path: return
        img = self.img_out
        if img.ndim==2:
            img_bgr = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        else:
            img_bgr = img
        ext = path.split('.')[-1].lower()
        ext = '.jpg' if ext=='jpg' else '.png'
        ok, buf = cv2.imencode(ext, img_bgr)
        if ok:
            buf.tofile(path)
        else:
            messagebox.showerror('错误','保存失败')

if __name__=='__main__':
    root=tk.Tk();root.title('边缘检测测试');EdgeFrame(root).pack();root.mainloop()
