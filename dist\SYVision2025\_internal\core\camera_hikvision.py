import os
import sys
import logging
import numpy as np
from PIL import Image
from .camera_base import CameraBase
from typing import Optional
import json
import threading
import time
from .camera_params import CameraParams

# 直接在文件内定义结构体，彻底避免导入问题
import ctypes
class MVCC_INTVALUE(ctypes.Structure):
    _fields_ = [("nCurValue", ctypes.c_uint32), 
                ("nMax", ctypes.c_uint32),
                ("nMin", ctypes.c_uint32),
                ("nInc", ctypes.c_uint32),
                ("nReserved", ctypes.c_uint32 * 4)]

class MVCC_ENUMVALUE(ctypes.Structure):
    _fields_ = [
        ("nCurValue", ctypes.c_uint32),
        ("nSupportedNum", ctypes.c_uint32),
        ("nSupportValue", ctypes.c_uint32 * 16),
        ("nReserved", ctypes.c_uint32 * 4),
    ]

# 自动添加DLL和SDK路径（只添加一次）
# 检测是否为编译环境
if getattr(sys, 'frozen', False) or hasattr(sys, '_MEIPASS'):
    # 编译后环境，使用打包后的MvImport路径
    if hasattr(sys, '_MEIPASS'):
        SDK_PATH = os.path.join(sys._MEIPASS, 'MvImport')
    else:
        # 获取当前脚本所在目录的MvImport
        current_dir = os.path.dirname(os.path.abspath(__file__))
        SDK_PATH = os.path.join(os.path.dirname(current_dir), 'MvImport')
else:
    # 开发环境，使用原始SDK路径
    SDK_PATH = r'D:\MVS\Development\Samples\Python\MvImport'

print(f"[CameraHikvision] 使用SDK路径: {SDK_PATH}")

if os.path.exists(SDK_PATH):
    if SDK_PATH not in os.environ.get('PATH', ''):
        os.add_dll_directory(SDK_PATH)
    if SDK_PATH not in sys.path:
        sys.path.append(SDK_PATH)
    print(f"[CameraHikvision] SDK路径已添加: {SDK_PATH}")
else:
    print(f"[CameraHikvision] 警告: SDK路径不存在: {SDK_PATH}")

# 导入海康SDK Python封装
try:
    from MvCameraControl_class import *
except ImportError:
    raise ImportError('未找到MvCameraControl_class.py，请检查SDK路径！')

# 初始化日志
logger = logging.getLogger(__name__)
if not logger.hasHandlers():
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# ---------------- 常量定义 ----------------
MV_GIGE_DEVICE = 1
MV_USB_DEVICE = 2
MV_CAM_ACQ_MODE_CONTINUOUS = 2  # AcquisitionMode: Continuous
MV_CAM_ACQ_MODE_SINGLE = 0      # AcquisitionMode: SingleFrame
MV_CAM_ACQ_MODE_MULTI = 1       # AcquisitionMode: MultiFrame

class CameraHikvision(CameraBase):
    """
    海康威视相机驱动实现，集成项目内SDK。
    """

    @staticmethod
    def enum_devices():
        '''
        枚举所有当前连接的物理相机设备，返回设备信息列表。
        '''
        device_list = MV_CC_DEVICE_INFO_LIST()
        ret = MvCamera.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, device_list)
        if ret != 0:
            raise RuntimeError(f'枚举设备失败，错误码: {ret}')
        result = []
        for i in range(device_list.nDeviceNum):
            dev = device_list.pDeviceInfo[i].contents
            info = {}
            if dev.nTLayerType == MV_GIGE_DEVICE:
                info['type'] = 'GIGE'
                ip = '.'.join([str((dev.SpecialInfo.stGigEInfo.nCurrentIp >> (8 * j)) & 0xff) for j in (3, 2, 1, 0)])
                sn = bytes(dev.SpecialInfo.stGigEInfo.chSerialNumber).decode(errors='ignore').strip('\x00')
                info['ip'] = ip
                info['sn'] = sn
            elif dev.nTLayerType == MV_USB_DEVICE:
                info['type'] = 'USB'
                info['ip'] = ''
                sn = bytes(dev.SpecialInfo.stUsb3VInfo.chSerialNumber).decode(errors='ignore').strip('\x00')
                info['sn'] = sn
            else:
                continue
            result.append(info)
        return result

    def __init__(self, camera_id, config):
        super().__init__(camera_id, config)
        self.device = None
        self.opened = False
        self.status = 'init'
        self.grabbing = False  # 当前是否在采集
        # 运行参数缓存
        self.params = {
            'exposure': None,      # us
            'gain': None,          # dB
            'gain_auto': None,     # dB
            'trigger_enabled': False,
            'trigger_source': 'Line0',
            'trigger_activation': 'Rising',
            'trigger_delay': 0.0,
        }
        self._lock = threading.Lock()  # 线程安全
        # 缓存帧尺寸和数据缓冲区
        self._payload_size = 0
        self._width = 0
        self._height = 0
        self._buf = None
        # 像素格式 & 日志节流
        self._pixel_type = None
        self._is_mono8 = False
        self._use_bgr_direct = True
        self._last_no_frame_log = 0.0
        # 抓帧相关配置
        self._grab_timeout_ms_continuous = config.get('grab_timeout_continuous', 1000)
        self._grab_timeout_ms_trigger = config.get('grab_timeout_trigger', 50)
        self._grab_start_time = 0

    # ---------------- 参数持久化 ----------------
    def save_params(self, filepath):
        """将当前相机参数保存到 JSON 文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.params, f, ensure_ascii=False, indent=2)
            logger.info("参数已保存到 %s", filepath)
            return True
        except Exception as e:
            logger.error("保存参数失败: %s", e)
            return False

    def load_params(self, filepath):
        """从 JSON 文件加载参数并立即应用"""
        if not os.path.isfile(filepath):
            logger.warning("参数文件不存在: %s", filepath)
            return False
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.params.update(data)
            logger.info("已加载参数文件: %s", filepath)
            if self.opened:
                self._apply_all_params()
            return True
        except Exception as e:
            logger.error("加载参数失败: %s", e)
            return False

    def _apply_all_params(self):
        """根据 self.params 批量写入相机。假设相机处于打开但未抓取状态"""
        try:
            self.set_exposure(self.params.get('exposure'))
            if self.params.get('gain_auto'):
                self.set_gain_auto(self.params.get('gain_auto'))
            else:
                self.set_gain(self.params.get('gain'))
            self.set_trigger_mode(
                enable=self.params.get('trigger_enabled', False),
                source=self.params.get('trigger_source', 'Line0'),
                activation=self.params.get('trigger_activation', 'Rising'),
                delay=self.params.get('trigger_delay', 0.0))
        except Exception as e:
            logger.warning("批量写入参数时部分失败: %s", e)

    def apply_params(self, params: 'CameraParams'):
        """应用CameraParams对象到相机，转换配置文件参数为内部参数格式"""
        try:
            # 转换AcquisitionMode和TriggerSource为trigger_enabled
            acquisition_mode = params.AcquisitionMode
            trigger_source = params.TriggerSource
            
            # 判断是否启用触发模式
            trigger_enabled = False
            if trigger_source and trigger_source.strip() and trigger_source != "":
                trigger_enabled = True
            elif acquisition_mode and "触发" in acquisition_mode:
                trigger_enabled = True
            elif acquisition_mode and acquisition_mode not in ["连续采集", "连续"]:
                trigger_enabled = True
                
            # 更新内部参数
            self.params.update({
                'exposure': params.ExposureTime,
                'gain': params.Gain,
                'gain_auto': params.GainAuto,
                'trigger_enabled': trigger_enabled,
                'trigger_source': trigger_source or 'Line0',
                'trigger_activation': params.TriggerActivation,
                'trigger_delay': params.TriggerDelay
            })
            
            # 如果相机已打开，立即应用参数
            if self.opened:
                self._apply_all_params()
                
        except Exception as e:
            logger.warning("应用参数失败: %s", e)

    # ---------------- 基础参数设置 ----------------
    def set_exposure(self, exposure_us: Optional[float] = None):
        """设置曝光时间 (单位: 微秒)。None 时不做修改"""
        if exposure_us is None:
            return True
        if not self.opened:
            logger.warning("set_exposure: 相机未打开")
            return False
        was_grabbing = self.grabbing
        if was_grabbing:
            self.stop_grab()
        try:
            with self._lock:
                ret = self.device.MV_CC_SetFloatValue("ExposureTime", float(exposure_us))
            if ret != 0:
                logger.error("设置曝光失败 ret=%s", ret)
                return False
            self.params['exposure'] = exposure_us
            logger.info("曝光设置为 %.1f us", exposure_us)
            return True
        finally:
            if was_grabbing:
                self.start_grab(continuous=not self.params['trigger_enabled'])

    def set_gain(self, gain_db: Optional[float] = None):
        """设置增益 (单位: dB)。None 时跳过"""
        if gain_db is None:
            return True
        if not self.opened:
            logger.warning("set_gain: 相机未打开")
            return False
        was_grabbing = self.grabbing
        if was_grabbing:
            self.stop_grab()
        try:
            with self._lock:
                ret = self.device.MV_CC_SetFloatValue("Gain", float(gain_db))
            if ret != 0:
                logger.error("设置增益失败 ret=%s", ret)
                return False
            self.params['gain'] = gain_db
            logger.info("增益设置为 %.2f dB", gain_db)
            return True
        finally:
            if was_grabbing:
                self.start_grab(continuous=not self.params['trigger_enabled'])

    def set_gain_auto(self, gain_auto: Optional[bool] = None):
        """设置增益自动模式 (单位: bool)。None 时跳过"""
        if gain_auto is None:
            return True
        if not self.opened:
            logger.warning("set_gain_auto: 相机未打开")
            return False
        was_grabbing = self.grabbing
        if was_grabbing:
            self.stop_grab()
        try:
            with self._lock:
                ret = self.device.MV_CC_SetEnumValue("GainAuto", 2 if gain_auto else 1)
            if ret != 0:
                logger.error("设置增益自动模式失败 ret=%s", ret)
                return False
            self.params['gain_auto'] = gain_auto
            logger.info("增益自动模式设置为 %s", gain_auto)
            return True
        finally:
            if was_grabbing:
                self.start_grab(continuous=not self.params['trigger_enabled'])

    # ---------------- 触发相关 ----------------
    # Enum 常量，简化魔法数字
    MV_TRIGGER_MODE_OFF = 0
    MV_TRIGGER_MODE_ON = 1
    MV_TRIGGER_SOURCE_LINE0 = 0
    MV_TRIGGER_SOURCE_SOFTWARE = 7
    MV_TRIGGER_ACTIVATION_RISING = 0
    MV_TRIGGER_ACTIVATION_FALLING = 1

    def set_trigger_mode(self, enable: bool, source: str = 'Line0', activation: str = 'Rising', delay: float = 0.0):
        """配置触发模式。enable=False 为连续采集。"""
        if not self.opened:
            logger.warning("set_trigger_mode: 相机未打开")
            return False

        src_lower = source.lower()
        if src_lower.startswith('line') or '线路' in source:
            source_enum = self.MV_TRIGGER_SOURCE_LINE0
        else:
            source_enum = self.MV_TRIGGER_SOURCE_SOFTWARE
        activation_enum = self.MV_TRIGGER_ACTIVATION_RISING if activation.lower() == 'rising' else self.MV_TRIGGER_ACTIVATION_FALLING

        was_grabbing = self.grabbing
        if was_grabbing:
            self.stop_grab()
        try:
            with self._lock:
                # TriggerMode
                ret = self.device.MV_CC_SetEnumValue("TriggerMode", self.MV_TRIGGER_MODE_ON if enable else self.MV_TRIGGER_MODE_OFF)
            if ret != 0:
                logger.error("设置TriggerMode失败 ret=%s", ret)
                return False
            if enable:
                # Source / Activation / Delay
                with self._lock:
                    self.device.MV_CC_SetEnumValue("TriggerSource", source_enum)
                    self.device.MV_CC_SetEnumValue("TriggerActivation", activation_enum)
                    self.device.MV_CC_SetFloatValue("TriggerDelay", float(delay))
                    # 建议关闭帧率控制
                    self.device.MV_CC_SetBoolValue("AcquisitionFrameRateEnable", False)
                    # AcquisitionMode 根据触发开关选择
                    self.device.MV_CC_SetEnumValue("AcquisitionMode", MV_CAM_ACQ_MODE_CONTINUOUS)
            else:
                # 连续采集恢复帧率控制
                with self._lock:
                    self.device.MV_CC_SetBoolValue("AcquisitionFrameRateEnable", True)
                    # 设置一个合理的帧率，例如10fps
                    self.device.MV_CC_SetFloatValue("AcquisitionFrameRate", 10.0)
                # print(f"DEBUG: 相机 {self.camera_id} 设置连续采集模式，帧率: 10fps")
            # 保存参数
            self.params['trigger_enabled'] = enable
            self.params['trigger_source'] = source
            self.params['trigger_activation'] = activation
            self.params['trigger_delay'] = delay
            logger.info("触发模式已%s", '开启' if enable else '关闭')
            return True
        finally:
            if was_grabbing:
                self.start_grab(continuous=not enable)

    # ---------------- 采集控制 ----------------
    def start_grab(self, continuous: bool = True):
        """开始采集。continuous=True 为自由运行，False 则使用已配置好的硬触发。"""
        if not self.opened:
            logger.warning("start_grab: 相机未打开")
            return False
        if self.grabbing:
            logger.debug("start_grab: 已在采集中")
            return True
        # 根据所需模式调整 TriggerMode
        if continuous:
            # 连续采集，确保关闭触发模式
            self.set_trigger_mode(False)
        else:
            # 硬触发采集，使用缓存的触发参数，不要覆盖用户设置
            self.set_trigger_mode(
                True,
                source=self.params.get('trigger_source', 'Line0'),
                activation=self.params.get('trigger_activation', 'Rising'),
                delay=self.params.get('trigger_delay', 0.0)
            )
        with self._lock:
            ret = self.device.MV_CC_StartGrabbing()
        if ret != 0:
            logger.error("StartGrabbing 失败 ret=%s", ret)
            return False
        self.grabbing = True
        self._grab_start_time = time.time()
        # 等待流稳定，避免刚开始无帧可取
        time.sleep(0.15)
        logger.info("开始%s采集", '连续' if continuous else '硬触发')
        return True

    def stop_grab(self):
        """停止采集"""
        if not self.opened or not self.grabbing:
            return True
        with self._lock:
            ret = self.device.MV_CC_StopGrabbing()
        if ret != 0:
            logger.error("StopGrabbing 失败 ret=%s", ret)
            return False
        self.grabbing = False
        logger.info("停止采集")
        return True

    def trigger_software(self):
        """发送软件触发信号"""
        if not self.opened:
            logger.warning("trigger_software: 相机未打开")
            return False
        if not self.grabbing:
            logger.warning("trigger_software: 相机未开始采集")
            return False
        
        with self._lock:
            ret = self.device.MV_CC_TriggerSoftware()
        if ret != 0:
            logger.error("软件触发失败 ret=%s", ret)
            return False
        
        # print(f"DEBUG: 相机 {self.camera_id} 发送软件触发信号成功")
        return True

    # 兼容外部调用快捷函数
    def start_grab_trigger(self):
        """开启硬触发采集"""
        return self.start_grab(continuous=False)

    # ---------------- 软触发 ----------------
    def start_grab_software(self):
        """配置为软件触发模式并开始采集"""
        # 设置触发模式：启用 + Source=Software
        ok = self.set_trigger_mode(True, source='Software', activation='Rising')
        if not ok:
            return False
        return self.start_grab(continuous=False)

    def software_trigger(self):
        """兼容外部调用的软触发接口，内部调用 trigger_once()."""
        return self.trigger_once()

    def trigger_once(self):
        """在软件触发模式下发送一次触发命令"""
        if not self.opened:
            logger.warning("trigger_once: 相机未打开")
            return False
        if not self.params.get('trigger_enabled') or self.params.get('trigger_source').lower() != 'software':
            logger.warning("trigger_once: 当前未处于软件触发模式")
            return False
        try:
            with self._lock:
                ret = self.device.MV_CC_SetCommandValue("TriggerSoftware")
            if ret != 0:
                logger.error("TriggerSoftware 失败 ret=%s", ret)
                return False
            logger.debug("TriggerSoftware 命令已发送")
            return True
        except Exception as e:
            logger.error("TriggerSoftware 异常: %s", e)
            return False

    # ---------------- 状态查询 ----------------
    def get_trigger_status(self):
        """返回触发相关状态。

        结构示例::
            {
                'enabled': True,
                'armed': True,     # 相机是否处于等待触发的 ready 状态 (部分型号支持 TriggerArmed)
                'frame_counter': n  # 统计帧计数 (若节点可读)
            }
        """
        status = {
            'enabled': self.params.get('trigger_enabled', False),
            'armed': None,
            'frame_counter': None,
        }
        if not self.opened:
            return status
        try:
            # 触发是否已 Arm：部分机型支持 TriggerArmed / TriggerStatus 节点
            try:
                armed_val = ctypes.c_bool()
                ret = self.device.MV_CC_GetBoolValue("TriggerArmed", armed_val)
                if ret == 0:
                    status['armed'] = bool(armed_val.value)
            except Exception:
                pass

            # 读取帧计数，判断是否有帧到达
            try:
                counter_struct = MVCC_INTVALUE()
                ret = self.device.MV_CC_GetIntValue("Statistic_Total_Frame_Count", counter_struct)
                if ret == 0:
                    status['frame_counter'] = int(counter_struct.nCurValue)
            except Exception:
                pass
        except Exception as e:
            logger.debug("get_trigger_status 异常: %s", e)
        return status

    # ------------------------------------------------------------------
    # 公共工具函数
    # ------------------------------------------------------------------
    def _release_handle(self):
        """安全释放相机句柄，防止资源泄漏"""
        if not self.device:
            return
        try:
            self.device.MV_CC_CloseDevice()
        except Exception:
            pass
        try:
            self.device.MV_CC_DestroyHandle()
        except Exception:
            pass
        self.device = None
        self.opened = False

    def _setup_sdk_log_path(self):
        """为海康 SDK 设置日志输出目录"""
        try:
            log_dir = os.path.join(SDK_PATH, 'log')
            os.makedirs(log_dir, exist_ok=True)
            dummy = MvCamera()
            dummy.MV_CC_SetSDKLogPath(log_dir)
            logger.debug("SDK 日志目录已设置: %s", log_dir)
        except Exception as e:
            logger.warning("SDK 日志目录设置失败: %s", e)

    def open(self, physical_idx=None):
        if self.opened:
            return True
        # 先彻底释放旧句柄，防止资源泄漏
        self._release_handle()

        # 枚举设备
        device_list = MV_CC_DEVICE_INFO_LIST()
        ret = MvCamera.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, device_list)
        if ret != 0:
            raise RuntimeError(f'枚举设备失败，错误码: {ret}')
        logger.info("当前枚举到设备数: %d", device_list.nDeviceNum)
        if device_list.nDeviceNum == 0:
            raise RuntimeError('未检测到任何海康相机设备，请检查物理连接、驱动安装和权限！')
        # 配置 SDK 日志目录
        self._setup_sdk_log_path()

        # 新增：如果传入physical_idx，直接用该idx连接
        if physical_idx is not None and 0 <= physical_idx < device_list.nDeviceNum:
            idx = physical_idx
            dev = device_list.pDeviceInfo[idx].contents
            logger.debug("连接前物理设备详细信息 idx=%s", idx)
            logger.debug("  类型: %s", 'GIGE' if dev.nTLayerType==0x1 else 'USB' if dev.nTLayerType==0x2 else dev.nTLayerType)
            if dev.nTLayerType == 0x1:
                ip = '.'.join([str((dev.SpecialInfo.stGigEInfo.nCurrentIp >> (8 * j)) & 0xff) for j in (3,2,1,0)])
                sn = bytes(dev.SpecialInfo.stGigEInfo.chSerialNumber).decode(errors='ignore').strip('\x00')
                manuf = bytes(dev.SpecialInfo.stGigEInfo.chManufacturerName).decode(errors='ignore').strip('\x00')
                logger.debug("  IP: %s", ip)
                logger.debug("  SN: %s", sn)
                logger.debug("  厂商: %s", manuf)
            elif dev.nTLayerType == 0x2:
                sn = bytes(dev.SpecialInfo.stUsb3VInfo.chSerialNumber).decode(errors='ignore').strip('\x00')
                manuf = bytes(dev.SpecialInfo.stUsb3VInfo.chManufacturerName).decode(errors='ignore').strip('\x00')
                logger.debug("  SN: %s", sn)
                logger.debug("  厂商: %s", manuf)
        else:
            # 兼容老逻辑：按serial查找
            idx = 0
            serial = self.config.get('serial', '').replace('\0', '').strip().upper()
            if not serial:
                # 未指定 serial 时默认选择索引 0
                idx = 0
                logger.info("未指定 serial，默认使用 idx 0 设备")
            logger.info("配置serial: '%s'", serial)
            found = False
            all_devices_log = []
            for i in range(device_list.nDeviceNum):
                dev = device_list.pDeviceInfo[i].contents
                sn = ''
                dev_type = ''
                ip = ''
                if dev.nTLayerType == MV_GIGE_DEVICE:
                    dev_type = 'GIGE'
                    ip = '.'.join([str((dev.SpecialInfo.stGigEInfo.nCurrentIp >> (8 * j)) & 0xff) for j in (3, 2, 1, 0)])
                    sn = bytes(dev.SpecialInfo.stGigEInfo.chSerialNumber).decode(errors='ignore').strip('\x00').replace('\0', '').strip().upper()
                    self.config['ip'] = ip  # 保存IP到config，供UI层使用
                    all_devices_log.append(f"设备{i}: GIGE IP={ip} SN={sn} 匹配: {sn == serial}")
                    logger.info("设备{i}: GIGE IP={ip} SN={sn} 匹配: {sn == serial}")
                    if serial and sn == serial:
                        idx = i
                        found = True
                        logger.info("匹配到目标相机 idx=%s", idx)
                        break
                elif dev.nTLayerType == MV_USB_DEVICE:
                    dev_type = 'USB'
                    ip = ''
                    sn = bytes(dev.SpecialInfo.stUsb3VInfo.chSerialNumber).decode(errors='ignore').strip('\x00').replace('\0', '').strip().upper()
                    all_devices_log.append(f"设备{i}: USB SN={sn} 匹配: {sn == serial}")
                    logger.info("设备{i}: USB SN={sn} 匹配: {sn == serial}")
                    if serial and sn == serial:
                        idx = i
                        found = True
                        logger.info("匹配到目标相机 idx=%s", idx)
                        break
            if serial and not found:
                raise RuntimeError(f'未找到序列号为{serial}的相机，请检查配置！\n当前枚举到的设备: {all_devices_log}')

        self.device = MvCamera()
        # 打印即将创建句柄的设备结构体内容
        dev_ptr = device_list.pDeviceInfo[idx]
        dev = dev_ptr.contents
        logger.debug("创建句柄前设备结构体内容 idx=%s", idx)
        logger.debug("  类型: %s", 'GIGE' if dev.nTLayerType==0x1 else 'USB' if dev.nTLayerType==0x2 else dev.nTLayerType)
        if dev.nTLayerType == 0x1:
            ip = '.'.join([str((dev.SpecialInfo.stGigEInfo.nCurrentIp >> (8 * j)) & 0xff) for j in (3,2,1,0)])
            sn = bytes(dev.SpecialInfo.stGigEInfo.chSerialNumber).decode(errors='ignore').strip('\x00')
            manuf = bytes(dev.SpecialInfo.stGigEInfo.chManufacturerName).decode(errors='ignore').strip('\x00')
            logger.debug("  IP: %s", ip)
            logger.debug("  SN: %s", sn)
            logger.debug("  厂商: %s", manuf)
        elif dev.nTLayerType == 0x2:
            sn = bytes(dev.SpecialInfo.stUsb3VInfo.chSerialNumber).decode(errors='ignore').strip('\x00')
            manuf = bytes(dev.SpecialInfo.stUsb3VInfo.chManufacturerName).decode(errors='ignore').strip('\x00')
            logger.debug("  SN: %s", sn)
            logger.debug("  厂商: %s", manuf)
        # ---- 结构体深拷贝，规避指针/内存问题 ----
        import ctypes
        dev_copy = MV_CC_DEVICE_INFO()
        ctypes.memmove(ctypes.byref(dev_copy), ctypes.byref(dev), ctypes.sizeof(MV_CC_DEVICE_INFO))
        # 保存设备信息副本，后续如需访问相机类型等字段可直接使用
        self.dev_info = dev_copy
        ret = self.device.MV_CC_CreateHandle(dev_copy)
        if ret != 0:
            msg = self._err_msg(ret)
            raise RuntimeError(f'创建相机句柄失败，错误码: {ret}，{msg}')
        # 打开设备
        ret = self.device.MV_CC_OpenDevice()
        if ret != 0:
            logger.error("打开相机失败，错误码: %s", ret)
            # 针对常见错误码2147484163，自动输出详细解释和排查建议
            if ret == 2147484163:
                logger.error("该错误通常为：设备已被其他程序占用、上次连接未释放、网络/IP冲突或权限不足。\n" 
                      "请尝试以下操作：\n"
                      "1. 重启电脑，确保无其他采集程序占用相机。\n"
                      "2. 检查本机与相机IP是否同网段且无冲突。\n"
                      "3. 关闭除主采集网卡外的其他网卡，避免多网卡冲突。\n"
                      "4. 检查MVS等官方软件是否未完全退出。\n"
                      "5. 如相机有Web管理界面，确认未被锁定。\n"
                      "6. 检查驱动DLL和SDK环境是否完整。\n")
                # 自动输出当前网络信息，辅助判断
                try:
                    import socket
                    import psutil
                    logger.error("本机网卡信息:")
                    for iface, addrs in psutil.net_if_addrs().items():
                        for addr in addrs:
                            if addr.family == socket.AF_INET:
                                logger.error("  网卡: %s, IP: %s", iface, addr.address)
                except Exception as e:
                    logger.error("获取本机网卡信息失败: %s", e)
            return False
        self.opened = True
        self.status = 'opened'
        logger.info("%s 打开", self.camera_id)
        
        # 设置触发模式为关闭（连续采集）
        try:
            # TriggerMode Off & AcquisitionMode Continuous
            ret = self.device.MV_CC_SetEnumValue("TriggerMode", 0)
            logger.info("设置连续采集模式: ret=%s", ret)
            # AcquisitionMode 根据触发开关选择
            if self.params.get('trigger_enabled'):
                # 触发模式下默认使用 SingleFrame，可根据需要扩展
                self.device.MV_CC_SetEnumValue("AcquisitionMode", MV_CAM_ACQ_MODE_SINGLE)
            else:
                # 连续采集
                self.device.MV_CC_SetEnumValue("AcquisitionMode", MV_CAM_ACQ_MODE_CONTINUOUS)
        except Exception as e:
            logger.warning("设置触发模式异常: %s", e)
        
        # 设置自动曝光模式
        try:
            # 先尝试设置为自动曝光模式
            ret_exp_auto = self.device.MV_CC_SetEnumValue("ExposureAuto", 2)  # 2=自动模式
            logger.info("设置自动曝光模式: ret=%s", ret_exp_auto)
            # 如果失败，尝试设置固定曝光时间
            if ret_exp_auto != 0:
                ret_exp = self.device.MV_CC_SetFloatValue("ExposureTime", 10000.0)  # 10ms曝光
                logger.info("设置固定曝光时间(10ms): ret=%s", ret_exp)
        except Exception as e:
            logger.warning("设置曝光异常: %s", e)
        
        # 增益设置将由apply_params方法根据配置文件控制
        # 不再在此处固定设置自动增益模式
        
        # 动态设置 GigE 最佳包大小，提升取流稳定性
        if dev.nTLayerType == MV_GIGE_DEVICE:
            size = self.device.MV_CC_GetOptimalPacketSize()
            if size > 0:
                self.device.MV_CC_SetIntValue("GevSCPSPacketSize", size)
                logger.info("已设置最优包大小: %s", size)
            else:
                logger.warning("获取最优包大小失败: %s", size)
            
        # 缓存帧大小并预分配数据缓冲区
        try:
            stPayload = MVCC_INTVALUE()
            stWidth = MVCC_INTVALUE()
            stHeight = MVCC_INTVALUE()
            self.device.MV_CC_GetIntValue('PayloadSize', stPayload)
            self.device.MV_CC_GetIntValue('Width', stWidth)
            self.device.MV_CC_GetIntValue('Height', stHeight)
            self._payload_size = stPayload.nCurValue
            self._width = stWidth.nCurValue
            self._height = stHeight.nCurValue
            self._buf = (ctypes.c_ubyte * self._payload_size)()
            logger.info("已缓存帧参数: PayloadSize=%s, 分辨率=%sx%s", self._payload_size, self._width, self._height)
        except Exception as e:
            logger.warning("缓存帧参数失败: %s", e)
        
        # ---------- 获取并缓存像素格式 ----------
        try:
            stPixel = MVCC_ENUMVALUE()
            self.device.MV_CC_GetEnumValue("PixelFormat", stPixel)
            self._pixel_type = stPixel.nCurValue
            from MvCameraControl_class import PixelType_Gvsp_Mono8  # 仅导入所需常量
            self._is_mono8 = (self._pixel_type == PixelType_Gvsp_Mono8)
            self._use_bgr_direct = not self._is_mono8
            logger.info("像素格式已缓存: 0x%X, is_mono8=%s", self._pixel_type, self._is_mono8)
        except Exception as e:
            logger.warning("获取像素格式失败: %s", e)
        
        # 不在 open() 中自动启动采集，交由上层显式调用 start_grab()
        return True

    def close(self):
        if self.device and self.opened:
            try:
                self.device.MV_CC_CloseDevice()
            except Exception:
                pass
            try:
                self.device.MV_CC_DestroyHandle()
            except Exception:
                pass
            self.opened = False
            self.status = 'closed'
            logger.info("%s 关闭", self.camera_id)
        self.device = None
        return True

    def grab(self):
        if not self.opened:
            logger.warning("grab 调用失败: 相机未打开")
            return None
        import numpy as np
        from PIL import Image
        import ctypes
        from MvCameraControl_class import MV_FRAME_OUT_INFO_EX, PixelType_Gvsp_Mono8
        
        # print(f"DEBUG: 相机 {self.camera_id} grab开始 - opened: {self.opened}, grabbing: {self.grabbing}")
        # print(f"DEBUG: 相机 {self.camera_id} 触发参数 - trigger_enabled: {self.params.get('trigger_enabled')}")
        
        # 自动调试：打印设备句柄和opened状态
        logger.debug("grab 入口 opened=%s, 设备句柄=%s", self.opened, getattr(self.device, 'handle', None))
        # 自动调试：打印open方法返回值
        if hasattr(self.device, 'last_open_ret'):
            logger.debug("open 方法返回值: %s", self.device.last_open_ret)
        
        # 若未在采集则自动启动，并等待数据流稳定
        if not self.grabbing:
            # print(f"DEBUG: 相机 {self.camera_id} 开始启动采集")
            self.start_grab(continuous=not self.params.get('trigger_enabled'))
            time.sleep(0.15)
        
        # 如果是软件触发模式，发送触发信号
        trigger_enabled = self.params.get('trigger_enabled')
        trigger_source = self.params.get('trigger_source', '')
        # print(f"DEBUG: 相机 {self.camera_id} 触发检查 - trigger_enabled: {trigger_enabled}, trigger_source: '{trigger_source}'")
        if trigger_enabled and trigger_source.startswith('软'):
            # print(f"DEBUG: 相机 {self.camera_id} 软件触发模式，发送触发信号")
            self.trigger_software()
            time.sleep(0.05)  # 等待触发响应
        
        # 使用缓存的帧参数
        nDataSize = self._payload_size
        width = self._width
        height = self._height
        if nDataSize == 0 or self._buf is None:
            # 尚未缓存，或之前缓存失败，动态获取一次并缓存
            stPayload = MVCC_INTVALUE()
            stWidth = MVCC_INTVALUE()
            stHeight = MVCC_INTVALUE()
            with self._lock:
                self.device.MV_CC_GetIntValue('PayloadSize', stPayload)
                self.device.MV_CC_GetIntValue('Width', stWidth)
                self.device.MV_CC_GetIntValue('Height', stHeight)
            nDataSize = stPayload.nCurValue
            width = stWidth.nCurValue
            height = stHeight.nCurValue
            self._payload_size = nDataSize
            self._width = width
            self._height = height
            self._buf = (ctypes.c_ubyte * nDataSize)()
            logger.debug("动态缓存帧参数: PayloadSize=%s, 分辨率=%sx%s", nDataSize, width, height)
        
        # 根据像素格式决定采集策略：Mono8 直接走原始帧，其他格式先尝试 BGR，再回退。
        data_buffer = self._buf
        stFrameInfo = MV_FRAME_OUT_INFO_EX()

        # ---------- Mono8: 直接原始帧 ----------
        if not self._use_bgr_direct:
            ret = **********  # 强制走下方回退逻辑
        else:
            with self._lock:
                raw_ret = self.device.MV_CC_GetImageForBGR(data_buffer, nDataSize, stFrameInfo, self._grab_timeout_ms_trigger if self.params.get('trigger_enabled') else self._grab_timeout_ms_continuous)
                ret = int(raw_ret)
        
        # 将返回值强制转换为 Python int，避免 ctypes 返回类型导致 "in" 判断失效
        ret_u32 = ret & 0xFFFFFFFF

        # 无帧可取的两种返回码：
        # ********** = MV_E_TIMEOUT, ********** = MV_E_NODATA
        if ret != 0:
            if ret_u32 in (**********, **********):
                # 日志节流，降低 20 路相机时的 IO 压力
                now_ts = time.time()
                if now_ts - self._last_no_frame_log > 2.0:
                    logger.debug(
                        "无帧可取 | camera=%s | trigger=%s | timeout=%sms | ret_signed=%s ret_u32=%s(%s)",
                        self.camera_id,
                        self.params.get('trigger_enabled'),
                        self._grab_timeout_ms_trigger if self.params.get('trigger_enabled') else self._grab_timeout_ms_continuous,
                        ret,
                        ret_u32,
                        hex(ret_u32),
                    )
                    self._last_no_frame_log = now_ts
                # ---------- Fallback: 尝试直接获取原始帧 ----------
                raw_buf = (ctypes.c_ubyte * nDataSize)()
                stFrameInfoRaw = MV_FRAME_OUT_INFO_EX()
                with self._lock:
                    raw_ret = self.device.MV_CC_GetOneFrameTimeout(raw_buf, nDataSize, stFrameInfoRaw, self._grab_timeout_ms_trigger if self.params.get('trigger_enabled') else self._grab_timeout_ms_continuous)
                raw_ret = int(raw_ret)
                raw_ret_u32 = raw_ret & 0xFFFFFFFF
                if raw_ret == 0:
                    logger.debug("GetOneFrameTimeout 成功, PixelType=%s", stFrameInfoRaw.enPixelType)
                    # 目前仅实现对 Mono8 的转换
                    if stFrameInfoRaw.enPixelType == PixelType_Gvsp_Mono8:
                        arr_raw = np.frombuffer(raw_buf, dtype=np.uint8, count=stFrameInfoRaw.nWidth * stFrameInfoRaw.nHeight)
                        img_gray = arr_raw.reshape((stFrameInfoRaw.nHeight, stFrameInfoRaw.nWidth))
                        img = np.stack([img_gray, img_gray, img_gray], axis=2)
                        return Image.fromarray(img, 'RGB')
                    else:
                        logger.warning("未实现 PixelType=%s 的转换，返回 None", stFrameInfoRaw.enPixelType)
                        return None
                else:
                    if raw_ret_u32 in (**********, **********):
                        return None
                    logger.error("MV_CC_GetOneFrameTimeout 失败: ret=%s", raw_ret)
                    return None
            else:
                logger.error("MV_CC_GetImageForBGR失败: ret=%s", ret)
                return None

        logger.debug("获取图像成功: w=%s h=%s", stFrameInfo.nWidth, stFrameInfo.nHeight)

        # 成功获取图像，将ctypes数组转换为NumPy数组
        try:
            # 根据stFrameInfo中提供的实际数据大小创建numpy数组
            # 注意：这里不使用计算的data_size，而是使用stFrameInfo.nFrameLen
            logger.debug("获取到的图像数据信息: width=%s, height=%s, 实际帧长=%s字节", stFrameInfo.nWidth, stFrameInfo.nHeight, stFrameInfo.nFrameLen)
            
            # 使用实际帧长度作为数组大小限制，防止越界
            safe_size = min(nDataSize, stFrameInfo.nFrameLen)
            logger.debug("使用安全的缓冲区大小=%s字节", safe_size)
            
            # 数据缓冲区直接转换为numpy数组，不指定count，让numpy自动确定大小
            arr = np.frombuffer(data_buffer, dtype=np.uint8)
            
            # 根据图像格式处理数据
            if len(arr) >= stFrameInfo.nWidth * stFrameInfo.nHeight * 3:  # BGR格式
                # BGR格式，直接重塑
                img = arr[:stFrameInfo.nWidth * stFrameInfo.nHeight * 3].reshape((stFrameInfo.nHeight, stFrameInfo.nWidth, 3))
            elif len(arr) >= stFrameInfo.nWidth * stFrameInfo.nHeight:  # 单通道格式
                # 单通道格式，需要转换为RGB
                img_gray = arr[:stFrameInfo.nWidth * stFrameInfo.nHeight].reshape((stFrameInfo.nHeight, stFrameInfo.nWidth))
                # 转换为3通道
                img = np.stack([img_gray, img_gray, img_gray], axis=2)
            else:
                raise ValueError(f"图像数据大小不足: 获取到{len(arr)}字节，期望至少{stFrameInfo.nWidth * stFrameInfo.nHeight}字节")
            
            logger.debug("成功获取图像: shape=%s, 像素值范围=[%s,%s]", img.shape, np.min(img) if len(img) > 0 else 'N/A', np.max(img) if len(img) > 0 else 'N/A')
            
            # 转换为PIL图像
            return Image.fromarray(img, 'RGB')
        except ValueError as ve:
            logger.error("图像数据大小不匹配: %s", ve)
            return None
        except Exception as e:
            logger.error("图像数据处理异常: %s", e)
            return None
        except AssertionError as e:
            logger.critical("SDK结构体定义异常: %s\n请检查MvCameraControl_class.py中MVCC_INTVALUE定义，必须继承自ctypes.Structure！", e)
            return None
        except Exception as e:
            logger.error("grab异常: %s", e)
            logger.debug("当前设备句柄: %s", getattr(self.device, 'handle', None))
            return None

    def reconnect(self):
        logger.info("%s 重连中...", self.camera_id)
        self.close()
        return self.open()

    def get_status(self):
        return self.status

    def get_params(self):
        """返回当前缓存的相机参数字典副本"""
        return self.params.copy()