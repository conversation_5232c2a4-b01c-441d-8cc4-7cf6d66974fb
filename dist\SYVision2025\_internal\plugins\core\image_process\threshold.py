"""Core image thresholding processor.
Supports fixed, Otsu, Triangle and adaptive thresholding.
"""
from __future__ import annotations
import cv2
import numpy as np
from typing import Dict, Any

__all__ = ['ThresholdProcessor']

class ThresholdProcessor:
    name = 'ImageThreshold'

    def __init__(self, *, mode: str = 'binary', thresh_val: int = 128, max_val: int = 255,
                 adapt_block: int = 11, adapt_C: int = 2):
        """Parameters
        mode: 'binary', 'binary_inv', 'otsu', 'triangle', 'adapt_mean', 'adapt_gauss'
        thresh_val: fixed threshold value for binary / binary_inv
        max_val: value to set for pixels above threshold
        adapt_block: block size for adaptive methods (must be odd and >=3)
        adapt_C: constant subtracted from mean/gaussian in adaptive methods
        """
        self.mode = mode
        self.thresh_val = int(thresh_val)
        self.max_val = int(max_val)
        self.adapt_block = int(adapt_block) | 1  # ensure odd
        self.adapt_C = int(adapt_C)

    # ------------------------------------------------------------
    def process(self, img: np.ndarray, **override) -> Dict[str, Any]:
        """Process image and return dictionary with 'output' key."""
        if override:
            for k, v in override.items():
                if hasattr(self, k):
                    setattr(self, k, v)
        gray = img
        if gray.ndim == 3:
            gray = cv2.cvtColor(gray, cv2.COLOR_BGR2GRAY)
        m = self.mode.lower()
        if m == 'binary':
            _, dst = cv2.threshold(gray, self.thresh_val, self.max_val, cv2.THRESH_BINARY)
        elif m == 'binary_inv':
            _, dst = cv2.threshold(gray, self.thresh_val, self.max_val, cv2.THRESH_BINARY_INV)
        elif m == 'otsu':
            _, dst = cv2.threshold(gray, 0, self.max_val, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif m == 'triangle':
            _, dst = cv2.threshold(gray, 0, self.max_val, cv2.THRESH_BINARY + cv2.THRESH_TRIANGLE)
        elif m == 'adapt_mean':
            dst = cv2.adaptiveThreshold(gray, self.max_val, cv2.ADAPTIVE_THRESH_MEAN_C,
                                         cv2.THRESH_BINARY, self.adapt_block, self.adapt_C)
        elif m == 'adapt_gauss':
            dst = cv2.adaptiveThreshold(gray, self.max_val, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, self.adapt_block, self.adapt_C)
        else:
            dst = gray.copy()
        return {'output': dst}
