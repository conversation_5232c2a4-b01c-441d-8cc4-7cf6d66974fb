"""Warp Perspective Plugin

Applies a planar homography (bird-eye / 俯视矫正) to输入图像。

用法：
1. 将该插件添加在 `undistort` 之后（如有）
2. 参数说明：
   • homography: 9 个浮点数列表，按行优先展开的 3×3 H 矩阵
   • dst_size : 目标尺寸 [w, h]（整数），决定输出图像大小

获得 H 的常见方式：
• 在 Calibrate UI 里记录棋盘格 world ↔ image 点，离线计算并保存 H
• 使用 OpenCV `findHomography` 得到 H 并填入参数

如果 H 未设置，插件将原图透传。
"""
from __future__ import annotations

from typing import Dict, Any, List, Tuple
import cv2
import numpy as np
import json

from plugins.plugin_base import PluginBase

__all__ = ["WarpPerspectivePlugin"]


class WarpPerspectivePlugin(PluginBase):
    name = "warp_perspective"
    label = "透视校正"
    category = "几何校正"

    params: Dict[str, Any] = {
        "homography": [],        # 9 floats
        "dst_size": [640, 480],  # width, height
    }

    param_labels = {
        "homography": "H矩阵 (9数)",
        "dst_size": "目标尺寸",
    }

    # --------------------------------------------------
    def process(self, img, ctx):  # type: ignore[override]
        if img is None:
            return img, ctx

        H_list: List[float] = self.params.get("homography", [])
        if len(H_list) != 9:
            # 未配置 H，直接透传
            return img, ctx
        dst_size: List[int] | Tuple[int, int] = self.params.get("dst_size", [img.shape[1], img.shape[0]])
        try:
            H = np.asarray(H_list, dtype=np.float64).reshape(3, 3)
            w, h = int(dst_size[0]), int(dst_size[1])
            warped = cv2.warpPerspective(img, H, (w, h))
            # 将 H 和尺寸写入 ctx 供后续使用
            ctx["warp_perspective"] = {
                "H": H,
                "dst_size": (w, h),
            }
            return warped, ctx
        except Exception as exc:
            print(f"[WarpPerspective] warp failed: {exc}")
            return img, ctx

    # ---------------- UI ----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, **extra):
        """简单对话框，允许用户编辑 H 和 dst_size。"""
        import tkinter as tk
        from tkinter import ttk, messagebox

        win = tk.Toplevel(master)
        win.title("透视校正参数")
        ttk.Label(win, text="H (9 numbers, 空格或逗号分隔)").grid(row=0, column=0, sticky="w")
        var_h = tk.StringVar(value=" ".join(str(x) for x in params.get("homography", [])))
        ent_h = ttk.Entry(win, textvariable=var_h, width=50)
        ent_h.grid(row=0, column=1, padx=4, pady=4)

        ttk.Label(win, text="输出尺寸 W,H").grid(row=1, column=0, sticky="w")
        var_size = tk.StringVar(value=",".join(str(x) for x in params.get("dst_size", [640,480])))
        ent_s = ttk.Entry(win, textvariable=var_size, width=20)
        ent_s.grid(row=1, column=1, padx=4, pady=4, sticky="w")

        def _ok():
            # 解析输入
            try:
                hs = [float(x) for x in var_h.get().replace(",", " ").split()] if var_h.get().strip() else []
                if hs and len(hs) != 9:
                    raise ValueError("H 需为 9 个数字")
                sz = [int(x) for x in var_size.get().replace("x", ",").replace(" ", "").split(",")]
                if len(sz) != 2:
                    raise ValueError("尺寸格式: w,h")
                new_params = {
                    "homography": hs,
                    "dst_size": sz,
                }
                on_change(new_params)
                win.destroy()
            except Exception as e:
                messagebox.showerror("格式错误", str(e))

        ttk.Button(win, text="确定", command=_ok).grid(row=2, column=0, columnspan=2, pady=8)
        win.grab_set(); win.transient(master)


# 注册实例
_ = WarpPerspectivePlugin()
