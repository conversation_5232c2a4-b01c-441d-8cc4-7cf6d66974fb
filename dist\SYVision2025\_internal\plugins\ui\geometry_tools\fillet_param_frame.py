"""FilletParamFrame - 圆角半径测量工具参数面板"""
from __future__ import annotations
import tkinter as tk
from tkinter import ttk
from typing import Dict, Any

from .base_param_frame import BaseParamFrame


class FilletParamFrame(BaseParamFrame):
    """圆角半径测量参数面板"""

    def _build(self):
        # 扫描线数
        lbl = ttk.Label(self, text="扫描线数:")
        lbl.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self._vars["scan_lines"] = tk.IntVar(value=12)
        spin = ttk.Spinbox(self, from_=4, to=40, width=8, textvariable=self._vars["scan_lines"])
        spin.grid(row=0, column=1, padx=5, pady=2)

        # 线宽
        lbl2 = ttk.Label(self, text="线宽:")
        lbl2.grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self._vars["line_width"] = tk.IntVar(value=3)
        spin2 = ttk.Spinbox(self, from_=1, to=20, width=8, textvariable=self._vars["line_width"])
        spin2.grid(row=1, column=1, padx=5, pady=2)

        # 边缘阈值
        lbl3 = ttk.Label(self, text="边缘阈值:")
        lbl3.grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self._vars["edge_threshold"] = tk.IntVar(value=15)
        spin3 = ttk.Spinbox(self, from_=1, to=100, width=8, textvariable=self._vars["edge_threshold"])
        spin3.grid(row=2, column=1, padx=5, pady=2)

        # Debug 选项
        self._vars["debug_log"] = tk.BooleanVar(value=False)
        chk = ttk.Checkbutton(self, text="调试日志", variable=self._vars["debug_log"])
        chk.grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        # 占位，保持紧凑
        self.columnconfigure(1, weight=1)
