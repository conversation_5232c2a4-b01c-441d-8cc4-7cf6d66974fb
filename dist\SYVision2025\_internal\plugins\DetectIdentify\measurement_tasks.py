"""
测量任务管理系统
支持多种测量工具的组合使用
"""

import uuid
from typing import Dict, List, Optional, Any
from enum import Enum
import json


class MeasurementType(Enum):
    """测量类型枚举"""
    RECTANGLE = "rectangle"  # 矩形测量
    CIRCLE = "circle"       # 圆测量
    ARC = "arc"            # 圆弧测量
    ANGLE = "angle"        # 角度测量


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 待执行
    RUNNING = "running"      # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"       # 执行失败
    DISABLED = "disabled"   # 已禁用


class MeasurementTask:
    """测量任务类"""
    
    def __init__(self, task_type: MeasurementType, name: str = None):
        """
        初始化测量任务
        
        Args:
            task_type: 测量类型
            name: 任务名称
        """
        self.id = str(uuid.uuid4())
        self.type = task_type
        self.name = name or f"{task_type.value}测量_{self.id[:8]}"
        self.status = TaskStatus.PENDING
        self.enabled = True
        
        # ROI信息
        self.roi = None
        self.use_template = False  # 是否使用模板定位
        
        # 测量参数
        self.params = self._get_default_params()
        
        # 结果
        self.result = None
        self.error_message = None
        
    def _get_default_params(self) -> Dict:
        """获取默认参数"""
        if self.type == MeasurementType.RECTANGLE:
            return {
                "scan_lines": 8,           # 扫描线数量
                "edge_threshold": 30,      # 边缘阈值
                "line_width": 3,          # 扫描线宽度
                "polarity": "both",       # 边缘极性
                "show_scan_lines": True,  # 显示扫描线
                "show_fit_lines": True    # 显示拟合线
            }
        elif self.type == MeasurementType.CIRCLE:
            return {
                "radial_lines": 12,       # 径向扫描线数量
                "edge_threshold": 30,     # 边缘阈值
                "fit_method": "least_squares",  # 拟合方法
                "min_points": 6,          # 最小拟合点数
                "show_scan_lines": True,  # 显示扫描线
                "show_fit_circle": True   # 显示拟合圆
            }
        elif self.type == MeasurementType.ARC:
            return {
                "start_angle": 0,         # 起始角度
                "end_angle": 180,         # 结束角度
                "radial_lines": 8,        # 径向扫描线数量
                "edge_threshold": 30,     # 边缘阈值
                "fit_method": "least_squares",  # 拟合方法
                "show_scan_lines": True,  # 显示扫描线
                "show_fit_arc": True      # 显示拟合圆弧
            }
        elif self.type == MeasurementType.ANGLE:
            return {
                "line1_length": 50,       # 直线1长度
                "line2_length": 50,       # 直线2长度
                "edge_threshold": 30,     # 边缘阈值
                "fit_method": "least_squares",  # 拟合方法
                "show_lines": True        # 显示拟合直线
            }
        else:
            return {}
    
    def set_roi(self, roi: Dict):
        """设置ROI"""
        self.roi = roi.copy()
    
    def set_template_mode(self, enabled: bool):
        """设置是否使用模板定位"""
        self.use_template = enabled
    
    def update_params(self, params: Dict):
        """更新参数"""
        self.params.update(params)
    
    def set_result(self, result: Dict):
        """设置测量结果"""
        if result.get('ok'):
            self.result = result
            self.status = TaskStatus.COMPLETED
            self.error_message = None
        else:
            self.result = None
            self.status = TaskStatus.FAILED
            self.error_message = result.get('error', '未知错误')
    
    def reset(self):
        """重置任务状态"""
        self.status = TaskStatus.PENDING
        self.result = None
        self.error_message = None
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'id': self.id,
            'type': self.type.value,
            'name': self.name,
            'status': self.status.value,
            'enabled': self.enabled,
            'roi': self.roi,
            'use_template': self.use_template,
            'params': self.params,
            'result': self.result,
            'error_message': self.error_message
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'MeasurementTask':
        """从字典创建任务"""
        task_type = MeasurementType(data['type'])
        task = cls(task_type, data['name'])
        task.id = data['id']
        task.status = TaskStatus(data['status'])
        task.enabled = data['enabled']
        task.roi = data['roi']
        task.use_template = data['use_template']
        task.params = data['params']
        task.result = data['result']
        task.error_message = data['error_message']
        return task


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        """初始化任务管理器"""
        self.tasks: List[MeasurementTask] = []
        self.current_task_index = -1
    
    def add_task(self, task_type: MeasurementType, name: str = None) -> MeasurementTask:
        """添加任务"""
        task = MeasurementTask(task_type, name)
        self.tasks.append(task)
        return task
    
    def remove_task(self, task_id: str) -> bool:
        """删除任务"""
        for i, task in enumerate(self.tasks):
            if task.id == task_id:
                del self.tasks[i]
                if self.current_task_index >= i:
                    self.current_task_index -= 1
                return True
        return False
    
    def get_task(self, task_id: str) -> Optional[MeasurementTask]:
        """获取任务"""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None
    
    def get_enabled_tasks(self) -> List[MeasurementTask]:
        """获取启用的任务"""
        return [task for task in self.tasks if task.enabled]
    
    def clear_all_tasks(self):
        """清除所有任务"""
        self.tasks.clear()
        self.current_task_index = -1
    
    def reset_all_tasks(self):
        """重置所有任务状态"""
        for task in self.tasks:
            task.reset()
        self.current_task_index = -1
    
    def get_task_count(self) -> Dict[str, int]:
        """获取任务统计"""
        total = len(self.tasks)
        enabled = len(self.get_enabled_tasks())
        completed = len([t for t in self.tasks if t.status == TaskStatus.COMPLETED])
        failed = len([t for t in self.tasks if t.status == TaskStatus.FAILED])
        
        return {
            'total': total,
            'enabled': enabled,
            'completed': completed,
            'failed': failed,
            'pending': enabled - completed - failed
        }
    
    def save_to_file(self, filepath: str):
        """保存任务到文件"""
        data = {
            'tasks': [task.to_dict() for task in self.tasks],
            'current_task_index': self.current_task_index
        }
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_from_file(self, filepath: str):
        """从文件加载任务"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.tasks = [MeasurementTask.from_dict(task_data) for task_data in data['tasks']]
            self.current_task_index = data.get('current_task_index', -1)
            return True
        except Exception as e:
            print(f"加载任务文件失败: {e}")
            return False
