"""IO Relay Tkinter UI
USB 串口继电器板调试工具，支持 2/4/8/16 路。
协议：4 字节 A0 | addr | op | checksum
"""
from __future__ import annotations

import threading, time
import tkinter as tk
from tkinter import ttk, messagebox

try:
    import serial, serial.tools.list_ports as list_ports
except ImportError:
    serial = None

OPS = {
    'ON': 0x01,
    'OFF': 0x00,
    'TOG': 0x04,
    'QRY': 0x05,
}

class IORelayFrame(ttk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.ser: serial.Serial | None = None
        self._stop_evt = threading.Event()
        self.relay_count = 2
        self.states: list[bool] = []
        self._build_ui()

    # ---------------- UI ----------------
    def _build_ui(self):
        top = ttk.Frame(self); top.grid(row=0, column=0, sticky='we', pady=4)
        ttk.Label(top, text='端口:').pack(side=tk.LEFT)
        self.cb_port = ttk.Combobox(top, width=8, state='readonly'); self.cb_port.pack(side=tk.LEFT, padx=2)
        ttk.Button(top, text='刷新', command=self._refresh_ports).pack(side=tk.LEFT)
        ttk.Label(top, text='波特率:').pack(side=tk.LEFT, padx=(10,0))
        self.cb_baud = ttk.Combobox(top, values=['9600','19200','38400','57600','115200'], width=8, state='readonly'); self.cb_baud.set('9600'); self.cb_baud.pack(side=tk.LEFT)
        self.btn_open = ttk.Button(top, text='打开', command=self._toggle_port); self.btn_open.pack(side=tk.LEFT, padx=(10,0))
        # relay count
        ttk.Label(top, text='路数:').pack(side=tk.LEFT, padx=(10,0))
        self.cb_count = ttk.Combobox(top, values=['2','4','8','16'], width=4, state='readonly'); self.cb_count.set('2'); self.cb_count.pack(side=tk.LEFT)
        self.cb_count.bind('<<ComboboxSelected>>', lambda e: self._rebuild_buttons())

        # grid for buttons
        self.grid_area = ttk.Frame(self); self.grid_area.grid(row=1, column=0, pady=6)
        self._refresh_ports(); self._rebuild_buttons()

    def _refresh_ports(self):
        if serial is None:
            messagebox.showerror('缺少依赖', '请先安装 pyserial'); return
        ports = [p.device for p in list_ports.comports()]
        self.cb_port['values'] = ports
        if ports:
            self.cb_port.set(ports[0])

    def _rebuild_buttons(self):
        for w in self.grid_area.winfo_children():
            w.destroy()
        self.relay_count = int(self.cb_count.get())
        self.states = [False]*self.relay_count
        cols = 4
        for idx in range(self.relay_count):
            row = idx // cols; col = (idx % cols)*3
            ttk.Label(self.grid_area, text=f'第{idx+1}路').grid(row=row, column=col, padx=2, pady=2)
            ttk.Button(self.grid_area, text='开', command=lambda i=idx: self._cmd(i,'ON')).grid(row=row, column=col+1)
            ttk.Button(self.grid_area, text='关', command=lambda i=idx: self._cmd(i,'OFF')).grid(row=row, column=col+2)
            lamp = tk.Label(self.grid_area, width=2, background='gray'); lamp.grid(row=row, column=col+3, padx=4)
            lamp._idx = idx

    # ---------------- Serial helpers ----------------
    def _toggle_port(self):
        if self.ser and self.ser.is_open:
            self._close_port(); return
        if serial is None:
            messagebox.showerror('缺少依赖', '未安装 pyserial'); return
        port = self.cb_port.get(); baud=int(self.cb_baud.get())
        if not port:
            messagebox.showwarning('提示','请选择串口'); return
        try:
            self.ser = serial.Serial(port, baud, timeout=0.1)
        except Exception as e:
            messagebox.showerror('打开失败', str(e)); return
        self.btn_open.config(text='关闭'); self.cb_port.config(state='disabled'); self.cb_baud.config(state='disabled')
        self._stop_evt.clear(); threading.Thread(target=self._rx_loop, daemon=True).start()

    def _close_port(self):
        if self.ser:
            self._stop_evt.set(); time.sleep(0.2)
            try: self.ser.close()
            except Exception: pass
            self.ser=None
        self.btn_open.config(text='打开'); self.cb_port.config(state='readonly'); self.cb_baud.config(state='readonly')

    def _rx_loop(self):
        while not self._stop_evt.is_set():
            try:
                data = self.ser.read_all()
                if len(data)==4 and data[0]==0xA0:
                    addr=data[1]; op=data[2]
                    state = (op==0x01 or op==0x03)
                    idx=addr-1
                    if 0<=idx<len(self.states):
                        self.states[idx]=state; self._update_lamp(idx)
            except Exception:
                break
            time.sleep(0.05)

    def _cmd(self, idx:int, action:str):
        if not (self.ser and self.ser.is_open):
            messagebox.showwarning('串口未开','请先打开串口'); return
        addr=idx+1; op=OPS[action]
        frame = bytes([0xA0, addr, op, (0xA0+addr+op)&0xFF])
        try: self.ser.write(frame)
        except Exception as e: messagebox.showerror('发送失败', str(e)); return
        if action in ('ON','OFF','TOG','QRY'):
            time.sleep(0.1)  # wait response

    def _update_lamp(self, idx):
        for w in self.grid_area.winfo_children():
            if getattr(w, '_idx', None)==idx and isinstance(w, tk.Label):
                w.configure(background='green' if self.states[idx] else 'gray')
                break

    def destroy(self):
        try:
            self._close_port()
        finally:
            super().destroy()


if __name__ == '__main__':
    root = tk.Tk(); root.title('继电器调试'); IORelayFrame(root).pack(); root.mainloop()
