[2025-07-05 15:06:42] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 15:07:03] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 17:26:12] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 17:42:15] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 18:41:19] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 18:49:19] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 18:49:56] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:10:01] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:11:58] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 19:12:05] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:19:46] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:20:03] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:20:07] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 19:46:41] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:48:02] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 19:48:12] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:59:04] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:59:20] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 19:59:24] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 20:08:59] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:09:15] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:09:25] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 20:09:32] [SYVision.operation] INFO: 用户:用户 操作:统一显示切换 目标:关闭 详情:null
[2025-07-05 20:18:54] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:21:37] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:22:01] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:22:15] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 20:22:21] [SYVision.operation] INFO: 用户:用户 操作:统一显示切换 目标:关闭 详情:null
[2025-07-05 20:25:01] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:32:01] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:32:56] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:33:00] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 20:36:20] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:37:03] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:37:22] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-05 20:37:27] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-05 20:46:26] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 11:40:08] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 11:43:36] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 11:43:53] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 11:43:56] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-06 11:55:28] [SYVision.operation] INFO: 用户:用户 操作:统一显示切换 目标:关闭 详情:null
[2025-07-06 12:02:30] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 12:02:44] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 12:02:46] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-06 12:02:52] [SYVision.operation] INFO: 用户:用户 操作:统一显示切换 目标:关闭 详情:null
[2025-07-06 12:09:43] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 12:09:56] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 12:09:59] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-06 12:10:16] [SYVision.operation] INFO: 用户:用户 操作:统一显示切换 目标:关闭 详情:null
[2025-07-06 15:20:26] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 15:20:50] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-06 15:21:09] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 15:21:18] [SYVision.operation] INFO: 用户:用户 操作:统一显示切换 目标:关闭 详情:null
[2025-07-06 15:23:05] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 19:11:48] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 19:17:18] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 19:17:33] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-06 19:17:36] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-06 19:19:13] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:连续调试 详情:{"previous_mode": "运行"}
[2025-07-07 10:17:18] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-07 16:18:21] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-07 16:20:22] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-07 16:20:33] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-07 16:49:03] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-08 15:00:26] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-08 15:08:16] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-08 15:08:18] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-09 10:48:48] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-09 10:49:03] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-09 10:49:05] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-09 11:23:12] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-09 11:23:25] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-09 11:23:33] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
[2025-07-09 16:10:10] [SYVision.operation] INFO: 用户:系统 操作:自动布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-09 16:10:35] [SYVision.operation] INFO: 用户:用户 操作:配方导入布局调整 目标:2工位 详情:{"workstation_count": 2, "layout": 2}
[2025-07-09 16:10:38] [SYVision.operation] INFO: 用户:系统 操作:模式切换 目标:运行 详情:{"previous_mode": "未知"}
