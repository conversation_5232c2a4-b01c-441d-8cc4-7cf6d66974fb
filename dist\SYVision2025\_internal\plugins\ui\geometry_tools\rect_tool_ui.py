# -*- coding: utf-8 -*-
"""交互式矩形卡尺 UI（轴对齐）。

使用 CrossCaliperMeasure 进行宽高测量，交互流程：
1. 拖拽定义矩形 ROI（中心和宽高）
2. “拟合边缘” 调用 CrossCaliperMeasure 搜索四边边缘并拟合
3. “测量” 显示宽度、高度

保存/加载 YAML 格式与其他工具保持一致，存放在 ``configs/rect_measure``。
"""
from __future__ import annotations

import pathlib, sys
from typing import Optional, Tuple, Any
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

import numpy as np
import yaml

# ---------- YAML 序列化辅助 ----------

def _yaml_sanitize(o):
    """递归将 numpy / Path 等转换为基本类型，确保 safe_dump 可用"""
    import numpy as _np
    from pathlib import Path as _Path
    if isinstance(o, (_np.generic,)):
        return o.item()
    if isinstance(o, _np.ndarray):
        return o.tolist()
    if isinstance(o, _Path):
        return str(o)
    if isinstance(o, dict):
        return {k: _yaml_sanitize(v) for k, v in o.items()}
    if isinstance(o, list):
        return [_yaml_sanitize(v) for v in o]
    return o
from pathlib import Path

PREVIEW_SIZE = 550  # 画布固定尺寸
CFG_DIR = Path(__file__).resolve().parents[3] / 'configs' / 'rect_measure'
CFG_DIR.mkdir(parents=True, exist_ok=True)

# third-party
import cv2
from PIL import Image, ImageTk

# --- 导入测量类 ---
try:
    from plugins.geometry_tools.rect_tool import measure_rect
except ImportError:
    _proj_root = pathlib.Path(__file__).resolve().parents[3]
    if str(_proj_root) not in sys.path:
        sys.path.insert(0, str(_proj_root))
    from plugins.geometry_tools.rect_tool import measure_rect


from pathlib import Path

class RectToolUI(tk.Toplevel):
    """交互 UI，同时提供 run_once(img) 以供无界面流水线调用。"""
    _ST_IDLE = 'idle'
    _ST_ROI = 'roi_defined'
    _ST_FITTED = 'fitted'
    _ST_MEASURED = 'measured'

    def __init__(self, master: Optional[tk.Widget] = None, img: Optional[np.ndarray] = None, *, pose: Optional[dict] = None, recipe_path: Optional[str] = None):
        super().__init__(master)
        self.title('矩形卡尺工具')
        self.geometry('1000x750')
        self.protocol('WM_DELETE_WINDOW', self._on_close)

        # 图像数据
        self.img_orig: Optional[np.ndarray] = None
        self.img_display: Optional[np.ndarray] = None
        self.tk_img: Optional[ImageTk.PhotoImage] = None

        # 视图参数
        self._zoom = 1.0
        self._offset = [0.0, 0.0]
        self._pan_start: Optional[Tuple[int, int]] = None

        # 拖拽相关
        self._dragging = False
        self.p0_img: Optional[Tuple[float, float]] = None  # 拖拽起点（图像坐标）
        self.rect_est: Optional[Tuple[float,float,float,float]] = None  # cx,cy,w,h

        # ROI 列表
        self.rois: list[dict[str, Any]] = []
        self.current_idx: Optional[int] = None
        self.next_id: int = 1

        # 保存关联的配方路径（若从流程编辑器调用则会传入）
        self._recipe_path: Optional[Path] = Path(recipe_path) if recipe_path else None

        # 算法参数
        self.width_var = tk.DoubleVar(value=20.0)
        self.n_lines_var = tk.IntVar(value=10)
        self.thresh_var = tk.DoubleVar(value=5.0)
        self.thickness_var = tk.IntVar(value=3)
        self.polarity_var = tk.StringVar(value='both')
        # 像素到毫米比例 (mm per pixel)，0 表示仅显示像素
        self.mm_var = tk.DoubleVar(value=0.0)

        # --- 布局 ---
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # 左侧面板
        p_frame = ttk.Frame(self, padding=6, width=260)
        p_frame.grid(row=0, column=0, sticky='ns')
        p_frame.grid_propagate(False)

        ttk.Button(p_frame, text='打开图像', command=self._open_image).pack(fill=tk.X, pady=2)
        self.btn_fit = ttk.Button(p_frame, text='拟合边缘', command=self._fit_edges, state='disabled')
        self.btn_fit.pack(fill=tk.X, pady=2)
        self.btn_measure = ttk.Button(p_frame, text='测量', command=self._measure_rect, state='disabled')
        self.btn_measure.pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='清除标记', command=self._clear_marks).pack(fill=tk.X, pady=2)

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)

        # 参数
        param_fr = ttk.LabelFrame(p_frame, text='算法参数', padding=4)
        param_fr.pack(fill=tk.X, pady=4)
        row = 0
        ttk.Label(param_fr, text='ROI 宽度').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.width_var, width=6).grid(row=row, column=1)
        row += 1
        ttk.Label(param_fr, text='线数').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.n_lines_var, width=6).grid(row=row, column=1)
        row += 1
        ttk.Label(param_fr, text='阈值').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.thresh_var, width=6).grid(row=row, column=1)
        row += 1
        ttk.Label(param_fr, text='厚度').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.thickness_var, width=6).grid(row=row, column=1)
        row += 1
        ttk.Label(param_fr, text='极性').grid(row=row, column=0, sticky='e')
        ttk.Combobox(param_fr, textvariable=self.polarity_var, state='readonly', width=8,
                     values=('both','dark2bright','bright2dark')).grid(row=row, column=1)
        # mm/px 输入
        row += 1
        ttk.Label(param_fr, text='mm/px').grid(row=row, column=0, sticky='e')
        ttk.Entry(param_fr, textvariable=self.mm_var, width=6).grid(row=row, column=1)

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        ttk.Label(p_frame, text='矩形列表').pack(anchor='w')
        self.tree = ttk.Treeview(p_frame, columns=('state','w','h'), show='headings', height=6)
        self.tree.column('state', width=60, anchor='center')
        self.tree.column('w', width=80, anchor='center')
        self.tree.column('h', width=80, anchor='center')
        self.tree.heading('state', text='阶段')
        self.tree.heading('w', text='宽度')
        self.tree.heading('h', text='高度')
        self.tree.pack(fill=tk.X)
        self.tree.bind('<<TreeviewSelect>>', self._on_tree_select)

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        self.result_var = tk.StringVar(value='宽度: - px / - mm  高度: - px / - mm')
        ttk.Label(p_frame, textvariable=self.result_var).pack()

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)
        ttk.Button(p_frame, text='保存配置', command=self._save_yaml).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='载入配置', command=self._load_yaml).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='导入模板匹配', command=self._import_template).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='确定', command=self._on_close).pack(fill=tk.X, pady=2)

        # 右侧画布
        self.canvas = tk.Canvas(self, width=PREVIEW_SIZE, height=PREVIEW_SIZE, bg='#202020')
        self.canvas.grid(row=0, column=1, sticky='nsew')
        self.canvas.bind('<ButtonPress-1>', self._on_drag_start)
        self.canvas.bind('<B1-Motion>', self._on_drag_move)
        self.canvas.bind('<ButtonRelease-1>', self._on_drag_end)
        self.canvas.bind('<MouseWheel>', self._on_zoom)
        self.canvas.bind('<ButtonPress-3>', self._on_pan_start)
        self.canvas.bind('<B3-Motion>', self._on_pan_move)


        if img is not None:
            self.load_image(img)
        # 如果上游直接传入 pose，则优先使用
        if pose is not None:
            try:
                self._load_pose(pose)
            except Exception as e:
                print('[WARN] 加载上游 pose 失败', e)

    # ---------------- 图像/坐标辅助 -----------------
    def _open_image(self):
        path = filedialog.askopenfilename(title='选择图片', filetypes=[('Images','*.png *.jpg *.bmp *.tif'),('All','*.*')])
        if not path:
            return
        img = cv2.imdecode(np.fromfile(path, dtype=np.uint8), cv2.IMREAD_COLOR)
        if img is None:
            messagebox.showerror('错误','无法加载图像')
            return
        self.load_image(img)

    def load_image(self, img: np.ndarray):
        self.img_orig = img
        self._zoom = PREVIEW_SIZE / max(img.shape[:2])
        self._offset = [0.0, 0.0]
        self._update_canvas_image()
        self._draw_overlays()

    def _update_canvas_image(self):
        if self.img_orig is None:
            return
        disp = cv2.resize(self.img_orig, None, fx=self._zoom, fy=self._zoom, interpolation=cv2.INTER_AREA)
        disp_rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
        pil = Image.fromarray(disp_rgb)
        self.tk_img = ImageTk.PhotoImage(pil)
        self.canvas.delete('img')
        self.canvas.create_image(self._offset[0], self._offset[1], anchor='nw', image=self.tk_img, tags='img')
        self.canvas.lower('img')

    def _canvas_to_img(self, x: float, y: float) -> Tuple[float,float]:
        return ((x - self._offset[0]) / self._zoom, (y - self._offset[1]) / self._zoom)

    # ---------------- 拖拽定义 ROI -----------------
    def _on_drag_start(self, e):
        if self.img_orig is None:
            return
        self._dragging = True
        self.p0_img = self._canvas_to_img(e.x, e.y)
        self.rect_est = None
        self._draw_overlays()

    def _on_drag_move(self, e):
        if not self._dragging:
            return
        cx0, cy0 = self.p0_img
        cx1, cy1 = self._canvas_to_img(e.x, e.y)
        cx = (cx0 + cx1) / 2
        cy = (cy0 + cy1) / 2
        w = abs(cx1 - cx0)
        h = abs(cy1 - cy0)
        self.rect_est = (cx, cy, w, h)
        self._draw_overlays()

    def _on_drag_end(self, e):
        if not self._dragging:
            return
        self._dragging = False
        if self.rect_est is None:
            return
        cx, cy, w, h = self.rect_est
        if w < 5 or h < 5:
            self.rect_est = None
            self._draw_overlays()
            return
        roi = {
            'id': self.next_id,
            'cx': cx,
            'cy': cy,
            'w': w,
            'h': h,
            'stage': self._ST_ROI,
            'result': None,
            'angle': 0.0
        }
        self.rois.append(roi)
        self.current_idx = len(self.rois) - 1
        self.next_id += 1
        self.rect_est = None
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()

    # ---------------- 按钮逻辑 -----------------
    def _update_btn_state(self):
        if self.current_idx is None:
            self.btn_fit.config(state='disabled')
            self.btn_measure.config(state='disabled')
            return
        stage = self.rois[self.current_idx]['stage']
        if stage == self._ST_ROI:
            self.btn_fit.config(state='normal')
            self.btn_measure.config(state='disabled')
        elif stage == self._ST_FITTED:
            self.btn_fit.config(state='normal')
            self.btn_measure.config(state='normal')
        elif stage == self._ST_MEASURED:
            self.btn_fit.config(state='normal')
            self.btn_measure.config(state='normal')

    def _fit_edges(self):
        if self.current_idx is None or self.img_orig is None:
            return
        roi = self.rois[self.current_idx]
        try:
            res = measure_rect(
                self.img_orig,
                center=(roi['cx'], roi['cy']),
                len_x=roi['w'],
                len_y=roi['h'],
                angle_deg=roi.get('angle', 0.0),
                width=self.width_var.get(),
                n_lines=self.n_lines_var.get(),
                polarity=self.polarity_var.get(),
                threshold=self.thresh_var.get(),
                thickness=self.thickness_var.get()
            )
        except ValueError:
            messagebox.showinfo('提示', '拟合失败，未找到足够边缘')
            return
        roi['result'] = res
        roi['stage'] = self._ST_FITTED
        self.result_var.set('宽度: - px  高度: - px')
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()

    def _measure_rect(self):
        if self.current_idx is None:
            return
        roi = self.rois[self.current_idx]
        if not (roi.get('result') and roi['stage'] == self._ST_FITTED):
            return
        res = roi['result']
        mm_per_px = self.mm_var.get()
        if mm_per_px > 0:
            w_mm = res['width'] * mm_per_px
            h_mm = res['height'] * mm_per_px
            self.result_var.set(f"宽度: {res['width']:.2f} px / {w_mm:.2f} mm  高度: {res['height']:.2f} px / {h_mm:.2f} mm")
        else:
            self.result_var.set(f"宽度: {res['width']:.2f} px  高度: {res['height']:.2f} px")
        roi['stage'] = self._ST_MEASURED
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()

    # ---------------- 绘制 -----------------
    def _draw_point(self, x: float, y: float, color: str, r: int = 2):
        cx = x * self._zoom + self._offset[0]
        cy = y * self._zoom + self._offset[1]
        self.canvas.create_oval(cx-r, cy-r, cx+r, cy+r, fill=color, outline=color, tags='ov')

    def _draw_overlays(self):
        if self.img_orig is None:
            return
        self._update_canvas_image()
        self.canvas.delete('ov')
        # 绘制已保存 ROI
        for idx, roi in enumerate(self.rois):
            cx, cy, w, h = roi['cx'], roi['cy'], roi['w'], roi['h']
            angle = roi.get('angle',0)
            if abs(angle) < 1e-3:
                x0 = (cx - w/2) * self._zoom + self._offset[0]
                y0 = (cy - h/2) * self._zoom + self._offset[1]
                x1 = (cx + w/2) * self._zoom + self._offset[0]
                y1 = (cy + h/2) * self._zoom + self._offset[1]
                width = 2 if idx == self.current_idx else 1
                self.canvas.create_rectangle(x0, y0, x1, y1, outline='green', width=width, tags='ov')
            else:
                box = cv2.boxPoints(((cx,cy),(w,h),angle))
                pts = [(p[0]*self._zoom+self._offset[0], p[1]*self._zoom+self._offset[1]) for p in box]
                width = 2 if idx == self.current_idx else 1
                for i in range(4):
                    x0c,y0c = pts[i]
                    x1c,y1c = pts[(i+1)%4]
                    self.canvas.create_line(x0c, y0c, x1c, y1c, fill='green', width=width, tags='ov')
            self.canvas.create_text(cx * self._zoom + self._offset[0], cy * self._zoom + self._offset[1],
                                    text=str(roi['id']), fill='green', tags='ov')
            # 如果已有测量结果，绘制边缘点与扫描线
            if roi.get('result') and roi['stage'] in (self._ST_FITTED, self._ST_MEASURED):
                res = roi['result']
                for pt in res.get('edge_pts1', []) + res.get('edge_pts2', []):
                    self._draw_point(pt[0], pt[1], 'red')
                for ln in res.get('scan_lines', []):
                    p0, p1 = ln
                    x0c = p0[0] * self._zoom + self._offset[0]
                    y0c = p0[1] * self._zoom + self._offset[1]
                    x1c = p1[0] * self._zoom + self._offset[0]
                    y1c = p1[1] * self._zoom + self._offset[1]
                    self.canvas.create_line(x0c, y0c, x1c, y1c, fill='yellow', tags='ov')
                # 绘制旋转矩形 - 已禁用显示拟合框，避免与外层ROI框混淆
                # if 'lines_h' in res and 'lines_v' in res:
                #     verts = []
                #     for m_h, c_h in res['lines_h']:
                #         for m_v, c_v in res['lines_v']:
                #             if abs(m_h - m_v) < 1e-6:
                #                 continue  # 理论上不会平行
                #             x = (c_v - c_h) / (m_h - m_v)
                #             y = m_h * x + c_h
                #             verts.append((x, y))
                #     # 去重
                #     verts = list({(round(p[0],2), round(p[1],2)):p for p in verts}.values())
                #     if len(verts) == 4:
                #         # 根据质心排序顶点，顺时针
                #         cx_mean = sum(p[0] for p in verts)/4
                #         cy_mean = sum(p[1] for p in verts)/4
                #         def angle(p):
                #             return np.arctan2(p[1]-cy_mean, p[0]-cx_mean)
                #         verts.sort(key=angle)
                #         pts_canvas = [(p[0]*self._zoom+self._offset[0], p[1]*self._zoom+self._offset[1]) for p in verts]
                #         # 闭合
                #         for i in range(4):
                #             x0c,y0c = pts_canvas[i]
                #             x1c,y1c = pts_canvas[(i+1)%4]
                #             self.canvas.create_line(x0c, y0c, x1c, y1c, fill='blue', width=2, tags='ov')
        # 拖拽实时矩形
        if self.rect_est is not None:
            cx, cy, w, h = self.rect_est
            x0 = (cx - w/2) * self._zoom + self._offset[0]
            y0 = (cy - h/2) * self._zoom + self._offset[1]
            x1 = (cx + w/2) * self._zoom + self._offset[0]
            y1 = (cy + h/2) * self._zoom + self._offset[1]
            self.canvas.create_rectangle(x0, y0, x1, y1, outline='cyan', dash=(4,2), tags='ov')

    def _refresh_tree(self):
        self.tree.delete(*self.tree.get_children())
        for idx, r in enumerate(self.rois):
            w = r['result']['width'] if (r.get('result') and 'width' in r['result']) else '-'
            h = r['result']['height'] if (r.get('result') and 'height' in r['result']) else '-'
            self.tree.insert('', 'end', iid=idx, values=(r['stage'], w, h))

    def _on_tree_select(self, evt):
        sel = self.tree.selection()
        if not sel:
            self.current_idx = None
        else:
            self.current_idx = int(sel[0])
        self._update_btn_state()
        self._draw_overlays()

    # ---------------- YAML -----------------
    # ----------------  配置保存 & 回写配方 -----------------
    def _save_yaml(self):
        if not self.rois:
            messagebox.showwarning('提示','没有 ROI 可保存')
            return
        cfg = {
            'algo': {
                'width': self.width_var.get(),
                'n_lines': self.n_lines_var.get(),
                'threshold': self.thresh_var.get(),
                'thickness': self.thickness_var.get(),
                'polarity': self.polarity_var.get(),
            },
            'mm_per_px': self.mm_var.get(),
        'rois': [
                {'id': r['id'], 'cx': r['cx'], 'cy': r['cy'], 'w': r['w'], 'h': r['h'], 'angle': r.get('angle',0)} for r in self.rois
            ]
        }
        path = filedialog.asksaveasfilename(defaultextension='.yml', initialdir=str(CFG_DIR),
                                            initialfile='default.yml', filetypes=[('YAML','*.yml;*.yaml')])
        if not path:
            return
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(cfg, f, allow_unicode=True, sort_keys=False)

        # 若存在配方路径，尝试写回 mm_per_px
        try:
            if self._recipe_path and self._recipe_path.exists():
                with open(self._recipe_path, 'r', encoding='utf-8') as f:
                    doc = yaml.safe_load(f) or {}
                changed = False
                for step in doc.get('pipeline', []):
                    if isinstance(step, dict) and 'rect_tool' in step:
                        if step['rect_tool'].get('mm_per_px') != self.mm_var.get():
                            step['rect_tool']['mm_per_px'] = float(self.mm_var.get())
                            changed = True
                if changed:
                    yaml.safe_dump(doc, open(self._recipe_path, 'w', encoding='utf-8'), allow_unicode=True, sort_keys=False)
        except Exception as exc:
            print('update recipe mm_per_px failed:', exc)

        messagebox.showinfo('保存成功', f'已保存到 {path}')

    # ---------------- 上游 pose 直接导入 -----------------
    def _load_pose(self, pose: dict):
        """根据上游传入 pose 信息（corners / center / angle）直接生成 ROI。"""
        corners = pose.get('corners')
        if corners and len(corners) == 4:
            import cv2, numpy as np
            box = np.array(corners, dtype=np.float32)
            (cx, cy), (w0, h0), ang = cv2.minAreaRect(box)
            if w0 < h0:
                w, h = h0, w0
                angle = ang + 90
            else:
                w, h = w0, h0
                angle = ang
        else:
            # 退化
            cx, cy = pose.get('center', (0, 0))
            angle = pose.get('angle', 0.0)
            w = self.width_var.get() * 10
            h = self.width_var.get() * 10
        self.rois.clear()
        self.rois.append({'id': 1, 'cx': cx, 'cy': cy, 'w': w, 'h': h, 'angle': angle, 'stage': self._ST_ROI, 'result': None})
        self.next_id = 2; self.current_idx = 0
        self._refresh_tree(); self._update_btn_state(); self._draw_overlays()

    # ---------------- YAML 导入（保留原逻辑） -----------------
    def _import_template(self):
        tpl_path = Path(__file__).resolve().parents[3]/'configs'/'template_match'/'default.yml'
        if not tpl_path.exists():
            messagebox.showwarning('提示','未找到模板匹配配置')
        data = yaml.safe_load(open(tpl_path,'r',encoding='utf-8'))
        info = data.get('detect_info',{})
        corners = info.get('corners')
        if corners and len(corners) == 4:
            import cv2, numpy as np, math
            box = np.array(corners, dtype=np.float32)
            (cx, cy), (w0, h0), ang = cv2.minAreaRect(box)
            # OpenCV: ang in (-90,0]; make w 对应长边
            if w0 < h0:
                w_raw, h_raw = h0, w0
                angle = ang + 90
            else:
                w_raw, h_raw = w0, h0
                angle = ang
            # 使用模板的原始尺寸（模板匹配阶段已完成外扩）
            w = w_raw
            h = h_raw
            print('[DEBUG] minAreaRect', cx, cy, w, h, angle)
        else:
            # 无角点时退化到 center + angle + 默认尺寸
            cx, cy = info.get('center', (0, 0))
            angle = info.get('angle', 0.0)
            w = self.width_var.get()*10
            h = self.width_var.get()*10
            print('[DEBUG] fallback template', cx, cy, w, h, angle)
        self.rois.clear()
        self.rois.append({'id':1,'cx':cx,'cy':cy,'w':w,'h':h,'angle':angle,'stage':self._ST_ROI,'result':None})
        self.next_id=2; self.current_idx=0
        self._refresh_tree(); self._update_btn_state(); self._draw_overlays()

    # ---------------- YAML -----------------
    def _load_yaml_file(self, path: Path):
        """无对话框版本，供自动加载。"""
        data = yaml.safe_load(open(path, 'r', encoding='utf-8')) or {}
        algo = data.get('algo', {})
        self.width_var.set(algo.get('width', 20))
        self.n_lines_var.set(algo.get('n_lines', 10))
        self.thresh_var.set(algo.get('threshold', 5.0))
        self.thickness_var.set(algo.get('thickness', 3))
        self.polarity_var.set(algo.get('polarity', 'both'))
        self.mm_var.set(data.get('mm_per_px', 0.0))
        self.rois.clear()
        for roi in data.get('rois', []):
            self.rois.append({'id': roi['id'], 'cx': roi['cx'], 'cy': roi['cy'], 'w': roi['w'], 'h': roi['h'],
                              'stage': self._ST_ROI, 'result': None, 'angle': roi.get('angle', 0)})
        self.next_id = max([r['id'] for r in self.rois], default=0) + 1
        self.current_idx = 0 if self.rois else None
        self._refresh_tree(); self._update_btn_state(); self._draw_overlays()

    def _load_yaml(self):
        path = filedialog.askopenfilename(filetypes=[('YAML','*.yml;*.yaml')], initialdir=str(CFG_DIR))
        if not path:
            return
        data = yaml.safe_load(open(path, 'r', encoding='utf-8')) or {}
        algo = data.get('algo', {})
        self.width_var.set(algo.get('width',20))
        self.n_lines_var.set(algo.get('n_lines',10))
        self.thresh_var.set(algo.get('threshold',5.0))
        self.thickness_var.set(algo.get('thickness',3))
        self.polarity_var.set(algo.get('polarity','both'))
        self.mm_var.set(data.get('mm_per_px', 0.0))
        self.rois.clear()
        for roi in data.get('rois', []):
            self.rois.append({'id': roi['id'], 'cx': roi['cx'], 'cy': roi['cy'], 'w': roi['w'], 'h': roi['h'],
                              'stage': self._ST_ROI, 'result': None, 'angle': roi.get('angle',0)})
        self.next_id = max([r['id'] for r in self.rois], default=0)+1
        self.current_idx = 0 if self.rois else None
        self._refresh_tree()
        self._update_btn_state()
        self._draw_overlays()

    # ---------------- 缩放/平移 -----------------
    def _on_zoom(self, e):
        if self.img_orig is None:
            return
        factor = 1.1 if e.delta > 0 else 0.9
        self._zoom *= factor
        self._zoom = max(0.1, min(self._zoom, 10.0))
        self._update_canvas_image()
        self._draw_overlays()

    def _on_pan_start(self, e):
        self._pan_start = (e.x, e.y)

    def _on_pan_move(self, e):
        if self._pan_start is None:
            return
        dx = e.x - self._pan_start[0]
        dy = e.y - self._pan_start[1]
        self._pan_start = (e.x, e.y)
        self._offset[0] += dx
        self._offset[1] += dy
        self._update_canvas_image()
        self._draw_overlays()

    # ---------------- 其他 -----------------
    def _clear_marks(self):
        self.rois.clear()
        self.current_idx = None
        self.next_id = 1
        self.tree.delete(*self.tree.get_children())
        self.result_var.set('宽度: - px  高度: - px')
        self._update_btn_state()
        self._draw_overlays()

    # ---------------- 自动流程对外接口 -----------------
    def run_once(self, img: np.ndarray):
        """流水线一次调用：加载图像 -> 导入模板 -> 拟合 -> 测量，返回结果dict。"""
        self.load_image(img)
        self._import_template()
        self._fit_edges()
        self._measure_rect()
        if self.current_idx is not None and self.rois[self.current_idx].get('result'):
            return self.rois[self.current_idx]['result']
        raise RuntimeError('measure failed')

    def _on_close(self):
        try:
            self.destroy()
        finally:
            if self.master and isinstance(self.master, tk.Tk):
                self.master.destroy()


if __name__ == '__main__':
    img_path = sys.argv[1] if len(sys.argv) > 1 else None
    img = cv2.imread(img_path) if img_path else None
    root = tk.Tk(); root.withdraw()
    RectToolUI(root, img)
    root.mainloop()
