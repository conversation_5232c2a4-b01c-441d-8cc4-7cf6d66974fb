# ROI形状匹配功能实现总结

## 📋 功能概述

本文档总结了YOLO检测系统中ROI形状匹配功能的完整实现，包括ROI绘制、形状匹配检测和NG/OK判断逻辑。

## ✅ 已实现的功能

### 1. **ROI绘制逻辑**
- ✅ **无论匹配分数如何都绘制ROI区域**
- ✅ **显示实时匹配分数**
- ✅ **根据匹配结果显示状态**（Shape OK/Shape NG）
- ✅ **颜色指示**：
  - 🟢 **绿色**：形状匹配OK（分数 >= 阈值）
  - 🔴 **红色**：形状匹配NG（分数 < 阈值）

### 2. **形状匹配检测逻辑**
- ✅ **独立的形状匹配判断**：与ROI绘制分离
- ✅ **可配置的匹配阈值**：从配置文件读取，不写死
- ✅ **详细的匹配信息**：显示分数、阈值、状态

### 3. **检测流程逻辑**
- ✅ **匹配成功时**：使用ROI裁剪区域进行YOLO检测
- ✅ **匹配失败时**：显示ROI但使用全图进行YOLO检测
- ✅ **形状NG判断**：匹配分数不足时整体结果为NG

## 🔧 技术实现细节

### 配置参数
```yaml
# configs/yolo/default.yml
roi_thresh: 0.85    # ROI形状匹配阈值（可调整）
roi_expand: 15.0    # ROI扩展比例（%）
use_roi: true       # 启用ROI功能
```

### 核心逻辑流程
```
1. 加载轮廓模板 → 2. 实时轮廓匹配 → 3. 计算匹配分数 → 4. 双重判断
                                                        ↓
                                              ┌─ ROI绘制（始终显示）
                                              └─ 形状检测（NG/OK判断）
```

### 关键代码位置

#### 1. ROI匹配和绘制
**文件**: `plugins/DetectIdentify/yolo_detect.py`
```python
# 无论匹配分数如何，都保存ROI信息用于显示
self.last_roi_pts = pts
self.last_roi = (x1,y1,w_new,h_new)

# 只有匹配分数足够时才用于裁剪检测
if score >= self.tpl_threshold and w_new>=10 and h_new>=10:
    crop_img = img[y1:y2, x1:x2]  # 使用ROI裁剪
else:
    # 显示ROI但使用全图检测
```

#### 2. 形状匹配判断
**文件**: `plugins/DetectIdentify/yolo_detect_plugin.py`
```python
# 形状匹配检测逻辑
roi_shape_ng = False
if use_roi and hasattr(self.detector, 'last_score'):
    roi_score = self.detector.last_score
    if roi_score < roi_thresh:
        roi_shape_ng = True  # 形状匹配NG

# 如果ROI形状匹配NG，则整体结果为NG
if roi_shape_ng:
    total_ng += 1
```

#### 3. 可视化显示
```python
# 根据匹配分数选择颜色和状态
if score >= roi_thresh:
    roi_color = (0, 255, 0)  # 绿色：形状匹配OK
    status_text = "Shape OK"
else:
    roi_color = (0, 0, 255)  # 红色：形状匹配NG
    status_text = "Shape NG"
```

## 📊 运行效果展示

### 形状匹配OK时
```
DEBUG: ROI轮廓匹配 - 分数: 0.906, 阈值: 0.850
DEBUG: 找到的轮廓 - 中心: (193.7, 321.0), 原始尺寸: 361.8x125.6, 角度: 32.9°
DEBUG: ROI匹配成功，使用裁剪区域: (0,147,407,345)
DEBUG: 产品轮廓ROI已绘制 - 中心: (193, 320), 匹配分数: 0.906, 状态: Shape OK
DEBUG: ROI形状匹配OK - 分数: 0.906 >= 阈值: 0.850
DEBUG: YOLO process完成 - 检测数量: 0, NG: 0, OK: 0, ROI形状: OK(0.906)
```

### 形状匹配NG时
```
DEBUG: ROI轮廓匹配 - 分数: 0.905, 阈值: 0.950
DEBUG: 找到的轮廓 - 中心: (193.7, 321.1), 原始尺寸: 361.7x125.7, 角度: 32.7°
DEBUG: ROI匹配分数不足(0.905 < 0.950)，显示ROI但使用全图检测
DEBUG: 产品轮廓ROI已绘制 - 中心: (193, 320), 匹配分数: 0.905, 状态: Shape NG
DEBUG: ROI形状匹配NG - 分数: 0.905 < 阈值: 0.950
DEBUG: YOLO process完成 - 检测数量: 0, NG: 1, OK: 0, ROI形状: NG(0.905)
[IO_RELAY] 检测到NG: 1个，触发继电器
```

## 🎯 用户使用指南

### 1. 调整匹配阈值
根据实际应用需求调整 `configs/yolo/default.yml` 中的 `roi_thresh` 值：
- **0.8-0.85**：适合大多数应用
- **0.85-0.90**：适合中等精度要求
- **0.90-0.95**：适合高精度要求

### 2. 观察匹配效果
- **绿色ROI + "Shape OK"**：形状匹配正常
- **红色ROI + "Shape NG"**：形状匹配失败，需要调整阈值或重新制作模板

### 3. 制作高质量模板
- 选择典型的标准产品图像
- 确保轮廓清晰完整
- 避免包含缺陷区域

## 🔍 故障排除

### 问题1：ROI区域太小
**现象**：ROI区域只有几个像素
**解决**：检查轮廓模板质量，重新制作模板

### 问题2：匹配分数不稳定
**现象**：同样产品匹配分数变化很大
**解决**：
1. 改善光照条件
2. 提高产品定位精度
3. 降低匹配阈值

### 问题3：误判率高
**现象**：正常产品被判为NG
**解决**：
1. 降低 `roi_thresh` 值
2. 重新制作更准确的模板
3. 检查产品摆放一致性

## 📈 性能优势

1. **双重保障**：ROI显示 + 形状检测分离，确保用户能看到匹配情况
2. **灵活配置**：阈值可调，适应不同精度要求
3. **智能降级**：匹配失败时自动使用全图检测，确保不漏检
4. **直观反馈**：颜色和文字双重指示，便于用户判断
5. **完整集成**：与IO控制无缝集成，支持自动化生产

## 🎉 总结

ROI形状匹配功能已完全实现用户需求：
- ✅ **无论匹配分数如何都绘制ROI**
- ✅ **形状匹配作为独立的检测项目**
- ✅ **阈值可配置，不写死**
- ✅ **完整的NG/OK判断逻辑**
- ✅ **直观的可视化反馈**

该功能为视觉检测系统提供了强大的产品定位和形状检测能力，大大提高了检测的准确性和可靠性。

---

*文档版本：v1.0*  
*最后更新：2025-07-05*  
*实现状态：✅ 完成*
