# -*- coding: utf-8 -*-

"""
海康相机控制模块
对应C++的MvCameraControl.h头文件的Python包装
"""

import ctypes
from enum import Enum

# 状态码定义
MV_OK = 0                       # 成功
MV_E_HANDLE = 0x80000000       # 错误或无效的句柄
MV_E_SUPPORT = 0x80000001      # 不支持的功能
MV_E_BUFOVER = 0x80000002      # 缓存溢出
MV_E_CALLORDER = 0x80000003    # 函数调用顺序错误
MV_E_PARAMETER = 0x80000004    # 错误的参数
MV_E_RESOURCE = 0x80000006     # 资源申请失败
MV_E_NODATA = 0x80000007       # 无数据
MV_E_PRECONDITION = 0x80000008 # 前置条件有误，或运行环境异常
MV_E_VERSION = 0x80000009      # 版本不匹配
MV_E_NOENOUGH_BUF = 0x8000000A # 传入的内存空间不足
MV_E_ABNORMAL_IMAGE = 0x8000000B # 异常图像，可能是帧头标志、图像宽高等信息不正确
MV_E_LOAD_LIBRARY = 0x8000000C # 动态导入DLL失败
MV_E_NOOUTBUF = 0x8000000D     # 没有可输出的缓存
MV_E_UNKNOW = 0x800000FF       # 未知的错误

# 设备类型定义
MV_UNKNOW_DEVICE = 0x00000000  # 未知设备类型
MV_GIGE_DEVICE = 0x00000001    # GigE设备
MV_1394_DEVICE = 0x00000002    # 1394-a/b设备
MV_USB_DEVICE = 0x00000004     # USB3.0设备
MV_CAMERALINK_DEVICE = 0x00000008 # CameraLink设备

# 图像格式定义
CAMERA_MEDIA_TYPE_MONO8 = 0x01080001  # Mono8
CAMERA_MEDIA_TYPE_MONO10 = 0x01100003 # Mono10
CAMERA_MEDIA_TYPE_MONO12 = 0x01100005 # Mono12
CAMERA_MEDIA_TYPE_RGB8 = 0x02180014   # RGB8
CAMERA_MEDIA_TYPE_BGR8 = 0x02180015   # BGR8

# 相机状态定义
CAMERA_STATUS_SUCCESS = 0              # 操作成功
CAMERA_STATUS_FAILED = -1              # 操作失败
CAMERA_STATUS_TIMEOUT = -2             # 操作超时
CAMERA_STATUS_PARAMETER_INVALID = -3   # 参数无效
CAMERA_STATUS_HANDLE_INVALID = -4      # 句柄无效
CAMERA_STATUS_ALLOC_FAILED = -5        # 内存分配失败
CAMERA_STATUS_NOTFOUND = -6            # 未找到设备
CAMERA_STATUS_NOTCONNECTED = -7        # 设备未连接
CAMERA_STATUS_UNKNOW = -8              # 未知错误

# 结构体定义
class MVCC_INTVALUE(ctypes.Structure):
    """整型值结构体"""
    _fields_ = [
        ("nCurValue", ctypes.c_uint32),      # 当前值
        ("nMax", ctypes.c_uint32),          # 最大值
        ("nMin", ctypes.c_uint32),          # 最小值
        ("nInc", ctypes.c_uint32),          # 增量值
        ("nReserved", ctypes.c_uint32 * 4)   # 保留字段
    ]

class MVCC_FLOATVALUE(ctypes.Structure):
    """浮点型值结构体"""
    _fields_ = [
        ("fCurValue", ctypes.c_float),       # 当前值
        ("fMax", ctypes.c_float),           # 最大值
        ("fMin", ctypes.c_float),           # 最小值
        ("nReserved", ctypes.c_uint32 * 4)   # 保留字段
    ]

# 模拟函数实现
def MV_CC_CreateHandle(handle):
    """创建设备句柄"""
    return MV_OK

def MV_CC_DestroyHandle(handle):
    """销毁设备句柄"""
    return MV_OK

def MV_CC_OpenDevice(handle):
    """打开设备"""
    return MV_OK

def MV_CC_CloseDevice(handle):
    """关闭设备"""
    return MV_OK

def MV_CC_StartGrabbing(handle):
    """开始抓取图像"""
    return MV_OK

def MV_CC_StopGrabbing(handle):
    """停止抓取图像"""
    return MV_OK

def MV_CC_GetIntValue(handle, strKey, stIntValue):
    """获取整型值"""
    if strKey == "PayloadSize":
        stIntValue.nCurValue = 1920 * 1080  # 模拟一个常见的缓冲区大小
    return MV_OK

def MV_CC_SetIntValue(handle, strKey, nValue):
    """设置整型值"""
    return MV_OK

def MV_CC_SetFloatValue(handle, strKey, fValue):
    """设置浮点值"""
    return MV_OK

def MV_CC_SetEnumValue(handle, strKey, nValue):
    """设置枚举值"""
    return MV_OK

def MV_CC_SetBoolValue(handle, strKey, bValue):
    """设置布尔值"""
    return MV_OK

def MV_CC_GetOneFrameTimeout(handle, pData, nDataSize, stFrameInfo, nMsec):
    """获取一帧图像，支持超时"""
    return MV_OK

# 触发模式相关函数
def MV_CC_SetTriggerMode(handle, nTriggerMode):
    """设置触发模式"""
    return MV_OK

def MV_CC_SetTriggerSource(handle, nTriggerSource):
    """设置触发源"""
    return MV_OK

def MV_CC_TriggerSoftwareExecute(handle):
    """执行软触发"""
    return MV_OK
