"""
高精度像素标定算法模块
支持多种标定方法：棋盘格、标准产品、多重验证
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import time


class HighPrecisionCalibration:
    """高精度标定系统"""
    
    def __init__(self):
        self.calibration_methods = [
            'chessboard',           # 棋盘格标定
            'standard_product',     # 标准产品标定
            'multi_validation'      # 多重验证标定
        ]
        
    def calibrate(self, image, method: str, reference_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行标定"""
        
        if method == 'chessboard':
            return self.chessboard_calibration(image, reference_data)
        elif method == 'standard_product':
            return self.standard_product_calibration(image, reference_data)
        elif method == 'multi_validation':
            return self.multi_validation_calibration(image, reference_data)
        else:
            return {'success': False, 'error': f'不支持的标定方法: {method}'}
    
    def chessboard_calibration(self, image, reference_data: Dict[str, Any]) -> Dict[str, Any]:
        """棋盘格标定（保持兼容现有方法）"""
        try:
            from scripts.calc_mm_per_px import calc_mm_per_px
            
            square_size = reference_data.get('square_size_mm', 1.0)
            pattern = (reference_data.get('cols', 11), reference_data.get('rows', 8))
            
            # 如果传入的是图像路径，直接使用；否则保存临时图像
            if isinstance(image, str):
                img_path = image
            else:
                import tempfile
                import os
                temp_dir = tempfile.gettempdir()
                img_path = os.path.join(temp_dir, 'temp_calibration.jpg')
                cv2.imwrite(img_path, image)
            
            mm_per_px, px_per_square = calc_mm_per_px(img_path, square_size, pattern)
            
            return {
                'success': True,
                'mm_per_px': mm_per_px,
                'method': 'chessboard',
                'confidence': 0.9,
                'details': {
                    'px_per_square': px_per_square,
                    'square_size_mm': square_size,
                    'pattern': pattern
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': f'棋盘格标定失败: {str(e)}'}
    
    def standard_product_calibration(self, image, reference_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准产品标定"""
        try:
            # 处理图像输入（支持路径或图像数组）
            if isinstance(image, str):
                import cv2
                img_array = cv2.imread(image)
                if img_array is None:
                    return {'success': False, 'error': f'无法加载图像: {image}'}
                img_path = image
            else:
                img_array = image
                img_path = "内存图像"

            # 获取图像尺寸信息
            img_height, img_width = img_array.shape[:2]
            img_info = {
                'width_px': img_width,
                'height_px': img_height,
                'total_pixels': img_width * img_height,
                'aspect_ratio': img_width / img_height,
                'source': img_path
            }

            print(f"[DEBUG] 图像信息: {img_width}×{img_height} 像素, 宽高比: {img_info['aspect_ratio']:.3f}")

            # 检测产品特征
            features = self.detect_product_features(img_array)

            if not features['detection_success']:
                return {
                    'success': False,
                    'error': features.get('error', '产品检测失败'),
                    'image_info': img_info
                }

            # 添加图像信息到特征中
            features['image_info'] = img_info

            # 计算多个特征的像素比例
            calibration_candidates = []

            # 基于总长度标定
            if 'total_length_px' in features and 'length_mm' in reference_data:
                mm_per_px_length = reference_data['length_mm'] / features['total_length_px']
                calibration_candidates.append({
                    'mm_per_px': mm_per_px_length,
                    'feature': 'total_length',
                    'reliability': 0.9,
                    'pixel_measurement': features['total_length_px'],
                    'reference_measurement': reference_data['length_mm']
                })
                print(f"[DEBUG] 长度标定: {features['total_length_px']:.2f}px → {reference_data['length_mm']}mm = {mm_per_px_length:.6f} mm/px")

            # 基于总宽度标定
            if 'total_width_px' in features and 'width_mm' in reference_data:
                mm_per_px_width = reference_data['width_mm'] / features['total_width_px']
                calibration_candidates.append({
                    'mm_per_px': mm_per_px_width,
                    'feature': 'total_width',
                    'reliability': 0.9,
                    'pixel_measurement': features['total_width_px'],
                    'reference_measurement': reference_data['width_mm']
                })
                print(f"[DEBUG] 宽度标定: {features['total_width_px']:.2f}px → {reference_data['width_mm']}mm = {mm_per_px_width:.6f} mm/px")

            if not calibration_candidates:
                return {
                    'success': False,
                    'error': '未找到可用的标定特征，请检查参考尺寸设置',
                    'features_detected': features,
                    'image_info': img_info
                }

            # 验证标定一致性并融合结果
            result = self.validate_and_fuse_calibrations(calibration_candidates)

            if result['success']:
                result['method'] = 'standard_product'
                result['features_detected'] = features
                result['image_info'] = img_info

                # 添加详细的标定报告
                result['calibration_report'] = self.generate_calibration_report(result, reference_data)

            return result

        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            print(f"[ERROR] 标准产品标定异常: {error_detail}")
            return {'success': False, 'error': f'标准产品标定失败: {str(e)}'}
    
    def detect_product_features(self, image, use_advanced_processing=True) -> Dict[str, Any]:
        """检测产品特征 - 角度无关，增强版，支持高级图像处理"""

        try:
            # 预处理 - 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            print(f"[DEBUG] 输入图像尺寸: {gray.shape}")

            # 高级图像处理流程
            if use_advanced_processing:
                processed_img = self._apply_advanced_processing(gray)
                print(f"[DEBUG] 应用高级图像处理")
            else:
                processed_img = gray

            # 多种二值化方法尝试
            binary_methods = []

            # 方法1：固定阈值
            _, binary1 = cv2.threshold(processed_img, 127, 255, cv2.THRESH_BINARY)
            binary_methods.append(('fixed_127', binary1))

            # 方法2：OTSU自适应阈值
            _, binary2 = cv2.threshold(processed_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            binary_methods.append(('otsu', binary2))

            # 方法3：自适应阈值
            binary3 = cv2.adaptiveThreshold(processed_img, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY, 11, 2)
            binary_methods.append(('adaptive', binary3))

            # 方法4：基于处理后图像的增强二值化
            if use_advanced_processing:
                enhanced_binary = self._enhanced_binarization(processed_img)
                binary_methods.append(('enhanced', enhanced_binary))

            # 尝试每种二值化方法
            best_result = None
            best_score = 0

            for method_name, binary in binary_methods:
                print(f"[DEBUG] 尝试二值化方法: {method_name}")

                # 检测轮廓
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

                if not contours:
                    print(f"[DEBUG] {method_name}: 未检测到轮廓")
                    continue

                # 选择最大轮廓
                main_contour = max(contours, key=cv2.contourArea)
                contour_area = cv2.contourArea(main_contour)

                # 计算轮廓质量分数
                img_area = gray.shape[0] * gray.shape[1]
                area_ratio = contour_area / img_area

                # 好的轮廓应该占图像面积的合理比例（10%-80%）
                if 0.1 <= area_ratio <= 0.8:
                    score = area_ratio * len(main_contour)  # 面积比例 × 轮廓点数
                    print(f"[DEBUG] {method_name}: 轮廓面积={contour_area:.0f}, 面积比例={area_ratio:.3f}, 分数={score:.0f}")

                    if score > best_score:
                        best_score = score
                        best_result = {
                            'method': method_name,
                            'binary': binary,
                            'contour': main_contour,
                            'area': contour_area,
                            'area_ratio': area_ratio
                        }
                else:
                    print(f"[DEBUG] {method_name}: 轮廓面积比例不合理 ({area_ratio:.3f})")

            if best_result is None:
                return {'detection_success': False, 'error': '所有二值化方法都未检测到合适的轮廓'}

            print(f"[DEBUG] 选择最佳方法: {best_result['method']}")
            main_contour = best_result['contour']

            # 获取最小外接矩形
            rect = cv2.minAreaRect(main_contour)
            (center_x, center_y), (width, height), angle = rect

            # 确保length > width
            length_px = max(width, height)
            width_px = min(width, height)

            # 计算更多特征
            contour_perimeter = cv2.arcLength(main_contour, True)

            # 计算轮廓的紧密度（面积/周长²）
            compactness = 4 * np.pi * best_result['area'] / (contour_perimeter ** 2) if contour_perimeter > 0 else 0

            features = {
                'detection_success': True,
                'total_length_px': length_px,
                'total_width_px': width_px,
                'center': (center_x, center_y),
                'rotation_angle': angle,
                'contour_area': best_result['area'],
                'contour_perimeter': contour_perimeter,
                'area_ratio': best_result['area_ratio'],
                'compactness': compactness,
                'binary_method': best_result['method'],
                'contour_points': len(main_contour),
                'bounding_rect': cv2.boundingRect(main_contour)
            }

            print(f"[DEBUG] 检测结果: 长度={length_px:.2f}px, 宽度={width_px:.2f}px, 角度={angle:.1f}°")

            return features

        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            print(f"[ERROR] 特征检测异常: {error_detail}")
            return {'detection_success': False, 'error': f'特征检测失败: {str(e)}'}
    
    def validate_and_fuse_calibrations(self, candidates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证标定一致性并融合结果"""
        
        if len(candidates) < 1:
            return {'success': False, 'error': '没有有效的标定候选'}
        
        if len(candidates) == 1:
            # 只有一个候选，直接返回
            return {
                'success': True,
                'mm_per_px': candidates[0]['mm_per_px'],
                'confidence': candidates[0]['reliability'],
                'used_features': [candidates[0]['feature']],
                'calibration_details': candidates
            }
        
        # 计算各候选值的差异
        mm_per_px_values = [c['mm_per_px'] for c in candidates]
        mean_value = np.mean(mm_per_px_values)
        max_deviation = max([abs(v - mean_value) for v in mm_per_px_values])
        relative_deviation = max_deviation / mean_value
        
        # 一致性检查
        if relative_deviation > 0.02:  # 2%阈值
            return {
                'success': False, 
                'error': f'标定不一致，最大偏差{relative_deviation*100:.1f}%',
                'candidates': candidates,
                'deviation_percent': relative_deviation * 100
            }
        
        # 加权融合
        weights = [c['reliability'] for c in candidates]
        weighted_mm_per_px = np.average(mm_per_px_values, weights=weights)
        
        # 计算置信度
        confidence = 1.0 - relative_deviation  # 偏差越小置信度越高
        
        return {
            'success': True,
            'mm_per_px': weighted_mm_per_px,
            'confidence': confidence,
            'max_deviation_percent': relative_deviation * 100,
            'used_features': [c['feature'] for c in candidates],
            'calibration_details': candidates,
            'uncertainty': {
                'absolute_uncertainty': max_deviation,
                'relative_uncertainty_percent': relative_deviation * 100
            }
        }
    
    def multi_validation_calibration(self, images, reference_data: Dict[str, Any]) -> Dict[str, Any]:
        """多重验证标定"""
        
        if not isinstance(images, list):
            images = [images]
        
        calibration_results = []
        
        for i, image in enumerate(images):
            print(f"处理第 {i+1}/{len(images)} 个标定样本...")
            
            result = self.standard_product_calibration(image, reference_data)
            
            if result['success'] and result['confidence'] > 0.8:  # 只接受高置信度结果
                calibration_results.append({
                    'image_index': i,
                    'mm_per_px': result['mm_per_px'],
                    'confidence': result['confidence'],
                    'method': result['method']
                })
        
        if len(calibration_results) < 2:
            return {'success': False, 'error': '有效标定样本不足（需要至少2个）'}
        
        # 计算最终标定值
        mm_per_px_values = [r['mm_per_px'] for r in calibration_results]
        
        final_mm_per_px = np.mean(mm_per_px_values)
        calibration_std = np.std(mm_per_px_values)
        calibration_cv = calibration_std / final_mm_per_px  # 变异系数
        
        # 评估重现性
        if calibration_cv < 0.005:
            repeatability = 'excellent'
        elif calibration_cv < 0.01:
            repeatability = 'good'
        else:
            repeatability = 'acceptable'
        
        return {
            'success': True,
            'mm_per_px': final_mm_per_px,
            'method': 'multi_validation',
            'confidence': 1.0 - calibration_cv,  # 变异系数越小置信度越高
            'standard_deviation': calibration_std,
            'coefficient_of_variation_percent': calibration_cv * 100,
            'samples_used': len(calibration_results),
            'repeatability': repeatability,
            'sample_results': calibration_results
        }

    def generate_calibration_report(self, calibration_result: Dict[str, Any], reference_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成详细的标定报告"""

        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'calibration_summary': {
                'mm_per_px': calibration_result['mm_per_px'],
                'method': calibration_result['method'],
                'confidence': calibration_result['confidence'],
                'features_used': calibration_result['used_features']
            },
            'image_analysis': {},
            'measurement_details': {},
            'quality_assessment': {}
        }

        # 图像分析
        if 'image_info' in calibration_result:
            img_info = calibration_result['image_info']
            report['image_analysis'] = {
                'resolution': f"{img_info['width_px']}×{img_info['height_px']}",
                'total_pixels': img_info['total_pixels'],
                'aspect_ratio': round(img_info['aspect_ratio'], 3),
                'source': img_info['source']
            }

        # 测量详情
        if 'calibration_details' in calibration_result:
            details = calibration_result['calibration_details']
            for detail in details:
                feature_name = detail['feature']
                report['measurement_details'][feature_name] = {
                    'pixel_measurement': round(detail['pixel_measurement'], 2),
                    'reference_mm': detail['reference_measurement'],
                    'calculated_mm_per_px': round(detail['mm_per_px'], 6),
                    'reliability': detail['reliability']
                }

        # 质量评估
        if 'features_detected' in calibration_result:
            features = calibration_result['features_detected']
            report['quality_assessment'] = {
                'contour_area_ratio': round(features.get('area_ratio', 0), 3),
                'compactness': round(features.get('compactness', 0), 3),
                'binary_method': features.get('binary_method', 'unknown'),
                'contour_points': features.get('contour_points', 0),
                'rotation_angle': round(features.get('rotation_angle', 0), 1)
            }

        # 精度评估
        if 'uncertainty' in calibration_result:
            uncertainty = calibration_result['uncertainty']
            report['accuracy_assessment'] = {
                'absolute_uncertainty_mm_per_px': round(uncertainty['absolute_uncertainty'], 8),
                'relative_uncertainty_percent': round(uncertainty['relative_uncertainty_percent'], 3),
                'estimated_measurement_error_per_100px_um': round(uncertainty['absolute_uncertainty'] * 100 * 1000, 2)
            }

        return report

    def _apply_advanced_processing(self, gray_image):
        """应用高级图像处理流程，模拟检测流程中的预处理"""

        try:
            # 1. 噪声去除 - 中值滤波
            denoised = cv2.medianBlur(gray_image, 3)
            print(f"[DEBUG] 应用中值滤波去噪")

            # 2. 高斯滤波平滑
            smoothed = cv2.GaussianBlur(denoised, (3, 3), 0)
            print(f"[DEBUG] 应用高斯滤波平滑")

            # 3. 对比度增强 - CLAHE (限制对比度自适应直方图均衡化)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(smoothed)
            print(f"[DEBUG] 应用CLAHE对比度增强")

            # 4. 边缘保护的双边滤波
            bilateral = cv2.bilateralFilter(enhanced, 9, 75, 75)
            print(f"[DEBUG] 应用双边滤波")

            # 5. 可选：锐化处理
            kernel_sharpen = np.array([[-1,-1,-1],
                                     [-1, 9,-1],
                                     [-1,-1,-1]])
            sharpened = cv2.filter2D(bilateral, -1, kernel_sharpen)
            print(f"[DEBUG] 应用锐化处理")

            return sharpened

        except Exception as e:
            print(f"[DEBUG] 高级图像处理失败，使用原始图像: {e}")
            return gray_image

    def _enhanced_binarization(self, processed_image):
        """基于处理后图像的增强二值化"""

        try:
            # 1. 多尺度形态学处理
            kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))

            # 开运算去除小噪声
            opened = cv2.morphologyEx(processed_image, cv2.MORPH_OPEN, kernel_small)

            # 闭运算填充小孔洞
            closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel_large)

            # 2. 自适应阈值 + 形态学后处理
            binary = cv2.adaptiveThreshold(closed, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 15, 2)

            # 3. 形态学清理
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_small)
            final = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_small)

            print(f"[DEBUG] 增强二值化处理完成")
            return final

        except Exception as e:
            print(f"[DEBUG] 增强二值化失败，使用简单阈值: {e}")
            _, simple_binary = cv2.threshold(processed_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            return simple_binary

    def apply_pipeline_processing(self, image, processing_params=None):
        """应用与检测流程相同的图像处理参数"""

        try:
            if processing_params is None:
                # 使用默认的处理参数
                processing_params = {
                    'median_kernel': 3,
                    'gaussian_kernel': 3,
                    'gaussian_sigma': 0,
                    'clahe_clip_limit': 2.0,
                    'clahe_tile_size': 8,
                    'bilateral_d': 9,
                    'bilateral_sigma_color': 75,
                    'bilateral_sigma_space': 75,
                    'enable_sharpening': True,
                    'morphology_kernel_size': 3
                }

            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            processed = gray

            # 1. 中值滤波去噪
            if processing_params.get('median_kernel', 0) > 0:
                kernel_size = processing_params['median_kernel']
                processed = cv2.medianBlur(processed, kernel_size)
                print(f"[PIPELINE] 中值滤波: kernel={kernel_size}")

            # 2. 高斯滤波
            if processing_params.get('gaussian_kernel', 0) > 0:
                kernel_size = processing_params['gaussian_kernel']
                sigma = processing_params.get('gaussian_sigma', 0)
                processed = cv2.GaussianBlur(processed, (kernel_size, kernel_size), sigma)
                print(f"[PIPELINE] 高斯滤波: kernel={kernel_size}, sigma={sigma}")

            # 3. CLAHE对比度增强
            if processing_params.get('clahe_clip_limit', 0) > 0:
                clip_limit = processing_params['clahe_clip_limit']
                tile_size = processing_params.get('clahe_tile_size', 8)
                clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=(tile_size, tile_size))
                processed = clahe.apply(processed)
                print(f"[PIPELINE] CLAHE: clip_limit={clip_limit}, tile_size={tile_size}")

            # 4. 双边滤波
            if processing_params.get('bilateral_d', 0) > 0:
                d = processing_params['bilateral_d']
                sigma_color = processing_params.get('bilateral_sigma_color', 75)
                sigma_space = processing_params.get('bilateral_sigma_space', 75)
                processed = cv2.bilateralFilter(processed, d, sigma_color, sigma_space)
                print(f"[PIPELINE] 双边滤波: d={d}, sigma_color={sigma_color}, sigma_space={sigma_space}")

            # 5. 锐化处理
            if processing_params.get('enable_sharpening', False):
                kernel_sharpen = np.array([[-1,-1,-1], [-1, 9,-1], [-1,-1,-1]])
                processed = cv2.filter2D(processed, -1, kernel_sharpen)
                print(f"[PIPELINE] 锐化处理")

            # 6. 形态学处理
            kernel_size = processing_params.get('morphology_kernel_size', 0)
            if kernel_size > 0:
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
                processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel)
                processed = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)
                print(f"[PIPELINE] 形态学处理: kernel_size={kernel_size}")

            print(f"[PIPELINE] 图像处理流程完成")
            return processed

        except Exception as e:
            print(f"[PIPELINE] 图像处理失败: {e}")
            return image


def validate_image_consistency(calibration_image_path: str, current_image_path: str) -> Dict[str, Any]:
    """
    验证标定图像与当前采集图像的像素尺寸一致性

    Args:
        calibration_image_path: 标定时使用的图像路径
        current_image_path: 当前采集的图像路径

    Returns:
        验证结果字典
    """
    try:
        import cv2

        # 加载图像
        calib_img = cv2.imread(calibration_image_path)
        current_img = cv2.imread(current_image_path)

        if calib_img is None:
            return {'success': False, 'error': f'无法加载标定图像: {calibration_image_path}'}

        if current_img is None:
            return {'success': False, 'error': f'无法加载当前图像: {current_image_path}'}

        # 获取尺寸
        calib_h, calib_w = calib_img.shape[:2]
        current_h, current_w = current_img.shape[:2]

        # 比较尺寸
        size_match = (calib_w == current_w) and (calib_h == current_h)

        # 计算宽高比
        calib_ratio = calib_w / calib_h
        current_ratio = current_w / current_h
        ratio_diff = abs(calib_ratio - current_ratio) / calib_ratio

        result = {
            'success': True,
            'size_match': size_match,
            'calibration_image': {
                'width': calib_w,
                'height': calib_h,
                'aspect_ratio': round(calib_ratio, 4),
                'total_pixels': calib_w * calib_h
            },
            'current_image': {
                'width': current_w,
                'height': current_h,
                'aspect_ratio': round(current_ratio, 4),
                'total_pixels': current_w * current_h
            },
            'comparison': {
                'width_diff': current_w - calib_w,
                'height_diff': current_h - calib_h,
                'aspect_ratio_diff_percent': round(ratio_diff * 100, 3),
                'pixel_count_diff': (current_w * current_h) - (calib_w * calib_h)
            }
        }

        # 评估一致性
        if size_match:
            result['consistency'] = 'perfect'
            result['recommendation'] = '图像尺寸完全一致，标定有效'
        elif ratio_diff < 0.01:  # 1%以内的宽高比差异
            result['consistency'] = 'acceptable'
            result['recommendation'] = '图像尺寸略有差异但宽高比接近，标定基本有效'
        else:
            result['consistency'] = 'poor'
            result['recommendation'] = '图像尺寸差异较大，建议重新标定'

        return result

    except Exception as e:
        return {'success': False, 'error': f'图像一致性验证失败: {str(e)}'}


def get_image_info(image_input) -> Dict[str, Any]:
    """
    获取图像的详细信息

    Args:
        image_input: 图像路径(str) 或 numpy数组

    Returns:
        图像信息字典
    """
    try:
        import cv2
        import os
        import numpy as np

        # 判断输入类型
        if isinstance(image_input, str):
            # 文件路径输入
            image_path = image_input
            if not os.path.exists(image_path):
                return {'success': False, 'error': f'图像文件不存在: {image_path}'}

            # 加载图像
            img = cv2.imread(image_path)
            if img is None:
                return {'success': False, 'error': f'无法加载图像: {image_path}'}

            # 获取文件信息
            file_size = os.path.getsize(image_path)
            file_ext = os.path.splitext(image_path)[1].lower()

            file_info = {
                'path': image_path,
                'size_bytes': file_size,
                'size_mb': round(file_size / (1024 * 1024), 2),
                'extension': file_ext
            }

        elif isinstance(image_input, np.ndarray):
            # numpy数组输入
            img = image_input
            file_info = {
                'path': '流程图像',
                'size_bytes': img.nbytes,
                'size_mb': round(img.nbytes / (1024 * 1024), 2),
                'extension': 'array'
            }

        else:
            return {'success': False, 'error': f'不支持的图像输入类型: {type(image_input)}'}

        # 获取图像信息
        height, width = img.shape[:2]
        channels = img.shape[2] if len(img.shape) == 3 else 1

        return {
            'success': True,
            'file_info': file_info,
            'image_properties': {
                'width': width,
                'height': height,
                'channels': channels,
                'total_pixels': width * height,
                'aspect_ratio': round(width / height, 4),
                'dtype': str(img.dtype)
            },
            'resolution_category': classify_resolution(width, height)
        }

    except Exception as e:
        return {'success': False, 'error': f'获取图像信息失败: {str(e)}'}


def classify_resolution(width: int, height: int) -> str:
    """分类图像分辨率"""
    total_pixels = width * height

    if total_pixels >= 8000000:  # 8MP+
        return 'high_resolution'
    elif total_pixels >= 2000000:  # 2MP+
        return 'medium_resolution'
    elif total_pixels >= 500000:   # 0.5MP+
        return 'standard_resolution'
    else:
        return 'low_resolution'


# 便捷函数
def calibrate_from_clean_binary_image(image, reference_dimensions: Dict[str, Any]) -> Dict[str, Any]:
    """
    基于处理后的二值图像进行高精度标定
    
    Args:
        image: 处理后的二值图像
        reference_dimensions: 参考尺寸 {'length_mm': 25.0, 'width_mm': 15.0}
    
    Returns:
        标定结果字典
    """
    calibrator = HighPrecisionCalibration()
    return calibrator.standard_product_calibration(image, reference_dimensions)


def calibrate_multi_samples(images: List, reference_dimensions: Dict[str, Any]) -> Dict[str, Any]:
    """
    多样本标定验证

    Args:
        images: 图像列表
        reference_dimensions: 参考尺寸

    Returns:
        标定结果字典
    """
    calibrator = HighPrecisionCalibration()
    return calibrator.multi_validation_calibration(images, reference_dimensions)


def calibrate_with_pipeline_processing(image_path: str, reference_dimensions: Dict[str, float],
                                     pipeline_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    使用与检测流程相同的图像处理进行标定

    Args:
        image_path: 图像路径
        reference_dimensions: 参考尺寸
        pipeline_config: 流程配置参数

    Returns:
        标定结果字典
    """

    # 默认的流程处理参数（模拟常用的检测流程）
    default_processing = {
        'median_kernel': 3,        # 中值滤波核大小
        'gaussian_kernel': 3,      # 高斯滤波核大小
        'gaussian_sigma': 0.8,     # 高斯滤波标准差
        'clahe_clip_limit': 2.0,   # CLAHE限制对比度
        'clahe_tile_size': 8,      # CLAHE瓦片大小
        'bilateral_d': 9,          # 双边滤波直径
        'bilateral_sigma_color': 75,    # 双边滤波颜色标准差
        'bilateral_sigma_space': 75,    # 双边滤波空间标准差
        'enable_sharpening': True,      # 启用锐化
        'morphology_kernel_size': 3,    # 形态学核大小
    }

    # 合并用户配置
    if pipeline_config:
        default_processing.update(pipeline_config)

    print(f"[CALIB] 使用流程处理参数进行标定: {default_processing}")

    try:
        import cv2

        # 加载原始图像
        original_image = cv2.imread(image_path)
        if original_image is None:
            return {'success': False, 'error': f'无法加载图像: {image_path}'}

        # 应用处理流程
        calibrator = HighPrecisionCalibration()
        processed_image = calibrator.apply_pipeline_processing(original_image, default_processing)

        # 使用处理后的图像进行标定
        return calibrator.standard_product_calibration(processed_image, reference_dimensions)

    except Exception as e:
        return {'success': False, 'error': f'流程处理标定失败: {str(e)}'}


def calibrate_from_processed_image(image_path: str, reference_dimensions: Dict[str, float]) -> Dict[str, Any]:
    """
    从已处理的图像进行标定（推荐用于生产环境）

    这个函数假设输入的图像已经经过了与检测流程相同的预处理，
    包括去噪、对比度增强、滤波等操作。

    Args:
        image_path: 已处理图像的路径
        reference_dimensions: 参考尺寸 {'length_mm': 25.0, 'width_mm': 15.0}

    Returns:
        标定结果字典
    """
    return calibrate_from_clean_binary_image(image_path, reference_dimensions)
