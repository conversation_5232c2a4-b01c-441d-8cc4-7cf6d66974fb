import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple


class BlobAnalyzer:
    """Core blob analysis logic (threshold + contour filter) independent of UI."""

    def __init__(self):
        # threshold params
        self.mode = 'otsu'  # 'otsu'|'fixed'|'adaptive'
        self.fixed_th = 128
        self.adapt_block = 31
        self.adapt_C = 5
        # area filter
        self.min_area = 50
        self.max_area = 0  # 0 means no max
        # ROI template (same style as YOLO template)
        self.tpl_cnt: Optional[np.ndarray] = None
        self.tpl_thresh = 0.8
        self.roi_expand = 0.1
        # runtime info
        self.last_roi_pts: Optional[np.ndarray] = None
        self.last_score: Optional[float] = None

    # ---------------- template handling -----------------
    def make_template_from_image(self, img: np.ndarray, threshold: float = 0.8):
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim == 3 else img
        _, bin_img = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        cnts, _ = cv2.findContours(bin_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not cnts:
            raise ValueError('未找到外轮廓')
        cnt = max(cnts, key=cv2.contourArea)
        self.tpl_cnt = cnt
        self.tpl_thresh = threshold

    # ---------------- detection -----------------
    def analyze(self, img: np.ndarray, use_roi: bool = False) -> Dict[str, Any]:
        crop = img
        offset = (0, 0)
        self.last_roi_pts = None
        self.last_score = None
        if use_roi and self.tpl_cnt is not None:
            # match shape using cv2.matchShapes brute rotation? Use contour match on binary
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim == 3 else img
            _, bin_img = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            cnts, _ = cv2.findContours(bin_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            best_cnt, best_score = None, 1e9
            for c in cnts:
                s = cv2.matchShapes(self.tpl_cnt, c, cv2.CONTOURS_MATCH_I1, 0)
                if s < best_score:
                    best_score, best_cnt = s, c
            self.last_score = 1 - best_score  # lower is better; convert to similarity-like
            if best_cnt is not None and self.last_score >= self.tpl_thresh:
                rect = cv2.minAreaRect(best_cnt)
                (cx, cy), (w0, h0), angle = rect
                scale = 1.0 + self.roi_expand
                rect = ((cx, cy), (w0 * scale, h0 * scale), angle)
                pts = cv2.boxPoints(rect).astype(int)
                x, y, w, h = cv2.boundingRect(pts)
                H, W = img.shape[:2]
                x1 = max(0, x); y1 = max(0, y)
                x2 = min(W, x + w); y2 = min(H, y + h)
                if (x2 - x1) >= 2 and (y2 - y1) >= 2:
                    crop = img[y1:y2, x1:x2]
                    offset = (x1, y1)
                    self.last_roi_pts = pts
        gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY) if crop.ndim == 3 else crop
        # threshold
        if self.mode == 'otsu':
            _, bin_img = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif self.mode == 'fixed':
            _, bin_img = cv2.threshold(gray, self.fixed_th, 255, cv2.THRESH_BINARY)
        else:
            block = self.adapt_block | 1
            bin_img = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                             cv2.THRESH_BINARY, block, self.adapt_C)
        contours, _ = cv2.findContours(bin_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        sel = []
        for c in contours:
            a = cv2.contourArea(c)
            if a < self.min_area: continue
            if self.max_area > 0 and a > self.max_area: continue
            sel.append(c)
        # offset back boxes
        results: List[Dict[str, Any]] = []
        ox, oy = offset
        for c in sel:
            x, y, w, h = cv2.boundingRect(c)
            results.append({'box': [x+ox, y+oy, x+ox+w, y+oy+h],
                            'area': cv2.contourArea(c)})
        stats = {
            'count': len(sel),
            'avg_area': float(np.mean([r['area'] for r in results])) if results else 0.0,
            'results': results
        }
        return stats


def load_analyzer() -> BlobAnalyzer:
    return BlobAnalyzer()
