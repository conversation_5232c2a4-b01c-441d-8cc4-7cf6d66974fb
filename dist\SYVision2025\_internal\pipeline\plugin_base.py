"""Plugin framework definitions.

Any image-processing plugin should subclass `PluginBase`. Subclasses are
registered automatically into the global `PLUGIN_REGISTRY` using class
creation hooks.  The registry maps plugin *name* (ascii, unique) to the
implementation class, for dynamic loading.

Example:

    class GaussianBlur(PluginBase):
        name = "blur"
        label = "高斯模糊"
        params = {"ksize": 5}

        def process(self, img, ctx):
            k = int(self.params.get("ksize", 5))
            return cv2.GaussianBlur(img, (k, k), 0), ctx
"""
from __future__ import annotations

from typing import Dict, Any, Type, Callable

PLUGIN_REGISTRY: Dict[str, Type["PluginBase"]] = {}


def register_plugin(cls: Type["PluginBase"]):
    """Add class into global registry."""
    if not cls.name:
        raise ValueError("Plugin class missing 'name' attribute")
    if cls.name in PLUGIN_REGISTRY:
        raise ValueError(f"Duplicated plugin name: {cls.name}")
    PLUGIN_REGISTRY[cls.name] = cls
    return cls


class MetaRegister(type):
    def __init__(cls, name, bases, attrs):  # noqa: D401
        super().__init__(name, bases, attrs)
        # Skip registration of the base class itself
        if name != "PluginBase" and not attrs.get("__abstract__", False):
            register_plugin(cls)


class PluginBase(metaclass=MetaRegister):
    """Plugin base class.

    Subclasses must implement `process(img, ctx)` and define:
      • name  – english unique id, used in YAML and registry
      • label – chinese display name (optional)
      • params – dict of default parameters (optional)
    """

    # required identifier, override in subclass
    name: str = "base"
    # optional user-friendly label
    label: str = "Base"
    # optional category for UI grouping
    category: str = "图像处理"
    # default params; each instance will get its own copy
    params: Dict[str, Any] = {}

    def __init__(self, **params):
        # merge user params over defaults
        default = self.__class__.params.copy()

        # 定义通用的上下文参数和UI参数，这些不应该传递给插件
        context_params = {"pose", "homography", "roi_dict", "width_px", "height_px"}
        ui_params = {"preview_img", "on_change", "master"}
        internal_params = {"_use_global_template", "_debug", "_temp"}
        excluded_params = context_params | ui_params | internal_params

        # 根据插件类别进行参数过滤
        if self.category == "几何测量":
            # 几何工具插件允许接收pose和homography参数用于坐标变换
            filtered_params = {k: v for k, v in params.items()
                             if k in default or k in {"pose", "homography"}}
        elif self.category == "标定工具":
            # 标定工具插件可能需要特殊参数
            filtered_params = {k: v for k, v in params.items()
                             if k in default or k not in excluded_params}
        elif self.category == "IO":
            # IO插件需要接收所有配置参数（如串口参数等）
            filtered_params = {k: v for k, v in params.items()
                             if k in default or k not in excluded_params}
        else:
            # 图像处理插件只接收预定义的参数，但要排除不相关的参数
            filtered_params = {k: v for k, v in params.items()
                             if k in default}

        default.update(filtered_params)
        self.params = default
        self.setup(self.params)

    # ------------------------------------------------------------------
    def setup(self, params: Dict[str, Any]):
        """Called during init when params are set/updated. Override if needed."""

    # ------------------------------------------------------------------
    def process(self, img, ctx):  # noqa: D401
        """Do work and return (img, ctx). Must be overridden."""
        raise NotImplementedError

    # ------------------------------------------------------------------
    # helper: get registry
    @staticmethod
    def get_registry():
        return PLUGIN_REGISTRY


def get_plugin_registry() -> Dict[str, Type[PluginBase]]:
    return PLUGIN_REGISTRY
