# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).parent.absolute()

# 数据文件收集
datas = [
    # 配置文件
    (str(project_root / 'config'), 'config'),
    # 相机SDK
    (str(project_root / 'MvImport'), 'MvImport'),
    # CommTools目录
    (str(project_root / 'CommTools'), 'CommTools'),
    # core目录
    (str(project_root / 'core'), 'core'),
    # path_manager.py
    (str(project_root / 'path_manager.py'), '.'),
]

# 二进制文件收集（海康相机DLL）
binaries = []
dll_patterns = [
    'MvCameraControl.dll',
    'MVGigEVisionSDK.dll', 
    'MvUsb3vTL.dll',
    'FormatConversion.dll',
    'MediaProcess.dll',
    'MvRender.dll',
    '*.dll'  # 收集所有DLL文件
]

# 从项目根目录收集DLL文件
for pattern in dll_patterns:
    dll_files = list(project_root.glob(pattern))
    for dll_file in dll_files:
        if dll_file.is_file():
            binaries.append((str(dll_file), '.'))

# 隐式导入
hiddenimports = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'numpy',
    'yaml',
    'logging',
    'threading',
    'ctypes',
    'pathlib',
    'core.camera_manager',
    'core.camera_hikvision',
    'core.camera_base',
    'core.camera_factory',
    'core.camera_worker',
    'core.camera_params',
]

a = Analysis(
    ['camera_settings_standalone.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',  # 排除可能导致Qt冲突的库
        'torch',
        'torchvision',
        'cv2',
        'scipy',
        'sklearn',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='CameraSettings',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'assets' / 'sy_vision_app_icon.ico') if (project_root / 'assets' / 'sy_vision_app_icon.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='CameraSettings',
)
