# 继电器延时触发功能使用指南

## 📋 功能概述

继电器延时触发功能是为了解决传送带分拣系统中的位置偏移问题而设计的。当检测到NG产品时，可以延时一段时间后再触发分拣动作，确保产品到达正确位置时执行分拣。

## 🎯 应用场景

### **传送带分拣系统**
```
检测点 ←--距离D--→ 分拣气缸
   ↓                    ↓
[产品] ----速度V---→ [分拣位置]
```

**问题**：检测到NG时，产品还没到达分拣位置
**解决**：延时触发 = 距离D ÷ 速度V

### **典型应用**
- 自动化生产线质量分拣
- 传送带产品剔除
- 流水线不良品分离
- 包装线缺陷产品移除

## ⚙️ 配置参数

### **基本参数**
| 参数 | 说明 | 默认值 | 范围 |
|------|------|--------|------|
| `enable_delay` | 启用延时触发 | `false` | true/false |
| `trigger_delay` | 触发延时时间 | `0` | 0-10000ms |
| `pulse_duration` | 脉冲持续时间 | `100` | 10-5000ms |

### **工作模式对比**

| 模式 | enable_delay | 触发时机 | 适用场景 |
|------|-------------|----------|----------|
| **立即触发** | `false` | 检测到条件立即触发 | 固定位置控制 |
| **延时触发** | `true` | 延时后触发 | 移动产品分拣 |

## 🔧 配置方法

### **1. UI界面配置**
1. 打开继电器配置界面
2. 选择"条件触发模式"
3. 勾选"启用延时触发"
4. 设置"触发延时(ms)"
5. 设置"脉冲持续时间(ms)"

### **2. 配置文件示例**
```yaml
# 传送带分拣配置示例
- io_relay:
    port: "COM3"
    baud: 9600
    relay_index: 1
    action: "CONDITIONAL"
    trigger_on_ng: true
    trigger_on_ok: false
    pulse_duration: 200      # 气缸动作时间
    enable_delay: true       # 启用延时触发
    trigger_delay: 1500      # 延时1.5秒
```

## 📐 延时时间计算

### **基本公式**
```
延时时间 = 检测点到分拣点距离 ÷ 传送带速度 - 气缸响应时间
```

### **计算示例**

#### **示例1：标准传送带**
- 传送带速度：100mm/s
- 检测点到气缸距离：300mm
- 气缸响应时间：100ms

```
理论延时 = 300mm ÷ 100mm/s = 3000ms
实际延时 = 3000ms - 100ms = 2900ms
```

#### **示例2：高速传送带**
- 传送带速度：500mm/s
- 检测点到气缸距离：200mm
- 气缸响应时间：50ms

```
理论延时 = 200mm ÷ 500mm/s = 400ms
实际延时 = 400ms - 50ms = 350ms
```

#### **示例3：慢速精密分拣**
- 传送带速度：50mm/s
- 检测点到气缸距离：150mm
- 气缸响应时间：80ms

```
理论延时 = 150mm ÷ 50mm/s = 3000ms
实际延时 = 3000ms - 80ms = 2920ms
```

## 🔄 工作时序

### **立即触发模式**
```
检测NG → 立即开启继电器 → 保持100ms → 关闭继电器
时间:    0ms              0ms        100ms
```

### **延时触发模式**
```
检测NG → 延时等待 → 开启继电器 → 保持100ms → 关闭继电器
时间:    0ms      1500ms     1500ms      1600ms
```

## 🛠️ 调试与优化

### **1. 初始设置**
1. 测量实际距离和传送带速度
2. 按公式计算理论延时时间
3. 设置初始延时值

### **2. 现场调试**
1. 使用标记产品进行测试
2. 观察分拣位置是否准确
3. 根据偏移情况调整延时时间

### **3. 微调原则**
- **分拣过早**：增加延时时间（+50ms步进）
- **分拣过晚**：减少延时时间（-50ms步进）
- **位置准确**：记录最佳延时值

### **4. 验证方法**
```python
# 连续测试多个产品，统计分拣准确率
测试产品数量: 100个
分拣成功: 98个
准确率: 98%
```

## 📊 性能特点

### **优势**
- ✅ **不阻塞主流程**：延时在后台线程执行
- ✅ **精确控制**：毫秒级延时精度
- ✅ **灵活配置**：支持0-10秒延时范围
- ✅ **向下兼容**：保持原有立即触发功能

### **注意事项**
- ⚠️ **传送带速度稳定性**：速度波动会影响分拣精度
- ⚠️ **产品间距**：确保延时时间内不会误分拣其他产品
- ⚠️ **系统响应时间**：考虑检测、处理、传输的总延时

## 🔧 故障排除

### **问题1：分拣位置不准确**
**可能原因**：
- 延时时间计算错误
- 传送带速度不稳定
- 距离测量不准确

**解决方案**：
1. 重新测量距离和速度
2. 现场微调延时时间
3. 检查传送带稳定性

### **问题2：延时触发不工作**
**可能原因**：
- `enable_delay` 未启用
- 串口连接问题
- 继电器硬件故障

**解决方案**：
1. 检查配置参数
2. 测试串口通信
3. 验证继电器硬件

### **问题3：分拣效率低**
**可能原因**：
- 延时时间过长
- 脉冲持续时间过长
- 气缸响应慢

**解决方案**：
1. 优化延时时间
2. 减少脉冲持续时间
3. 升级气缸设备

## 📈 应用案例

### **案例1：电子元件分拣线**
```yaml
# 高精度电子元件分拣
trigger_delay: 800    # 传送带速度快，延时短
pulse_duration: 50    # 精密分拣，动作快
```

### **案例2：食品包装线**
```yaml
# 食品包装缺陷剔除
trigger_delay: 2000   # 传送带速度慢，延时长
pulse_duration: 300   # 包装较重，动作时间长
```

### **案例3：汽车零件检测**
```yaml
# 汽车零件质量分拣
trigger_delay: 1200   # 中等速度传送带
pulse_duration: 200   # 标准气缸动作时间
```

## 🎯 最佳实践

1. **精确测量**：使用激光测距仪测量距离
2. **速度校准**：定期校准传送带速度
3. **分段调试**：先调试单个产品，再测试连续生产
4. **记录参数**：保存最佳配置参数备份
5. **定期维护**：检查机械磨损对时序的影响

---

*文档版本：v1.0*  
*最后更新：2025-07-05*  
*功能状态：✅ 已实现*
