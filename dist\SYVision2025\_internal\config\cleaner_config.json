{"enabled": true, "cleanup_rules": {"captured_images": {"enabled": true, "keep_days": 7, "max_files": 1000, "max_size_mb": 500, "path": "captured_images", "description": "清理捕获的图像文件，保留最近7天"}, "logs": {"enabled": true, "keep_days": 30, "max_files": 50, "max_file_size_mb": 10, "path": "logs", "description": "清理日志文件，保留最近30天"}, "test_outputs": {"enabled": true, "keep_days": 3, "max_files": 100, "path": "test_outputs", "description": "清理测试输出文件，保留最近3天"}, "cache": {"enabled": true, "keep_hours": 24, "paths": ["__pycache__", "*.pyc", "*.pyo"], "description": "清理Python缓存文件"}, "temp_configs": {"enabled": true, "keep_days": 1, "path": "recipe_editor/tmp", "description": "清理临时配置文件，保留最近1天"}, "old_recipes": {"enabled": false, "keep_days": 90, "path": "recipes", "exclude_patterns": ["*.yaml", "*.json"], "description": "清理过期的配方文件（默认禁用）"}}, "schedule": {"startup_cleanup": true, "daily_cleanup": "02:00", "shutdown_cleanup": true, "auto_cleanup_on_low_space": true, "low_space_threshold_gb": 1.0, "check_interval_seconds": 30}, "notifications": {"show_cleanup_results": true, "log_cleanup_actions": true, "notify_on_low_space": true, "notify_on_large_cleanup": true, "large_cleanup_threshold_mb": 100}, "safety": {"confirm_large_deletions": true, "backup_before_cleanup": false, "dry_run_mode": false, "max_files_per_cleanup": 10000}, "performance": {"cleanup_batch_size": 100, "cleanup_delay_ms": 10, "background_cleanup": true}}