"""Geometry Tools package

提供一些手动作图 / 示教性质的几何工具，与自动测量插件互补。
每个工具模块应暴露 `launch(img: np.ndarray|None)` 函数，弹出独立 Tk 窗口进行交互，返回结果字典。

包含的工具：
- angle_tool: 角度测量工具 (支持三点测量、拟合直线、ROI拟合)
- arc_tool: 圆弧测量工具
- caliper_tool: 卡尺测量工具
- line_tool: 直线工具
- circle_tool: 圆形工具
- distance_tool: 距离测量工具
- position_correction_plugin: 位置修正工具 (透视、旋转、平移、缩放修正)
"""

__all__ = ['line_tool', 'circle_tool', 'distance_tool', 'angle_tool', 'caliper_tool', 'arc_tool', 'position_correction_plugin']

# 插件版本信息
VERSION = '1.0.0'

# 工具启动函数映射
TOOL_LAUNCHERS = {
    'angle_tool': 'plugins.ui.geometry_tools.angle_tool_ui:launch',
    'arc_tool': 'plugins.ui.geometry_tools.arc_tool_ui:launch',
    'caliper_tool': 'plugins.ui.geometry_tools.caliper_tool_ui:launch',
}

def launch_tool(tool_name: str, img=None):
    """启动指定的几何工具

    Args:
        tool_name: 工具名称
        img: 输入图像 (可选)

    Returns:
        工具的测量结果字典
    """
    if tool_name not in TOOL_LAUNCHERS:
        raise ValueError(f"未知的工具: {tool_name}")

    launcher_path = TOOL_LAUNCHERS[tool_name]
    module_path, func_name = launcher_path.split(':')

    try:
        import importlib
        module = importlib.import_module(module_path)
        launcher = getattr(module, func_name)
        return launcher(img)
    except Exception as e:
        raise RuntimeError(f"启动工具 {tool_name} 失败: {str(e)}")
