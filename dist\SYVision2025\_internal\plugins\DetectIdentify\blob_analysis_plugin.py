"""Blob Analysis 插件包装

将 BlobAnalyzer 暴露为流水线插件，可交互设置参数与模板。
结果写入 ctx[step_name]。
"""
from __future__ import annotations
from typing import Dict, Any
import importlib

import cv2
from pathlib import Path

from plugins.plugin_base import PluginBase
from plugins.DetectIdentify.blob_analysis import load_analyzer


class BlobAnalysisPlugin(PluginBase):
    name = "blob_analysis"
    label = "斑点分析"
    category = "检测识别"

    params: Dict[str, Any] = {
        "config_file": "configs/blob/default.yml",
    }

    param_labels = {
        "config_file": "配置文件",
    }

    # ---------------- lifecycle ----------------
    def setup(self, params: Dict[str, Any]):
        self.analyzer = load_analyzer()
        # 如果有配置文件，UI 打开时再加载；这里不做磁盘 IO

    def process(self, img, ctx):
        # 若想离线运行，可直接调用 analyzer.analyze
        if img is None:
            return img, ctx
        stats = self.analyzer.analyze(img, use_roi=False)
        ctx[self.name] = stats
        return img, ctx

    # ---------------- UI ----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        """打开专属 BlobAnalysisFrame UI。关闭时同步配置文件路径。"""
        import tkinter as tk
        from tkinter import messagebox
        try:
            FrameCls = importlib.import_module('plugins.ui.detect_identify.blob_analysis_ui').BlobAnalysisFrame
        except Exception as e:
            messagebox.showerror('错误', f'加载 UI 失败: {e}')
            return

        win = tk.Toplevel(master)
        win.title('斑点分析')
        frame = FrameCls(win)
        frame.pack(fill=tk.BOTH, expand=True)
        # 若有预览图像则加载
        if preview_img is not None:
            frame.img_src = preview_img.copy()
            try:
                frame._show_image(frame.canvas_src, preview_img)
            except Exception:
                pass
        cfg_file = params.get('config_file', '')
        from pathlib import Path
        if cfg_file and Path(cfg_file).is_file():
            frame._load_cfg()

        def _on_close():
            # BlobAnalysisFrame 保存时会写 yml，再将路径同步
            on_change({'config_file': cfg_file or ''})
            win.destroy()
        win.protocol('WM_DELETE_WINDOW', _on_close)
        win.grab_set()
        win.transient(master)


# register
_ = BlobAnalysisPlugin
