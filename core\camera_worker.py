import threading
import time

class CameraWorker(threading.Thread):
    """
    每路相机独立采集线程，负责循环采集并推送数据到下游。
    """
    def __init__(self, camera, push_callback=None, interval=0.05):
        super().__init__()
        self.camera = camera
        self.push_callback = push_callback  # 图像推送到下游（如工位队列）的回调
        self.interval = interval  # 采集间隔
        self.running = False

    def run(self):
        # 确保相机已打开
        if hasattr(self.camera, 'is_open'):
            if not self.camera.is_open():
                try:
                    self.camera.open()
                except Exception as e:
                    print(f"[CameraWorker] 打开相机失败: {e}")
        elif hasattr(self.camera, 'open'):
            try:
                self.camera.open()
            except Exception as e:
                print(f"[CameraWorker] 打开相机失败: {e}")

        self.running = True
        while self.running:
            try:
                frame = self.camera.grab()
                if self.push_callback:
                    self.push_callback(self.camera.camera_id, frame)
            except Exception as e:
                print(f"[CameraWorker] 采集异常: {e}，尝试重连...")
                self.camera.reconnect()
            time.sleep(self.interval)

    def stop(self):
        self.running = False
        self.camera.close()
