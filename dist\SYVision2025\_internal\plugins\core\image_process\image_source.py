"""plugins.core.image_process.image_source
统一图像源接口：支持海康工业相机、文件夹循环、本地单张。
"""
from __future__ import annotations
import cv2, sys
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Union

# ---- 海康相机封装 ----
try:
    from core.camera_hikvision import CameraHikvision
except ImportError:  # 直接运行此文件
    _root = Path(__file__).resolve().parents[3]
    if str(_root) not in sys.path:
        sys.path.insert(0, str(_root))
    from core.camera_hikvision import CameraHikvision

__all__ = ['ImageSource', 'CameraSource', 'FolderSource', 'FileSource', 'create_source']

# 导入路径管理模块
try:
    from path_manager import get_capture_path
    CAP_DIR = Path(get_capture_path())
except ImportError:
    # 如果路径管理模块不可用，使用默认路径
    CAP_DIR = Path(__file__).resolve().parents[3] / 'captures'
    CAP_DIR.mkdir(parents=True, exist_ok=True)


def _timestamp_name(suffix: str = '.jpg') -> str:
    return datetime.now().strftime('%Y%m%d_%H%M%S_%f') + suffix


class ImageSource:
    """图像源基类."""

    def open(self) -> bool:  # noqa: D401
        """打开资源."""
        raise NotImplementedError

    def close(self):
        """关闭资源."""
        raise NotImplementedError

    def next_frame(self) -> Optional[np.ndarray]:
        """获取下一帧图像，返回 None 表示无新帧."""
        raise NotImplementedError

    def snapshot(self) -> Optional[str]:
        """保存当前帧并返回路径；不支持则返回 None."""
        return None


# ---------------- Camera -----------------
class CameraSource(ImageSource):
    """海康工业相机实时采集."""

    def __init__(self, cam_index: int = 0, save_dir: Path | None = None):
        self._cam: Optional[CameraHikvision] = None
        self._last: Optional[np.ndarray] = None
        self.cam_index = cam_index
        self.save_dir = Path(save_dir) if save_dir else CAP_DIR
        self.last_error: str | None = None
        self.save_dir.mkdir(parents=True, exist_ok=True)

    def open(self) -> bool:
        try:
            self._cam = CameraHikvision(f'cam{self.cam_index}', {})
            if not self._cam.open(self.cam_index):
                return False
            # 启动连续采集，方便 UI 实时显示
            self._cam.start_grab(continuous=True)
            return True
        except Exception as e:
            self.last_error = str(e)
            print('打开相机失败:', e)
            self._cam = None
            return False

    def close(self):
        if self._cam:
            try:
                if getattr(self._cam, 'grabbing', False):
                    self._cam.stop_grab()
            except Exception:
                pass
            self._cam.close()
            self._cam = None

    def next_frame(self):
        if not self._cam:
            return None
        frame = self._cam.grab()
        if frame is None:
            return None
        import numpy as np
        from PIL import Image
        if isinstance(frame, Image.Image):
            frame = cv2.cvtColor(np.array(frame), cv2.COLOR_RGB2BGR)
        self._last = frame
        return self._last

    def snapshot(self):
        if self._last is None:
            return None
        file = self.save_dir / _timestamp_name()
        cv2.imwrite(str(file), self._last)
        return str(file)


# ---------------- Folder -----------------
class FolderSource(ImageSource):
    """循环播放文件夹中的图片."""

    def __init__(self, folder: Union[str, Path]):
        self.folder = Path(folder)
        exts = {'.jpg', '.png', '.bmp', '.jpeg', '.tif', '.tiff'}
        self.paths: List[Path] = [p for p in sorted(self.folder.glob('*')) if p.suffix.lower() in exts]
        self.idx = -1

    def open(self):
        return bool(self.paths)

    def close(self):
        pass

    def next_frame(self):
        if not self.paths:
            return None
        self.idx = (self.idx + 1) % len(self.paths)
        return cv2.imread(str(self.paths[self.idx]))


# ---------------- Single File ------------
class FileSource(ImageSource):
    """单张图片源."""

    def __init__(self, file_path: Union[str, Path]):
        self.path = Path(file_path)
        self._sent = False

    def open(self):
        return self.path.exists()

    def close(self):
        pass

    def next_frame(self):
        if self._sent:
            return None
        self._sent = True
        return cv2.imread(str(self.path))


# -------- factory ---------

def create_source(mode: str, path: Optional[str] = None) -> ImageSource:
    """创建图像源.

    mode: 'camera' | 'folder' | 'file'."""
    mode = mode.lower()
    if mode == 'camera':
        idx = int(path) if path and path.isdigit() else 0
        return CameraSource(idx)
    if mode == 'folder' and path:
        return FolderSource(path)
    if mode == 'file' and path:
        return FileSource(path)
    raise ValueError('Unsupported mode or missing path')
