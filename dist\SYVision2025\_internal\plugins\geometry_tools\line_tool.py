"""Line Tool - 手动点击多点拟合直线
简化自旧版 GeoLine（PyQt），改为纯 Tkinter，方便集成。
使用：
    from plugins.geometry_tools.line_tool import launch
    result = launch(img)
result  -> dict { 'angle_deg': float, 'slope': float, 'intercept': float }
"""
import tkinter as tk
from tkinter import filedialog, messagebox
from math import atan2, degrees
import cv2, numpy as np


class LineTool(tk.Toplevel):
    def __init__(self, master=None, img=None):
        super().__init__(master)
        self.title('手动直线拟合')
        self.geometry('800x600')
        self.points: list[tuple[int,int]] = []
        self.img = img.copy() if img is not None else None
        self.canvas = tk.Canvas(self, bg='black')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        self.canvas.bind('<Button-1>', self._on_click)

        btn_frame = tk.Frame(self)
        btn_frame.pack(fill=tk.X)
        tk.Button(btn_frame, text='载入图片', command=self._load_img).pack(side=tk.LEFT)
        tk.Button(btn_frame, text='拟合直线', command=self._fit).pack(side=tk.LEFT)
        tk.Button(btn_frame, text='清除', command=self._clear).pack(side=tk.LEFT)
        self.lbl_info = tk.Label(btn_frame, text='点击若干点后按“拟合直线”')
        self.lbl_info.pack(side=tk.LEFT, padx=10)

        if self.img is not None:
            self._show_img()
        self.result = None
        self.wait_window(self)  # modal

    # ------------- events -------------
    def _on_click(self, evt):
        if self.img is None:
            return
        x,y=evt.x, evt.y
        self.points.append((x,y))
        r=3
        self.canvas.create_oval(x-r,y-r,x+r,y+r,outline='red',fill='red')

    def _load_img(self):
        fname = filedialog.askopenfilename(filetypes=[('Image','*.png;*.jpg;*.bmp')])
        if not fname:
            return
        img=cv2.imread(fname)
        if img is None:
            messagebox.showerror('错误','无法读取图片')
            return
        self.img = img
        self._show_img()

    def _fit(self):
        if len(self.points) < 2:
            messagebox.showwarning('提示','请至少点击两点')
            return
        pts = np.array(self.points, dtype=np.float32)
        vx,vy,x0,y0 = cv2.fitLine(pts, cv2.DIST_L2,0,0.01,0.01)
        k = vy/vx if vx!=0 else 1e6
        b = y0 - k*x0
        ang_deg = degrees(atan2(vy, vx))
        # draw line
        h = self.canvas.winfo_height(); w=self.canvas.winfo_width()
        x1,y1 = 0, int(b)
        x2,y2 = w, int(k*w + b)
        self.canvas.create_line(x1,y1,x2,y2, fill='lime', width=2)
        self.lbl_info.config(text=f'角度:{ang_deg:.2f}°, k={k:.3f}, b={b:.1f}')
        self.result={'angle_deg': float(ang_deg), 'slope': float(k), 'intercept': float(b)}

    def _clear(self):
        self.points.clear()
        self.canvas.delete('all')
        if self.img is not None:
            self._show_img()
        self.lbl_info.config(text='点击若干点后按“拟合直线”')

    # ------------- helper -------------
    def _show_img(self):
        rgb=cv2.cvtColor(self.img, cv2.COLOR_BGR2RGB)
        h,w = rgb.shape[:2]
        scale = min(self.canvas.winfo_width()/w if self.canvas.winfo_width()>0 else 1,
                     self.canvas.winfo_height()/h if self.canvas.winfo_height()>0 else 1,1)
        disp=cv2.resize(rgb,(int(w*scale), int(h*scale)))
        self.tk_img = tk.PhotoImage(width=disp.shape[1], height=disp.shape[0], data=cv2.imencode('.png',cv2.cvtColor(disp,cv2.COLOR_RGB2BGR))[1].tobytes(),format='png')
        self.canvas.create_image(0,0, anchor='nw', image=self.tk_img)


def launch(img=None):
    tool= LineTool(master=tk._default_root or tk.Tk(), img=img)
    return tool.result
