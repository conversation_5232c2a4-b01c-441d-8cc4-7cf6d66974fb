"""Helper for writing mm_per_px back to recipe pipeline when geometry tool UIs save.

Usage:
    from ._recipe_sync import update_mm_in_recipe
    update_mm_in_recipe(self._recipe_path, 'rect_tool', self.mm_var.get())
"""
from __future__ import annotations

from pathlib import Path
import yaml
from typing import Union

# ----------------------------------------------------------------------
# Internal generic updater
# ----------------------------------------------------------------------

def _update_fields_in_recipe(recipe_path: Union[str, Path, None], step_key: str, updates: dict) -> None:
    """Update arbitrary fields under given step in recipe YAML.

    Only writes the YAML file when *any* value actually changed to avoid
    unnecessary file churn.
    """
    if not recipe_path:
        return
    p = Path(recipe_path)
    if not p.exists():
        return
    try:
        doc = yaml.safe_load(p.read_text(encoding="utf-8")) or {}
    except Exception:
        return
    # 既兼容根节点列表 (workstation pipeline.yaml)，也兼容 {pipeline: [...]} 结构
    steps: list = []
    if isinstance(doc, list):
        steps = doc
    elif isinstance(doc, dict):
        steps = doc.get("pipeline", [])  # type: ignore
    changed = False
    for step in steps:
        if isinstance(step, dict) and step_key in step:
            for k, v in updates.items():
                if step[step_key].get(k) != v:
                    step[step_key][k] = v
                    changed = True
    if changed:
        p.write_text(
            yaml.safe_dump(doc, allow_unicode=True, sort_keys=False),
            encoding="utf-8",
        )

# ----------------------------------------------------------------------
# Public helpers
# ----------------------------------------------------------------------

def update_rois_in_recipe(recipe_path: Union[str, Path, None], step_key: str, rois: list[dict]):
    """Write rois list back to recipe yaml under given step."""
    _update_fields_in_recipe(recipe_path, step_key, {"rois": rois})

def update_mm_in_recipe(recipe_path: Union[str, Path, None], step_key: str, mm_val: float) -> None:
    """Update mm_per_px in recipe (supports list- or dict-root)."""
    _update_fields_in_recipe(recipe_path, step_key, {"mm_per_px": float(mm_val)})

def update_arc_params(recipe_path: Union[str, Path, None], *, r_est: float, smooth_alpha: float, score_th: float):
    """Update arc ROI stabilization related params in recipe."""
    _update_fields_in_recipe(
        recipe_path,
        'arc_tool',
        {
            "r_est": float(r_est),
            "smooth_alpha": float(smooth_alpha),
            "score_th": float(score_th),
        },
    )
