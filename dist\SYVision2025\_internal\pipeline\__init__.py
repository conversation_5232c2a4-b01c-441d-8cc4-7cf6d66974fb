"""Pipeline framework package.

This package centralizes pipeline-related components and exposes a global
registry of plugins.  On import it will automatically load all Python
modules under the legacy ``plugins`` package so that existing plugins are
registered without code modification.
"""
from importlib import import_module
from pathlib import Path
from types import ModuleType
from typing import Dict, Type

from plugins.plugin_base import PluginBase, get_plugin_registry  # noqa: F401

PLUGIN_REGISTRY = get_plugin_registry()

# ---------------------------------------------------------------------------
# Legacy compatibility – auto import everything under top-level ``plugins``
# directory so that their subclasses register themselves.
# ---------------------------------------------------------------------------
_root = Path(__file__).resolve().parents[1]
_plugins_dir = _root / "plugins"

if _plugins_dir.exists():
    for py_path in _plugins_dir.rglob("*.py"):
        if py_path.name.startswith("__"):
            continue
        rel = py_path.relative_to(_plugins_dir)
        module_name = "plugins." + ".".join(rel.with_suffix("").parts)
        try:
            import_module(module_name)
        except Exception:
            # Keep failures silent for now; they will surface when plugin
            # actually used. Optionally log to console.
            print(f"[pipeline] failed to import plugin module {module_name}")

__all__ = [
    "PluginBase",
    "PLUGIN_REGISTRY",
    "get_plugin_registry",
]
