from .camera_hikvision import CameraHikvision
from .camera_base import CameraBase

class CameraFactory:
    """
    根据配置创建相机实例（当前仅支持海康 GIGE/USB）。
    """
    @staticmethod
    def create(config):
        brand = config.get('brand', '').lower()
        if brand == 'hikvision':
            return CameraHikvision(config['id'], config)
        else:
            raise ValueError(f"不支持的相机品牌: {brand}，目前仅支持海康 GIGE/USB 工业相机。")
