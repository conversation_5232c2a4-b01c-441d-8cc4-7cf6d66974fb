"""Undistort Plugin

Applies cv2.undistort based on camera intrinsic parameters written to
ctx["camera_calib"] by the upstream `camera_calibrate` plugin.

Insert this plugin **after** `camera_calibrate` in the pipeline to obtain
real-time distortion-corrected images for downstream detection /
measurement steps.
"""
from __future__ import annotations

from typing import Dict, Any
import numpy as np
import cv2

from plugins.plugin_base import PluginBase

__all__ = ["UndistortPlugin"]


class UndistortPlugin(PluginBase):
    name = "undistort"
    label = "去畸变"
    category = "几何校正"

    # 当前无需额外参数，预留字段便于后续扩展（如 ROI 裁剪、插值方法）
    params: Dict[str, Any] = {}

    # --------------------------------------------------
    def setup(self, params: Dict[str, Any]):  # noqa: D401
        # 无需初始化；若后续支持 ROI 等参数可在此解析
        pass

    # --------------------------------------------------
    def process(self, img, ctx):  # type: ignore[override]
        """Undistort image if calibration data present.

        Expected calibration format (from camera_calibrate plugin):
            ctx["camera_calib"] = {
                "K": np.ndarray (3×3),
                "dist": np.ndarray (N,),
                "img_size": (w, h),
            }
        """
        if img is None:
            return img, ctx

        calib = ctx.get("camera_calib")
        if not calib:
            return img, ctx

        try:
            K = np.asarray(calib["K"], dtype=np.float64)
            dist = np.asarray(calib["dist"], dtype=np.float64)
            undistorted = cv2.undistort(img, K, dist)
            return undistorted, ctx
        except Exception as exc:
            print(f"[Undistort] cv2.undistort failed: {exc}")
            return img, ctx


# ---------------------------------------------------------------------------
# 注册实例，确保被 MetaRegister 捕获后在 legacy 系统中可用
_ = UndistortPlugin()
