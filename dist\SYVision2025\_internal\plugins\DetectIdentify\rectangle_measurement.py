"""
矩形测量工具
实现十字卡尺矩形测量，包括拟合线显示和边缘检测
"""

import cv2
import numpy as np
import math
from typing import Dict, List, Tuple, Optional


class RectangleMeasurer:
    """矩形测量器 - 十字卡尺方法"""
    
    def __init__(self):
        """初始化测量器"""
        self.scan_lines = 8           # 扫描线数量
        self.edge_threshold = 30      # 边缘阈值
        self.line_width = 3          # 扫描线宽度
        self.polarity = "both"       # 边缘极性: "positive", "negative", "both"
        self.show_scan_lines = True  # 显示扫描线
        self.show_fit_lines = True   # 显示拟合线
        
    def measure(self, img: np.ndarray, roi: Dict, params: Dict = None) -> Dict:
        """
        执行矩形测量
        
        Args:
            img: 输入图像
            roi: ROI信息
            params: 测量参数
            
        Returns:
            测量结果
        """
        try:
            # 更新参数
            if params:
                self._update_params(params)
            
            # 转换为灰度图
            if len(img.shape) == 3:
                gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray_img = img.copy()
            
            # 获取ROI参数
            center = roi['center']
            len_x = roi['len_x']
            len_y = roi['len_y']
            angle = roi.get('angle', 0)
            
            # 生成扫描线
            scan_lines = self._generate_scan_lines(center, len_x, len_y, angle)
            
            # 检测边缘点
            edge_results = self._detect_edges(gray_img, scan_lines)
            
            # 拟合矩形边界
            fit_results = self._fit_rectangle_edges(edge_results)
            
            if not fit_results['success']:
                return {
                    "ok": False,
                    "error": "矩形边界拟合失败",
                    "scan_lines": scan_lines,
                    "edge_points": edge_results['all_edges']
                }
            
            # 计算尺寸
            dimensions = self._calculate_dimensions(fit_results)
            
            return {
                "ok": True,
                "width": dimensions['width'],
                "height": dimensions['height'],
                "center": dimensions['center'],
                "angle": dimensions['angle'],
                "roi_info": roi,
                "scan_lines": scan_lines,
                "edge_points": edge_results['all_edges'],
                "fit_lines": fit_results['lines'],
                "corner_points": fit_results['corners'],
                "edge_counts": edge_results['counts']
            }
            
        except Exception as e:
            return {"ok": False, "error": f"矩形测量失败: {str(e)}"}
    
    def _update_params(self, params: Dict):
        """更新参数"""
        self.scan_lines = params.get('scan_lines', self.scan_lines)
        self.edge_threshold = params.get('edge_threshold', self.edge_threshold)
        self.line_width = params.get('line_width', self.line_width)
        self.polarity = params.get('polarity', self.polarity)
        self.show_scan_lines = params.get('show_scan_lines', self.show_scan_lines)
        self.show_fit_lines = params.get('show_fit_lines', self.show_fit_lines)
    
    def _generate_scan_lines(self, center: Tuple[float, float], 
                           len_x: float, len_y: float, angle: float) -> Dict:
        """生成扫描线"""
        cx, cy = center
        angle_rad = math.radians(angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        
        scan_lines = {
            'horizontal': [],  # 水平扫描线（测量高度）
            'vertical': []     # 垂直扫描线（测量宽度）
        }
        
        # 生成水平扫描线
        for i in range(self.scan_lines):
            # 在局部坐标系中的Y偏移
            local_y = (i - (self.scan_lines - 1) / 2) * (len_y * 0.8 / self.scan_lines)
            
            # 线的起点和终点（局部坐标）
            local_start = (-len_x * 0.6, local_y)
            local_end = (len_x * 0.6, local_y)
            
            # 转换到全局坐标
            start_x = cx + local_start[0] * cos_a - local_start[1] * sin_a
            start_y = cy + local_start[0] * sin_a + local_start[1] * cos_a
            end_x = cx + local_end[0] * cos_a - local_end[1] * sin_a
            end_y = cy + local_end[0] * sin_a + local_end[1] * cos_a
            
            scan_lines['horizontal'].append({
                'start': (start_x, start_y),
                'end': (end_x, end_y),
                'direction': 'horizontal'
            })
        
        # 生成垂直扫描线
        for i in range(self.scan_lines):
            # 在局部坐标系中的X偏移
            local_x = (i - (self.scan_lines - 1) / 2) * (len_x * 0.8 / self.scan_lines)
            
            # 线的起点和终点（局部坐标）
            local_start = (local_x, -len_y * 0.6)
            local_end = (local_x, len_y * 0.6)
            
            # 转换到全局坐标
            start_x = cx + local_start[0] * cos_a - local_start[1] * sin_a
            start_y = cy + local_start[0] * sin_a + local_start[1] * cos_a
            end_x = cx + local_end[0] * cos_a - local_end[1] * sin_a
            end_y = cy + local_end[0] * sin_a + local_end[1] * cos_a
            
            scan_lines['vertical'].append({
                'start': (start_x, start_y),
                'end': (end_x, end_y),
                'direction': 'vertical'
            })
        
        return scan_lines
    
    def _detect_edges(self, img: np.ndarray, scan_lines: Dict) -> Dict:
        """检测边缘点"""
        all_edges = []
        horizontal_edges = []
        vertical_edges = []
        
        # 处理水平扫描线
        for line in scan_lines['horizontal']:
            edges = self._detect_edges_on_line(img, line)
            horizontal_edges.extend(edges)
            all_edges.extend(edges)
        
        # 处理垂直扫描线
        for line in scan_lines['vertical']:
            edges = self._detect_edges_on_line(img, line)
            vertical_edges.extend(edges)
            all_edges.extend(edges)
        
        return {
            'all_edges': all_edges,
            'horizontal_edges': horizontal_edges,
            'vertical_edges': vertical_edges,
            'counts': {
                'total': len(all_edges),
                'horizontal': len(horizontal_edges),
                'vertical': len(vertical_edges)
            }
        }
    
    def _detect_edges_on_line(self, img: np.ndarray, line: Dict) -> List[Tuple[float, float]]:
        """在扫描线上检测边缘点"""
        start = line['start']
        end = line['end']
        
        # 生成扫描线上的像素点
        num_points = int(max(abs(end[0] - start[0]), abs(end[1] - start[1])))
        if num_points < 2:
            return []
        
        x_coords = np.linspace(start[0], end[0], num_points)
        y_coords = np.linspace(start[1], end[1], num_points)
        
        # 采样像素值
        pixel_values = []
        valid_points = []
        
        for i, (x, y) in enumerate(zip(x_coords, y_coords)):
            xi, yi = int(round(x)), int(round(y))
            if 0 <= xi < img.shape[1] and 0 <= yi < img.shape[0]:
                pixel_values.append(img[yi, xi])
                valid_points.append((x, y))
        
        if len(pixel_values) < 3:
            return []
        
        # 计算梯度
        gradients = np.gradient(pixel_values)
        
        # 检测边缘
        edges = []
        for i in range(1, len(gradients) - 1):
            grad_strength = abs(gradients[i])
            
            if grad_strength > self.edge_threshold:
                # 检查极性
                if self.polarity == "positive" and gradients[i] <= 0:
                    continue
                elif self.polarity == "negative" and gradients[i] >= 0:
                    continue
                
                # 检查是否为局部极值
                if (grad_strength > abs(gradients[i-1]) and 
                    grad_strength > abs(gradients[i+1])):
                    edges.append(valid_points[i])
        
        return edges
    
    def _fit_rectangle_edges(self, edge_results: Dict) -> Dict:
        """拟合矩形边界"""
        try:
            all_edges = edge_results['all_edges']
            
            if len(all_edges) < 8:  # 至少需要8个边缘点
                return {'success': False, 'error': '边缘点不足'}
            
            # 将边缘点转换为numpy数组
            points = np.array(all_edges)
            
            # 使用RANSAC拟合四条直线
            lines = self._fit_four_lines_ransac(points)
            
            if len(lines) < 4:
                return {'success': False, 'error': '无法拟合四条边界线'}
            
            # 计算交点（矩形角点）
            corners = self._calculate_line_intersections(lines)
            
            return {
                'success': True,
                'lines': lines,
                'corners': corners
            }
            
        except Exception as e:
            return {'success': False, 'error': f'拟合失败: {str(e)}'}
    
    def _fit_four_lines_ransac(self, points: np.ndarray) -> List[Dict]:
        """使用RANSAC拟合四条直线"""
        lines = []
        remaining_points = points.copy()
        
        for _ in range(4):  # 最多拟合4条线
            if len(remaining_points) < 3:
                break
            
            best_line = None
            best_inliers = []
            max_inliers = 0
            
            # RANSAC迭代
            for _ in range(100):
                # 随机选择两个点
                if len(remaining_points) < 2:
                    break
                    
                idx = np.random.choice(len(remaining_points), 2, replace=False)
                p1, p2 = remaining_points[idx]
                
                # 拟合直线
                line = self._fit_line_from_points(p1, p2)
                
                # 计算内点
                inliers = []
                for point in remaining_points:
                    dist = self._point_to_line_distance(point, line)
                    if dist < 3.0:  # 距离阈值
                        inliers.append(point)
                
                if len(inliers) > max_inliers:
                    max_inliers = len(inliers)
                    best_inliers = inliers
                    best_line = line
            
            if best_line and len(best_inliers) >= 3:
                # 使用所有内点重新拟合直线
                inlier_points = np.array(best_inliers)
                refined_line = self._fit_line_least_squares(inlier_points)
                lines.append(refined_line)
                
                # 移除已使用的点
                remaining_points = np.array([p for p in remaining_points if not any(np.allclose(p, inlier) for inlier in best_inliers)])
        
        return lines
    
    def _fit_line_from_points(self, p1: np.ndarray, p2: np.ndarray) -> Dict:
        """从两点拟合直线"""
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]
        
        if abs(dx) < 1e-6:  # 垂直线
            return {'type': 'vertical', 'x': p1[0]}
        else:
            slope = dy / dx
            intercept = p1[1] - slope * p1[0]
            return {'type': 'normal', 'slope': slope, 'intercept': intercept}
    
    def _fit_line_least_squares(self, points: np.ndarray) -> Dict:
        """最小二乘法拟合直线"""
        if len(points) < 2:
            return None

        x = points[:, 0]
        y = points[:, 1]

        # 检查是否为垂直线
        if np.std(x) < 1e-6:
            return {'type': 'vertical', 'x': np.mean(x)}

        # 手动实现最小二乘法
        n = len(points)
        sum_x = np.sum(x)
        sum_y = np.sum(y)
        sum_xy = np.sum(x * y)
        sum_x2 = np.sum(x * x)

        # 计算斜率和截距
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n

        return {'type': 'normal', 'slope': slope, 'intercept': intercept}
    
    def _point_to_line_distance(self, point: np.ndarray, line: Dict) -> float:
        """计算点到直线的距离"""
        x, y = point
        
        if line['type'] == 'vertical':
            return abs(x - line['x'])
        else:
            # ax + by + c = 0 形式
            a = line['slope']
            b = -1
            c = line['intercept']
            
            return abs(a * x + b * y + c) / math.sqrt(a * a + b * b)
    
    def _calculate_line_intersections(self, lines: List[Dict]) -> List[Tuple[float, float]]:
        """计算直线交点"""
        corners = []
        
        for i in range(len(lines)):
            for j in range(i + 1, len(lines)):
                intersection = self._line_intersection(lines[i], lines[j])
                if intersection:
                    corners.append(intersection)
        
        return corners
    
    def _line_intersection(self, line1: Dict, line2: Dict) -> Optional[Tuple[float, float]]:
        """计算两直线交点"""
        if line1['type'] == 'vertical' and line2['type'] == 'vertical':
            return None  # 平行线
        
        if line1['type'] == 'vertical':
            x = line1['x']
            y = line2['slope'] * x + line2['intercept']
            return (x, y)
        
        if line2['type'] == 'vertical':
            x = line2['x']
            y = line1['slope'] * x + line1['intercept']
            return (x, y)
        
        # 两条普通直线
        if abs(line1['slope'] - line2['slope']) < 1e-6:
            return None  # 平行线
        
        x = (line2['intercept'] - line1['intercept']) / (line1['slope'] - line2['slope'])
        y = line1['slope'] * x + line1['intercept']
        
        return (x, y)
    
    def _calculate_dimensions(self, fit_results: Dict) -> Dict:
        """计算矩形尺寸"""
        corners = fit_results['corners']
        
        if len(corners) < 4:
            # 如果角点不足，使用直线计算
            lines = fit_results['lines']
            return self._calculate_dimensions_from_lines(lines)
        
        # 使用角点计算
        corners = np.array(corners)
        
        # 计算中心点
        center = np.mean(corners, axis=0)
        
        # 计算边长
        distances = []
        for i in range(len(corners)):
            for j in range(i + 1, len(corners)):
                dist = np.linalg.norm(corners[i] - corners[j])
                distances.append(dist)
        
        distances.sort()
        
        # 假设最短的两个距离是宽度和高度
        width = distances[0] if len(distances) > 0 else 0
        height = distances[1] if len(distances) > 1 else 0
        
        # 计算角度（使用第一条边）
        if len(corners) >= 2:
            dx = corners[1][0] - corners[0][0]
            dy = corners[1][1] - corners[0][1]
            angle = math.degrees(math.atan2(dy, dx))
        else:
            angle = 0
        
        return {
            'width': width,
            'height': height,
            'center': tuple(center),
            'angle': angle
        }
    
    def _calculate_dimensions_from_lines(self, lines: List[Dict]) -> Dict:
        """从直线计算尺寸"""
        # 简化实现：返回默认值
        return {
            'width': 0,
            'height': 0,
            'center': (0, 0),
            'angle': 0
        }
