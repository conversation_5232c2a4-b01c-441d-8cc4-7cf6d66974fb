conditions: '[{"name": "目标数量检查", "source_plugin": "yolo", "source_key": "total_count",
  "operator": "==", "target_value": 1, "weight": 1.0, "enabled": true}, {"name": "合格数量检查",
  "source_plugin": "yolo", "source_key": "ok_count", "operator": ">=", "target_value":
  1, "weight": 0.9, "enabled": true}, {"name": "不合格数量检查", "source_plugin": "yolo",
  "source_key": "ng_count", "operator": "==", "target_value": 0, "weight": 1.0, "enabled":
  true}, {"name": "总面积检查", "source_plugin": "yolo", "source_key": "total_area_mm2",
  "operator": "IN_RANGE", "target_value": [5.0, 50.0], "weight": 0.7, "enabled": true},
  {"name": "ROI匹配检查", "source_plugin": "yolo", "source_key": "roi_matched", "operator":
  ">=", "target_value": 0.95, "weight": 0.8, "enabled": true}]'
default_result: UNKNOWN
enable_judgment: true
enable_logging: true
enable_statistics: true
io_outputs: '[{"name": "合格指示灯", "io_type": "digital", "address": "DO1", "ok_value":
  true, "ng_value": false, "warn_value": null, "enabled": true}, {"name": "不合格指示灯",
  "io_type": "digital", "address": "DO2", "ok_value": false, "ng_value": true, "warn_value":
  null, "enabled": true}, {"name": "分拣气缸", "io_type": "digital", "address": "DO3",
  "ok_value": false, "ng_value": true, "warn_value": null, "enabled": true}, {"name":
  "计数脉冲", "io_type": "digital", "address": "DO4", "ok_value": true, "ng_value": true,
  "warn_value": true, "enabled": true}, {"name": "蜂鸣器报警", "io_type": "digital", "address":
  "DO5", "ok_value": false, "ng_value": true, "warn_value": true, "enabled": true},
  {"name": "质量等级信号", "io_type": "analog", "address": "AO1", "ok_value": 10.0, "ng_value":
  0.0, "warn_value": 5.0, "enabled": true}, {"name": "检测计数信号", "io_type": "analog",
  "address": "AO2", "ok_value": 1.0, "ng_value": 1.0, "warn_value": 1.0, "enabled":
  false}]'
judgment_mode: custom
log_level: INFO
logic_expression: C1 AND C2 AND C3
ng_threshold: 3
statistics_window: 50
warn_threshold: 5
