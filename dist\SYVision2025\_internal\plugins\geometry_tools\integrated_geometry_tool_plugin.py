"""集成几何测量工具插件包装

此模块为 **集成几何测量工具** 在 Pipeline Editor 中的插件壳。
功能：
1. 向插件系统注册自身（`name = "integrated_geometry_tool"`）
2. 暴露持久参数（测量类型、扫描参数等）
3. 在需要时懒加载并打开 UI 交互界面
4. 在流水线运行阶段执行几何测量逻辑
5. 能够继承上级ROI信息（模板匹配、形状匹配等）
"""
from __future__ import annotations

from typing import Dict, Any, Optional
import importlib

# 插件基础类
from plugins.plugin_base import PluginBase


class IntegratedGeometryToolPlugin(PluginBase):
    """集成几何测量工具插件（供 Pipeline Editor 使用）"""

    # 唯一标识（插件注册名）
    name: str = "integrated_geometry_tool"
    # 在 UI 中显示的中文名称
    label: str = "集成几何测量"
    # 分类（用于左侧树形控件分组）
    category: str = "几何测量"

    # ---------------- 持久参数 -----------------
    params: Dict[str, Any] = {
        "measurement_type": "cross_caliper",    # 默认十字卡尺测量
        "scan_lines": 24,                  # 扫描线数量
        "line_width": 10,                   # 扫描线宽度
        "edge_threshold": 5,              # 边缘阈值
        "polarity": "BOTH",                # 边缘极性: BOTH, POSITIVE, NEGATIVE
        "roi_expand_ratio": 1.1,          # ROI扩张比例
        "fillet_roi_radius": 0.0,          # 圆角局部ROI半径
        "debug_draw": True,                # 调试绘制
        "remove_overlay": True,            # 移除上游绘制的绿色ROI线
        "debug_log": False,               # 调试日志打印
        "show_roi_box": True,            # 是否绘制绿色外框
        "config_file": "",                 # 配置文件路径
        # 模板匹配相关参数（与UI界面保持一致）
        "template_file": "",              # 模板文件路径
        "match_method": "template",        # 匹配方式: template, shape, feature
        "match_threshold": 0.8,           # 匹配阈值
        "angle_method": "shape_match",    # 角度测量方法
    }

    # 参数标签（用于UI显示）
    param_labels: Dict[str, str] = {
        "measurement_type": "测量类型",
        "scan_lines": "扫描线数",
        "line_width": "线宽",
        "edge_threshold": "边缘阈值",
        "polarity": "边缘极性",
        "roi_expand_ratio": "ROI扩张比例",
        "fillet_roi_radius": "圆角ROI半径",
        "debug_log": "调试日志",
        "show_roi_box": "绘制ROI框",
        "debug_draw": "调试绘制",
        "config_file": "配置文件",
        "template_file": "模板文件",
        "match_method": "匹配方式",
        "match_threshold": "匹配阈值",
        "angle_method": "角度测量方法",
    }

    def __init__(self, **params):
        super().__init__(**params)
        self._ui_instance = None

    def process(self, img, ctx):
        """
        处理图像，执行几何测量

        Args:
            img: 输入图像
            ctx: 上下文，包含上级ROI信息

        Returns:
            img: 处理后的图像
            ctx: 更新后的上下文
        """
        try:
            import cv2

            # 加载工位配置文件
            self._load_workstation_config()

            # 获取上级ROI信息
            roi_info = self._extract_roi_from_context(ctx)
            if roi_info is None:
                # 如果没有上级ROI，尝试使用内置模板匹配
                print("⚠️ 未找到上级ROI信息，尝试内置模板匹配")
                roi_info = self._execute_builtin_template_matching(img)

                if roi_info is None:
                    # 如果内置模板匹配也失败，使用默认全图ROI
                    print("⚠️ 内置模板匹配失败，使用默认全图ROI")
                    h, w = img.shape[:2]
                    roi_info = {
                        "center": (w // 2, h // 2),
                        "len_x": w * 0.8,  # 使用图像80%的宽度
                        "len_y": h * 0.8,  # 使用图像80%的高度
                        "angle": 0,
                        "source": "default",
                        "corners": [
                            (w * 0.1, h * 0.1),
                            (w * 0.9, h * 0.1),
                            (w * 0.9, h * 0.9),
                            (w * 0.1, h * 0.9)
                        ]
                    }
                    print(f"✅ 使用默认ROI: 中心({roi_info['center'][0]:.0f}, {roi_info['center'][1]:.0f}), 尺寸{roi_info['len_x']:.0f}x{roi_info['len_y']:.0f}")
                else:
                    print(f"✅ 内置模板匹配成功: 中心{roi_info['center']}, 置信度{roi_info.get('score', 'N/A')}")
            else:
                print(f"✅ 继承上级ROI: {roi_info}")

            # 转换为灰度图
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                # 可选：去除上游形状匹配绘制的绿色ROI线，避免干扰圆角拟合
                if self.params.get("remove_overlay", True):
                    # ROI线通常为纯或近似绿色 (BGR≈(0,255,0))，阈值宽松些
                    b,g,r = cv2.split(img)
                    mask = (g > 180) & (b < 100) & (r < 100)
                    gray[mask] = 0
            else:
                gray = img.copy()

            # 导入测量工具
            from plugins.geometry_tools.integrated_geometry_tool import (
                measure_circle, measure_arc, measure_cross_caliper, measure_fillet_radius, measure_angle,
                GeometryMeasureParams
            )

            # 创建测量参数
            measure_params = GeometryMeasureParams(
                scan_lines=self.params.get("scan_lines", 12),
                line_width=self.params.get("line_width", 8),
                edge_threshold=self.params.get("edge_threshold", 15),
                polarity=self.params.get("polarity", "BOTH"),
                roi_expand_ratio=self.params.get("roi_expand_ratio", 1.0),
                fillet_roi_radius=self.params.get("fillet_roi_radius", 0.0),
                debug_log=self.params.get("debug_log", False)
            )

            # 执行测量
            measurement_type = self.params.get("measurement_type", "circle")

            if measurement_type == "circle":
                result = measure_circle(gray, roi_info, measure_params)
            elif measurement_type == "arc":
                result = measure_arc(gray, roi_info, measure_params)
            elif measurement_type == "cross_caliper":
                # 提取UI参数
                ui_params = ctx.get("ui_params", {})
                result = measure_cross_caliper(gray, roi_info, measure_params, ui_params)
            elif measurement_type == "fillet_radius":
                # 可选corner_id参数，None表示测量全部四角
                corner_id = self.params.get("corner_id")
                result = measure_fillet_radius(gray, roi_info, measure_params, corner_id=corner_id)
            elif measurement_type == "angle":
                # 角度测量选项
                angle_options = {
                    'angle_method': self.params.get("angle_method", "shape_match")
                }
                result = measure_angle(gray, roi_info, measure_params, angle_options)
            else:
                result = {"ok": False, "error": f"不支持的测量类型: {measurement_type}"}

            # 更新上下文
            ctx["measurement_result"] = result

            # 调试绘制：总是显示ROI等可视化
            if self.params.get("debug_draw", True):
                img = self._draw_measurement_result(img, roi_info, result)

            # 打印结果
            if result.get("ok"):
                print(f"✅ {measurement_type}测量成功: {result}")
            else:
                print(f"❌ {measurement_type}测量失败: {result.get('error')}")

            return img, ctx

        except Exception as e:
            print(f"❌ 集成几何测量异常: {e}")
            ctx["measurement_result"] = {"ok": False, "error": str(e)}
            return img, ctx

    def _extract_roi_from_context(self, ctx) -> Optional[Dict[str, Any]]:
        """从上下文中提取ROI信息"""
        # 尝试多种可能的ROI来源
        roi_sources = [
            "pose",           # 模板匹配结果
            "template_match", # 模板匹配结果
            "shape_match",    # 形状匹配结果
            "roi",           # 直接ROI
        ]
        
        for source in roi_sources:
            roi_data = ctx.get(source)
            if roi_data is not None:
                return self._normalize_roi_format(roi_data, source)
        
        return None

    def _normalize_roi_format(self, roi_data, source_type) -> Dict[str, Any]:
        """标准化ROI格式"""
        if source_type in ["pose", "template_match", "shape_match"]:
            # 从模板匹配结果中提取ROI
            center = roi_data.get("center", (0, 0))
            len_x = roi_data.get("len_x", 100)
            len_y = roi_data.get("len_y", 100)
            angle = roi_data.get("angle", 0)
            
            corners = roi_data.get("corners")
            if not corners or len(corners)!=4:
                # 若上级未提供四角点，按矩形顶点计算（逆时针）
                import math, numpy as np
                a_rad = math.radians(angle)
                cos_a, sin_a = math.cos(a_rad), math.sin(a_rad)
                hx, hy = len_x/2.0, len_y/2.0
                corners = []
                for dx,dy in [(-hx,-hy),(hx,-hy),(hx,hy),(-hx,hy)]:
                    x = dx*cos_a - dy*sin_a + center[0]
                    y = dx*sin_a + dy*cos_a + center[1]
                    corners.append((x,y))
            return {
                "center": center,
                "len_x": len_x,
                "len_y": len_y,
                "angle": angle,
                "source": source_type,
                "corners": corners
            }
        else:
            # 直接ROI格式
            return roi_data

    def _draw_measurement_result(self, img, roi_info, result):
        """绘制测量结果"""
        import cv2
        import numpy as np
        import math

        # 确保图像是彩色的，以便正确显示彩色标记
        if len(img.shape) == 2 or (len(img.shape) == 3 and img.shape[2] == 1):
            # 灰度图转换为彩色图
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)

        # 无论测量是否成功，都绘制基础ROI可视化(矩形框、中心点、圆角采样圆)。
        success = bool(result.get("ok"))

        center = roi_info["center"]
        len_x = roi_info["len_x"]
        len_y = roi_info["len_y"]

        # 是否绘制绿色外框（避免干扰圆角拟合）
        draw_roi_box = self.params.get("show_roi_box", True)
        if self.params.get("measurement_type") == "fillet_radius" and not self.params.get("show_roi_box", False):
            draw_roi_box = False
        angle_deg = roi_info.get("angle", 0)

        # 计算旋转矩形的四个角点
        angle_rad = math.radians(angle_deg)
        cos_a, sin_a = math.cos(angle_rad), math.sin(angle_rad)
        half_x, half_y = len_x / 2, len_y / 2

        corners = []
        for x, y in [(-half_x, -half_y), (half_x, -half_y), (half_x, half_y), (-half_x, half_y)]:
            rx = x * cos_a - y * sin_a + center[0]
            ry = x * sin_a + y * cos_a + center[1]
            corners.append((int(rx), int(ry)))

        if draw_roi_box:
            pts = np.array(corners, np.int32).reshape((-1, 1, 2))
            cv2.polylines(img, [pts], True, (0, 255, 0), 2)
            # 中心点
            cv2.circle(img, (int(center[0]), int(center[1])), 5, (0, 255, 0), -1)

        # 若有四角点，绘制圆角采样圆（黄色）
        if roi_info.get("corners"):
            roi_r_cfg = self.params.get("fillet_roi_radius", 0)
            diag = float((len_x**2 + len_y**2) ** 0.5)
            roi_r_def = diag * 0.35
            roi_r_draw = roi_r_cfg if roi_r_cfg>0 else roi_r_def
            import cv2
            for (x_c,y_c) in roi_info["corners"]:
                cv2.circle(img, (int(round(x_c)), int(round(y_c))), int(round(roi_r_draw)), (0,255,255),1, lineType=cv2.LINE_AA)

        # 绘制测量结果文本
        if result.get("width") and result.get("height"):
            text = f"W:{result['width']:.1f} H:{result['height']:.1f}"
            cv2.putText(img, text, (int(center[0])+10, int(center[1])-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # 绘制弧形测量结果
        if result.get("radius") and result.get("arc_length_px"):
            arc_text = f"R:{result['radius']:.1f} Arc:{result['arc_length_px']:.1f}"
            cv2.putText(img, arc_text, (int(center[0])+10, int(center[1])-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)  # 橙色

            # 绘制圆心和半径圆
            if result.get("center"):
                arc_center = result["center"]
                cv2.circle(img, (int(arc_center[0]), int(arc_center[1])), 3, (255, 165, 0), -1)  # 圆心点
                cv2.circle(img, (int(arc_center[0]), int(arc_center[1])), int(result["radius"]), (255, 165, 0), 1)  # 半径圆

        # 绘制圆形测量结果
        elif result.get("radius") and result.get("diameter"):
            circle_text = f"D:{result['diameter']:.1f} R:{result['radius']:.1f}"
            cv2.putText(img, circle_text, (int(center[0])+10, int(center[1])-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)  # 青色

        # 绘制角度测量结果
        if result.get("angle") is not None:
            angle_text = f"Angle: {result['angle']:.1f}deg"
            cv2.putText(img, angle_text, (int(center[0])+10, int(center[1])+20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

        # 绘制角点ID和角度信息
        if result.get("corner_details"):
            for detail in result["corner_details"]:
                corner_id = detail['id']
                corner_x, corner_y = detail['x'], detail['y']
                corner_angle = detail['angle']

                # 绘制角点编号
                cv2.circle(img, (int(corner_x), int(corner_y)), 8, (255, 255, 0), 2)
                cv2.putText(img, str(corner_id), (int(corner_x)-5, int(corner_y)+5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)

                # 绘制角点角度
                angle_text = f"{corner_angle:.0f}deg"
                cv2.putText(img, angle_text, (int(corner_x)+15, int(corner_y)-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

        # 若返回了圆角信息，绘制圆弧
        if result.get("corners"):
            for i, c in enumerate(result["corners"]):
                cx, cy = c.get("center", (0, 0))
                r = c.get("radius", 0)
                if r and r > 1:
                    # 绘制拟合的圆
                    cv2.circle(img, (int(round(cx)), int(round(cy))), int(round(r)), (0, 0, 255), 2)
                    # 绘制拟合圆心
                    cv2.circle(img, (int(round(cx)), int(round(cy))), 3, (255, 0, 0), -1)

                    # 绘制轮廓点（用于调试拟合过程）
                    pts = c.get("pts")
                    if pts is not None and len(pts) > 0:
                        # 获取坐标转换信息
                        center_crop = c.get("center_crop", (0, 0))
                        roi_center = c.get("roi_center", (cx, cy))

                        # 将轮廓点从裁剪坐标转换到原图坐标
                        for pt in pts:
                            # 坐标转换：裁剪坐标 -> 原图坐标
                            pt_x = int(pt[0] - center_crop[0] + cx)
                            pt_y = int(pt[1] - center_crop[1] + cy)
                            cv2.circle(img, (pt_x, pt_y), 1, (0, 255, 0), -1)  # 绿色轮廓点

                    # 添加半径标签
                    cv2.putText(img, f'R{i}={r:.1f}',
                               (int(round(cx)) + 10, int(round(cy)) - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

                # 若携带局部ROI半径，绘制该窗口（黄色虚线）
                roi_r = c.get("roi_radius_px")
                roi_center = c.get("roi_center", (cx, cy))
                if roi_r and roi_r > 1:
                    cv2.circle(img, (int(round(roi_center[0])), int(round(roi_center[1]))), int(round(roi_r)), (0, 255, 255), 1, lineType=cv2.LINE_AA)

        return img

    def open_ui(self):
        """打开UI界面"""
        try:
            if self._ui_instance is None:
                # 懒加载UI模块
                ui_module = importlib.import_module(
                    "plugins.ui.geometry_tools.integrated_geometry_tool_ui"
                )
                self._ui_instance = ui_module.IntegratedGeometryToolUI(self)
            
            self._ui_instance.show()
            
        except Exception as e:
            print(f"❌ 打开UI失败: {e}")

    def close_ui(self):
        """关闭UI界面"""
        if self._ui_instance:
            self._ui_instance.close()
            self._ui_instance = None

    def save_config(self, file_path: str = None):
        """保存配置到文件"""
        import yaml
        from pathlib import Path

        try:
            if file_path is None:
                # 创建插件独享的配置目录，与其他插件保持一致
                cfg_dir = Path(__file__).resolve().parents[2] / 'configs' / 'integrated_geometry'
                cfg_dir.mkdir(parents=True, exist_ok=True)
                file_path = cfg_dir / 'default.yml'

            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(self.params, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ 配置已保存到: {file_path}")
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")

    def load_config(self, file_path: str = None):
        """从文件加载配置"""
        import yaml
        from pathlib import Path

        try:
            if file_path is None:
                # 使用路径管理模块获取配置目录
                try:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                    from path_manager import get_plugin_config_path
                    file_path = get_plugin_config_path('integrated_geometry', 'default.yml')
                except ImportError:
                    # 回退到原来的逻辑
                    cfg_dir = Path(__file__).resolve().parents[2] / 'configs' / 'integrated_geometry'
                    file_path = cfg_dir / 'default.yml'

                # 如果默认配置文件不存在，先保存当前参数作为默认配置
                if not Path(file_path).exists():
                    self.save_config()
                    return

            with open(file_path, 'r', encoding='utf-8') as f:
                loaded_params = yaml.safe_load(f)

            # 更新参数
            if loaded_params:
                self.params.update(loaded_params)

            print(f"✅ 配置已从文件加载: {file_path}")
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")

    def _load_workstation_config(self):
        """加载工位配置文件"""
        try:
            from pathlib import Path
            import os

            # 方法1: 尝试从环境变量获取当前工位路径
            workstation_path = os.environ.get('CURRENT_WORKSTATION_PATH')
            if workstation_path:
                config_file = Path(workstation_path) / "integrated_geometry.yml"
                if config_file.exists():
                    print(f"📁 从环境变量找到工位配置: {config_file}")
                    self.load_config(str(config_file))
                    return

            # 方法2: 尝试从当前工作目录找到工位配置文件
            cwd = Path.cwd()
            config_file = cwd / "integrated_geometry.yml"
            if config_file.exists():
                print(f"📁 从当前目录找到工位配置: {config_file}")
                self.load_config(str(config_file))
                return

            # 方法3: 搜索recipes目录下的工位配置
            try:
                from path_manager import get_recipe_path
                recipes_dir = Path(get_recipe_path())
            except ImportError:
                recipes_dir = Path(__file__).resolve().parents[2] / "recipes"
            if recipes_dir.exists():
                for recipe_dir in recipes_dir.iterdir():
                    if recipe_dir.is_dir():
                        for ws_dir in recipe_dir.iterdir():
                            if ws_dir.is_dir() and ws_dir.name.startswith('ws'):
                                config_file = ws_dir / "integrated_geometry.yml"
                                if config_file.exists():
                                    print(f"📁 搜索到工位配置: {config_file}")
                                    self.load_config(str(config_file))
                                    return

            print(f"📁 未找到工位配置文件，使用默认配置")

        except Exception as e:
            print(f"❌ 加载工位配置失败: {e}")

    def _execute_builtin_template_matching(self, img):
        """执行内置模板匹配"""
        try:
            # 检查是否有模板文件配置
            template_file = self.params.get("template_file", "")
            if not template_file:
                print("⚠️ 未配置模板文件")
                return None

            # 尝试加载模板文件
            from pathlib import Path
            template_path = Path(template_file)

            # 使用路径管理模块搜索模板文件
            if not template_path.is_absolute() and not template_path.exists():
                try:
                    from path_manager import get_recipe_path, get_plugin_config_path

                    # 尝试在 recipes 目录中递归查找
                    rel_pattern = template_file.replace("\\", "/").lstrip("./")
                    print(f"[DEBUG] 在recipes目录递归搜索模板: {rel_pattern}")

                    recipes_dir = Path(get_recipe_path())
                    for candidate in recipes_dir.rglob(rel_pattern):
                        if candidate.is_file():
                            print(f"[DEBUG] 相对路径未找到，使用搜索到的模板: {candidate}")
                            template_path = candidate
                            break

                    # 如果还是找不到，尝试在插件配置目录中查找
                    if not template_path.exists():
                        template_path = Path(get_plugin_config_path('integrated_geometry', template_file))
                        print(f"[DEBUG] 在配置目录查找模板: {template_path}")

                except ImportError:
                    # 回退到原来的逻辑
                    proj_root = Path(__file__).resolve().parents[2]
                    rel_pattern = template_file.replace("\\", "/").lstrip("./")

                    for candidate in (proj_root / "recipes").rglob(rel_pattern):
                        if candidate.is_file():
                            template_path = candidate
                            break

                    if not template_path.exists():
                        cfg_dir = proj_root / 'configs' / 'integrated_geometry'
                        template_path = cfg_dir / template_file

                # 最后尝试在当前工作目录查找
                if not template_path.exists():
                    template_path = Path.cwd() / template_file
                    print(f"[DEBUG] 在当前目录查找模板: {template_path}")

            if not template_path.exists():
                print(f"⚠️ 模板文件不存在: {template_path}")
                return None

            import cv2
            template = cv2.imread(str(template_path), cv2.IMREAD_GRAYSCALE)
            if template is None:
                print(f"⚠️ 无法加载模板文件: {template_path}")
                return None

            # 转换输入图像为灰度图
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            else:
                gray = img.copy()

            # 根据匹配方式执行匹配
            match_method = self.params.get("match_method", "template")
            match_threshold = self.params.get("match_threshold", 0.8)

            print(f"[DEBUG] 当前匹配方法: {match_method}, 阈值: {match_threshold}")

            if match_method == "shape":
                print(f"[DEBUG] 执行形状匹配")
                return self._shape_matching(gray, template, match_threshold)
            else:
                print(f"[DEBUG] 执行模板匹配")
                return self._template_matching(gray, template, match_threshold)

        except Exception as e:
            print(f"❌ 内置模板匹配异常: {e}")
            return None

    def _shape_matching(self, img, template, min_conf):
        """形状匹配"""
        try:
            import cv2
            import numpy as np

            # 二值化
            _, img_bin = cv2.threshold(img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            _, tpl_bin = cv2.threshold(template, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 查找轮廓
            img_contours, _ = cv2.findContours(img_bin, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            tpl_contours, _ = cv2.findContours(tpl_bin, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not img_contours or not tpl_contours:
                return None

            # 选择最大轮廓
            img_cnt = max(img_contours, key=cv2.contourArea)
            tpl_cnt = max(tpl_contours, key=cv2.contourArea)

            # 计算匹配度
            match_score = cv2.matchShapes(img_cnt, tpl_cnt, cv2.CONTOURS_MATCH_I1, 0)
            score = 1.0 / (1.0 + match_score)  # 转换为0-1的置信度

            if score >= min_conf:
                # 🔧 修复：使用minAreaRect计算旋转矩形（与UI保持一致）
                # 计算轮廓中心
                M = cv2.moments(img_cnt)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                else:
                    # 如果无法计算质心，使用边界框中心
                    rect = cv2.boundingRect(img_cnt)
                    cx = rect[0] + rect[2]//2
                    cy = rect[1] + rect[3]//2

                # 计算轮廓的最小外接矩形（带角度）
                rect = cv2.minAreaRect(img_cnt)
                angle = rect[2]  # 角度

                # 调整角度到合理范围
                if angle < -45:
                    angle += 90

                return {
                    "center": (cx, cy),
                    "len_x": rect[1][0],
                    "len_y": rect[1][1],
                    "angle": angle,
                    "score": score,
                    "method": "shape"
                }
            return None

        except Exception as e:
            print(f"❌ 形状匹配异常: {e}")
            return None

    def _template_matching(self, img, template, min_conf):
        """模板匹配"""
        try:
            import cv2
            import numpy as np

            # 执行模板匹配
            result = cv2.matchTemplate(img, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            if max_val >= min_conf:
                h, w = template.shape[:2]
                center = (max_loc[0] + w//2, max_loc[1] + h//2)

                return {
                    "center": center,
                    "len_x": w,
                    "len_y": h,
                    "angle": 0,
                    "score": max_val,
                    "method": "template"
                }
            return None

        except Exception as e:
            print(f"❌ 模板匹配异常: {e}")
            return None

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img=None):
        """打开完整的集成几何工具UI界面。"""
        try:
            import importlib
            mod = importlib.import_module('plugins.ui.geometry_tools.integrated_geometry_tool_ui')
            ui_cls = getattr(mod, 'IntegratedGeometryToolUI', None)
            if ui_cls is None:
                from tkinter import messagebox
                messagebox.showerror('错误', '未找到 IntegratedGeometryToolUI')
                return

            # 创建插件实例并设置参数
            plugin_instance = IntegratedGeometryToolPlugin()
            plugin_instance.params.update(params)

            # 创建集成几何工具UI
            geometry_ui = ui_cls(plugin_instance)

            # 如果有预览图像，设置到UI中
            if preview_img is not None:
                geometry_ui.img_src = preview_img
                geometry_ui.img_original = preview_img.copy()

            # 显示UI界面
            geometry_ui.show()

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror('错误', f'打开集成几何工具失败: {str(e)}')


# 插件实例（供插件系统自动发现）
plugin_instance = IntegratedGeometryToolPlugin()
