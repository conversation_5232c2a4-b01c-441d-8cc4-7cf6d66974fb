from .camera_base import CameraBase
from .camera_hikvision import CameraHikvision
from .camera_factory import CameraFactory
from .camera_worker import CameraWorker
from .camera_params import CameraParams
from pathlib import Path
from typing import Union, List
import yaml
import sys
import os

# 导入路径管理模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from path_manager import get_config_path

class CameraManager:
    """统一管理所有相机的生命周期与状态。

    使用方法：
        mgr = CameraManager(path_to_yaml)
        mgr.start_all()
    """
    def __init__(self, cfg_source: Union[str, Path, List[dict]]):
        self.cameras: dict[str, CameraBase] = {}
        self.workers: dict[str, CameraWorker] = {}
        self._cfg_path: Path | None = None
        # 根据输入类型加载
        if isinstance(cfg_source, (str, Path)):
            self._cfg_path = Path(cfg_source)
            camera_cfgs = self._load_yaml(self._cfg_path)
        else:
            camera_cfgs = cfg_source  # 直接 list[dict]
        self._init_cameras(camera_cfgs)

    # ---------- 加载 ----------
    @staticmethod
    def _load_yaml(path: Path) -> List[dict]:
        if not path.exists():
            raise FileNotFoundError(path)
        with open(path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f) or {}
        return data.get('cameras', [])

    def _init_cameras(self, camera_configs: List[dict]):
        # 先停止并清理已有
        self.stop_all()
        self.cameras.clear()
        self.workers.clear()
        for cfg in camera_configs:
            cam = CameraFactory.create(cfg)
            # 应用参数文件
            param_file = cfg.get('param_file')
            if param_file:
                param_path = Path(get_config_path(param_file))
                if param_path.exists():
                    params_dict = yaml.safe_load(param_path.read_text(encoding='utf-8')) or {}
                    if hasattr(cam, 'apply_params'):
                        cam.apply_params(CameraParams.from_dict(params_dict))
            self.cameras[cfg['id']] = cam
            self.workers[cfg['id']] = CameraWorker(cam)

    # ---------- 热重载 ----------
    def reload(self):
        if self._cfg_path is None:
            raise RuntimeError("CameraManager 未使用 YAML 初始化, 无法 reload")
        camera_cfgs = self._load_yaml(self._cfg_path)
        self._init_cameras(camera_cfgs)

    # ---------- 运行控制 ----------
    def start_all(self):
        for worker in self.workers.values():
            worker.start()

    def stop_all(self):
        for worker in self.workers.values():
            worker.stop()

    # ---------- 查询 ----------
    def get_camera(self, camera_id):
        return self.cameras.get(camera_id)

    def get_status(self):
        return {cid: cam.get_status() for cid, cam in self.cameras.items()}

    def init_cameras(self, camera_configs):
        for cfg in camera_configs:
            cam = CameraFactory.create(cfg)
            self.cameras[cfg['id']] = cam
            self.workers[cfg['id']] = CameraWorker(cam)

    def start_all(self):
        for worker in self.workers.values():
            worker.start()

    def stop_all(self):
        for worker in self.workers.values():
            worker.stop()

    def get_camera(self, camera_id):
        return self.cameras.get(camera_id)

    def get_status(self):
        return {cid: cam.get_status() for cid, cam in self.cameras.items()}
