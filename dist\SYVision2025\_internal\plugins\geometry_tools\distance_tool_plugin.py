"""Distance 工具插件包装."""
from __future__ import annotations
from typing import Dict, Any
import importlib
from plugins.plugin_base import PluginBase

class DistanceToolPlugin(PluginBase):
    name = "distance_tool"
    label = "距离测量"
    category = "几何测量"
    params: Dict[str, Any] = {}

    def process(self, img, ctx):
        return img, ctx

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        """调用 DistanceTool UI (内置)."""
        try:
            mod = importlib.import_module('plugins.geometry_tools.distance_tool')
            ui_cls = getattr(mod, 'DistanceTool', None)
            if ui_cls:
                if preview_img is not None:
                    ui_cls(master, img=preview_img)
                else:
                    ui_cls(master)
            else:
                from tkinter import messagebox
                messagebox.showerror('错误', '未找到 DistanceTool UI')
        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror('错误', f'打开距离测量工具失败: {str(e)}')
            import traceback
            traceback.print_exc()

_ = DistanceToolPlugin()
