"""Camera calibration plugin wrapper.

Expose CalibrateEngine as a Pipeline plugin so that the user can collect chessboard
views, run calibration, save/load intrinsics, and optionally undistort frames during
runtime. The heavy-lifting is already implemented in `camera_calibrate.py`.
"""
from __future__ import annotations

from typing import Dict, Any
from pathlib import Path
import importlib
import cv2

from plugins.plugin_base import PluginBase
from plugins.camera_calibrate.camera_calibrate import CalibrateEngine, load_calibration

__all__ = ["CameraCalibPlugin"]


class CameraCalibPlugin(PluginBase):
    """相机标定插件 (Pipeline Wrapper)"""

    name = "camera_calibrate"
    label = "相机标定"
    category = "系统"

    params: Dict[str, Any] = {
        "calib_file": "calib/intrinsics.yml",   # 樱桃小组件默认路径
        "preview_undistort": False,              # 运行时是否实时去畸变
    }

    param_labels = {
        "calib_file": "标定文件",
        "preview_undistort": "实时去畸变",
    }

    # --------------------------------------------------
    def setup(self, params: Dict[str, Any]):  # type: ignore[override]
        self._engine: CalibrateEngine | None = None
        self._calib = None
        yml = Path(params.get("calib_file", ""))
        if yml.is_file():
            try:
                self._calib = load_calibration(yml)
            except Exception as e:
                print(f"[CamCalib] Failed to load {yml}: {e}")
                self._calib = None
        if self._calib:
            print(f"[CamCalib] Loaded calib from {yml}")

    def process(self, img, ctx):  # type: ignore[override]
        if self._calib:
            ctx["camera_calib"] = {
                "K": self._calib.K,
                "dist": self._calib.dist,
                "img_size": self._calib.img_size,
            }
            if self.params.get("preview_undistort", False) and img is not None:
                try:
                    img = cv2.undistort(img, self._calib.K, self._calib.dist)
                except Exception as e:
                    print(f"[CamCalib] undistort failed: {e}")
        return img, ctx

    # -------------------------------------------------- UI
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, **extra):
        """弹出简易标定界面，完成采集+标定+保存。"""
        import tkinter as tk
        from tkinter import messagebox

        try:
            ui_mod = importlib.import_module("plugins.ui.camera_calibrate.calibrate_ui")
            FrameCls = getattr(ui_mod, "CameraCalibFrame")
        except Exception as e:
            messagebox.showerror("错误", f"加载标定 UI 失败: {e}")
            return

        win = tk.Toplevel(master)
        win.title("相机标定")
        frame: "CameraCalibrateFrame" = FrameCls(win, calib_path=params.get("calib_file"))
        frame.pack(fill=tk.BOTH, expand=True)

        def _on_close():
            # 标定完成后，UI 内部会把结果路径写到 frame.result_path
            if getattr(frame, "result_path", None):
                on_change({"calib_file": frame.result_path})
            win.destroy()

        win.protocol("WM_DELETE_WINDOW", _on_close)
        win.grab_set()
        win.transient(master)


# 注册实例
_ = CameraCalibPlugin()
