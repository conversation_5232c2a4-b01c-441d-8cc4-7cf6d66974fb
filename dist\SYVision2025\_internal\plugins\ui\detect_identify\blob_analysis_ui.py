import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
import sys, os
import cv2, yaml, time
import numpy as np

# ensure project root in path
BASE_DIR = Path(__file__).resolve().parents[3]
if str(BASE_DIR) not in sys.path:
    sys.path.append(str(BASE_DIR))

from plugins.DetectIdentify.blob_analysis import load_analyzer, BlobAnalyzer

PREVIEW_MAX = 600
CFG_DIR = Path(__file__).resolve().parents[3] / 'configs' / 'blob'
CFG_DIR.mkdir(parents=True, exist_ok=True)
DEFAULT_CFG = CFG_DIR / 'default.yml'

class BlobAnalysisFrame(ttk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.analyzer: BlobAnalyzer | None = None
        self.img_src: np.ndarray | None = None
        # ---- params ----
        self.mode_var = tk.StringVar(value='otsu')
        self.fixed_th = tk.IntVar(value=128)
        self.adapt_blk = tk.IntVar(value=31)
        self.adapt_C = tk.IntVar(value=5)
        self.min_area = tk.IntVar(value=50)
        self.max_area = tk.IntVar(value=0)
        self.use_roi_var = tk.BooleanVar(value=False)
        self.tpl_thresh = tk.DoubleVar(value=0.8)
        self.expand_var = tk.DoubleVar(value=10)
        self.mm_per_px = tk.DoubleVar(value=0.01)
        self.ng_count_th = tk.IntVar(value=1)
        self.ng_area_th = tk.DoubleVar(value=0.5)  # mm^2
        self._build_ui()

    # ---------------- UI -----------------
    def _build_ui(self):
        pfrm = ttk.LabelFrame(self, text='参数')
        pfrm.grid(row=0, column=0, sticky='ns')
        # image load
        ttk.Button(pfrm, text='加载图片', command=self._open_image).grid(row=0, column=0, columnspan=2, sticky='we')
        ttk.Separator(pfrm, orient='horizontal').grid(row=1, column=0, columnspan=2, sticky='we', pady=2)
        # threshold mode
        ttk.Label(pfrm, text='模式').grid(row=2, column=0, sticky='e')
        ttk.Combobox(pfrm, values=['otsu', 'fixed', 'adaptive'], textvariable=self.mode_var, width=8, state='readonly').grid(row=2, column=1)
        ttk.Label(pfrm, text='固定阈').grid(row=3, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.fixed_th, width=6).grid(row=3, column=1)
        ttk.Label(pfrm, text='块').grid(row=4, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.adapt_blk, width=6).grid(row=4, column=1)
        ttk.Label(pfrm, text='C').grid(row=5, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.adapt_C, width=6).grid(row=5, column=1)
        ttk.Separator(pfrm, orient='horizontal').grid(row=6, column=0, columnspan=2, sticky='we', pady=2)
        # area filter
        ttk.Label(pfrm, text='最小面积').grid(row=7, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.min_area, width=6).grid(row=7, column=1)
        ttk.Label(pfrm, text='最大面积').grid(row=8, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.max_area, width=6).grid(row=8, column=1)
        ttk.Separator(pfrm, orient='horizontal').grid(row=9, column=0, columnspan=2, sticky='we', pady=2)
        # ROI controls
        ttk.Checkbutton(pfrm, text='使用模板ROI', variable=self.use_roi_var).grid(row=10, column=0, columnspan=2, sticky='w')
        ttk.Button(pfrm, text='制作模板', command=self._make_template).grid(row=11, column=0, columnspan=2, sticky='we')
        ttk.Label(pfrm, text='ROI阈值').grid(row=12, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.tpl_thresh, width=6).grid(row=12, column=1)
        ttk.Label(pfrm, text='外扩%').grid(row=13, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.expand_var, width=6).grid(row=13, column=1)
        ttk.Label(pfrm, text='mm/px').grid(row=14, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.mm_per_px, width=6).grid(row=14, column=1)
        ttk.Separator(pfrm, orient='horizontal').grid(row=15, column=0, columnspan=2, sticky='we', pady=2)
        # NG thresholds
        ttk.Label(pfrm, text='NG计数>=').grid(row=16, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.ng_count_th, width=6).grid(row=16, column=1)
        ttk.Label(pfrm, text='NG面积>=').grid(row=17, column=0, sticky='e')
        ttk.Entry(pfrm, textvariable=self.ng_area_th, width=6).grid(row=17, column=1)
        ttk.Button(pfrm, text='运行', command=self._run).grid(row=18, column=0, columnspan=2, sticky='we')
        ttk.Button(pfrm, text='保存配置', command=self._save_cfg).grid(row=19, column=0, sticky='we')
        ttk.Button(pfrm, text='加载配置', command=self._load_cfg).grid(row=19, column=1, sticky='we')

        # preview canvases
        self.canvas_src = tk.Canvas(self, width=PREVIEW_MAX, height=PREVIEW_MAX, bg='black')
        self.canvas_dst = tk.Canvas(self, width=PREVIEW_MAX, height=PREVIEW_MAX, bg='black')
        self.canvas_src.grid(row=0, column=1)
        self.canvas_dst.grid(row=0, column=2)
        self.info_var = tk.StringVar(value='')
        ttk.Label(self, textvariable=self.info_var).grid(row=1, column=0, columnspan=3, sticky='w')

    # ---------------- actions -----------------
    def _open_image(self):
        path = filedialog.askopenfilename(filetypes=[('Images', '*.jpg *.png *.bmp *.tif *.tiff')])
        if not path:
            return
        img = cv2.imdecode(np.fromfile(path, dtype=np.uint8), cv2.IMREAD_COLOR)
        if img is None:
            messagebox.showerror('错误', '无法读取图片')
            return
        self.img_src = img
        self._show_image(self.canvas_src, img)
        if self.analyzer is None:
            self.analyzer = load_analyzer()

    def _make_template(self):
        if self.img_src is None:
            messagebox.showwarning('提示', '请先加载图片'); return
        if self.analyzer is None:
            self.analyzer = load_analyzer()
        try:
            self.analyzer.make_template_from_image(self.img_src, self.tpl_thresh.get())
            messagebox.showinfo('模板', '模板已创建')
        except Exception as e:
            messagebox.showerror('错误', str(e))

    def _run(self):
        if self.img_src is None:
            messagebox.showwarning('提示', '请先加载图片'); return
        if self.analyzer is None:
            self.analyzer = load_analyzer()
        # set params
        self.analyzer.mode = self.mode_var.get()
        self.analyzer.fixed_th = self.fixed_th.get()
        self.analyzer.adapt_block = self.adapt_blk.get()
        self.analyzer.adapt_C = self.adapt_C.get()
        self.analyzer.min_area = self.min_area.get()
        self.analyzer.max_area = self.max_area.get()
        self.analyzer.tpl_threshold = self.tpl_thresh.get()
        self.analyzer.roi_expand = self.expand_var.get()/100.0
        t0 = time.perf_counter()
        stats = self.analyzer.analyze(self.img_src, use_roi=self.use_roi_var.get())
        infer_ms = (time.perf_counter() - t0) * 1000
        out = self.img_src.copy()
        # draw ROI
        if self.use_roi_var.get() and self.analyzer.last_roi_pts is not None:
            cv2.polylines(out, [self.analyzer.last_roi_pts], True, (0, 255, 0), 1)
        mmpp = self.mm_per_px.get() if self.mm_per_px.get() > 0 else 1.0
        overall_ng = False
        for r in stats['results']:
            x1, y1, x2, y2 = r['box']
            wmm = (x2 - x1) * mmpp
            hmm = (y2 - y1) * mmpp
            area_mm = (x2 - x1) * (y2 - y1) * mmpp * mmpp
            is_ng = (area_mm >= self.ng_area_th.get())
            color = (0, 0, 255) if is_ng else (0, 255, 0)
            if is_ng:
                overall_ng = True
            cv2.rectangle(out, (x1, y1), (x2, y2), color, 2)
        if stats['count'] >= self.ng_count_th.get():
            overall_ng = True
        self._show_image(self.canvas_dst, out)
        txt = f"Count:{stats['count']} AvgArea:{stats['avg_area']:.1f}  耗时:{infer_ms:.1f}ms  结果:{'NG' if overall_ng else 'OK'}"
        self.info_var.set(txt)

    # ---------------- utils -----------------
    def _show_image(self, canvas: tk.Canvas, img: np.ndarray):
        h, w = img.shape[:2]
        scale = PREVIEW_MAX / max(h, w)
        nh, nw = int(h * scale), int(w * scale)
        im_resized = cv2.resize(img, (nw, nh))
        im_rgb = cv2.cvtColor(im_resized, cv2.COLOR_BGR2RGB)
        from PIL import Image, ImageTk
        im_pil = ImageTk.PhotoImage(image=Image.fromarray(im_rgb))
        canvas.delete('all')
        canvas.image = im_pil
        canvas.create_image(PREVIEW_MAX / 2, PREVIEW_MAX / 2, image=im_pil)

    # ---------------- config save/load -----------------
    def _save_cfg(self):
        cfg = {
            'mode': self.mode_var.get(),
            'fixed_th': self.fixed_th.get(),
            'adapt_blk': self.adapt_blk.get(),
            'adapt_C': self.adapt_C.get(),
            'min_area': self.min_area.get(),
            'max_area': self.max_area.get(),
            'use_roi': self.use_roi_var.get(),
            'tpl_thresh': self.tpl_thresh.get(),
            'roi_expand': self.expand_var.get(),
            'mm_per_px': self.mm_per_px.get(),
            'ng_count_th': self.ng_count_th.get(),
            'ng_area_th': self.ng_area_th.get(),
            'template_npy': None,
        }
        if self.analyzer and self.analyzer.tpl_cnt is not None:
            tpl_path = CFG_DIR / 'template_cnt.npy'
            np.save(tpl_path, self.analyzer.tpl_cnt)
            cfg['template_npy'] = tpl_path.name
        path = filedialog.asksaveasfilename(defaultextension='.yml', initialfile=DEFAULT_CFG.name, initialdir=str(CFG_DIR), filetypes=[('YAML', '*.yml')])
        if not path:
            path = DEFAULT_CFG
        with open(path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(cfg, f, allow_unicode=True)
        messagebox.showinfo('配置', '保存成功')

    def _load_cfg(self):
        path = DEFAULT_CFG if DEFAULT_CFG.exists() else ''
        if not path:
            path = filedialog.askopenfilename(initialdir=str(CFG_DIR), filetypes=[('YAML', '*.yml')])
            if not path:
                return
        with open(path, 'r', encoding='utf-8') as f:
            cfg = yaml.safe_load(f) or {}
        self.mode_var.set(cfg.get('mode', 'otsu'))
        self.fixed_th.set(cfg.get('fixed_th', 128))
        self.adapt_blk.set(cfg.get('adapt_blk', 31))
        self.adapt_C.set(cfg.get('adapt_C', 5))
        self.min_area.set(cfg.get('min_area', 50))
        self.max_area.set(cfg.get('max_area', 0))
        self.use_roi_var.set(cfg.get('use_roi', False))
        self.tpl_thresh.set(cfg.get('tpl_thresh', 0.8))
        self.expand_var.set(cfg.get('roi_expand', 10))
        self.mm_per_px.set(cfg.get('mm_per_px', 0.01))
        self.ng_count_th.set(cfg.get('ng_count_th', 1))
        self.ng_area_th.set(cfg.get('ng_area_th', 0.5))
        if self.analyzer is None:
            self.analyzer = load_analyzer()
        tpl_file = cfg.get('template_npy')
        if tpl_file:
            p = CFG_DIR / tpl_file
            if p.exists():
                try:
                    self.analyzer.tpl_cnt = np.load(p, allow_pickle=False)
                except Exception:
                    pass
        messagebox.showinfo('配置', '加载完成')


if __name__ == '__main__':
    root = tk.Tk(); root.title('斑点分析'); BlobAnalysisFrame(root).pack(); root.mainloop()
