"""Caliper 工具插件包装."""
from __future__ import annotations
from typing import Any, Dict
import importlib
from plugins.plugin_base import PluginBase

class CaliperToolPlugin(PluginBase):
    name = "caliper_tool"
    label = "卡尺测量"
    category = "几何测量"
    params: Dict[str, Any] = {}

    def process(self, img, ctx):
        return img, ctx

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img=None):
        try:
            mod = importlib.import_module('plugins.ui.geometry_tools.caliper_tool_ui')
            ui_cls = getattr(mod, 'CaliperToolUI', None)
            if ui_cls:
                if preview_img is not None:
                    ui_cls(master, img=preview_img)
                else:
                    ui_cls(master)
            else:
                from tkinter import messagebox
                messagebox.showerror('错误', '未找到 CaliperToolUI')
        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror('错误', f'打开卡尺测量工具失败: {str(e)}')
            import traceback
            traceback.print_exc()

_ = CaliperToolPlugin()
