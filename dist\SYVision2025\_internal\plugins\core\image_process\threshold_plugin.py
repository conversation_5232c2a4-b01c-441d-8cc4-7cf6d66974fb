"""Pipeline plugin wrapper for ThresholdProcessor."""
from __future__ import annotations
from typing import Dict, Any
import numpy as np

from plugins.plugin_base import PluginBase
from plugins.core.image_process.threshold import ThresholdProcessor

__all__ = ["Threshold"]

class Threshold(PluginBase):
    """阈值分割流水线插件"""

    name = "threshold"
    label = "阈值分割"

    params: Dict[str, Any] = {
        "mode": "binary",       # binary|binary_inv|otsu|triangle|adapt_mean|adapt_gauss
        "thresh_val": 128,
    }

    param_labels = {
        "mode": "模式",
        "thresh_val": "阈值",
    }

    def __init__(self, **params):
        super().__init__(**params)
        self._proc = ThresholdProcessor(**self.params)

    def setup(self, params: Dict[str, Any]):
        self._proc = ThresholdProcessor(**params)

    def process(self, img, ctx):
        if img is None:
            return img, ctx
        res = self._proc.process(img)
        ctx[self.name] = {}
        return res["output"], ctx

    # ---------------- UI -----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        import importlib, tkinter as tk
        mod = importlib.import_module('plugins.ui.image_process.threshold_ui')
        if hasattr(mod, 'ThresholdFrame'):
            win = tk.Toplevel(master)
            frame = mod.ThresholdFrame(win)
            frame.pack(fill='both', expand=True)

            if preview_img is not None and hasattr(frame, 'load_image'):
                try:
                    frame.load_image(preview_img)
                except Exception:
                    pass

            frame.set_params(params)
            def _ok():
                p = frame.get_params()
                on_change(p)
                win.destroy()
            tk.Button(win, text='确定', command=_ok).pack(pady=2)
        else:
            from tkinter import messagebox
            messagebox.showerror('错误', '未找到 ThresholdFrame')


# ensure registration
_ = Threshold()