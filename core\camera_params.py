"""相机参数数据结构封装。
统一管理 UI ↔ SDK 之间传递的参数，后续添加新字段只需在此处扩展。
"""
from dataclasses import dataclass, asdict

__all__ = ["CameraParams"]


@dataclass
class CameraParams:
    ExposureTime: float = 10000.0  # us
    Gain: float = 0.0  # dB
    GainAuto: bool = True  # 自动增益模式，True=自动，False=手动
    AcquisitionMode: str = "连续采集"  # 连续采集 / 单帧 / 多帧
    TriggerSource: str = ""  # "软件" / "线路0" 等
    TriggerActivation: str = "Rising"  # Rising / Falling
    TriggerDelay: float = 0.0  # us

    # —— 工具方法 ——
    def to_dict(self):
        """序列化为普通 dict，方便 YAML / JSON 持久化。"""
        return asdict(self)

    @staticmethod
    def from_dict(d: dict):
        """根据已有字典构造 CameraParams；未提供字段使用默认值。"""
        base = CameraParams()
        base_dict = base.to_dict()
        base_dict.update(d or {})
        return CameraParams(**base_dict)
