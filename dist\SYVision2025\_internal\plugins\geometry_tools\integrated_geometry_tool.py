# -*- coding: utf-8 -*-
"""集成几何测量工具 - 核心算法

这个模块提供集成的几何测量功能，支持：
- 矩形卡尺测量
- 圆形测量  
- 弧形测量
- 能够继承上级ROI信息

API使用示例:
    >>> from plugins.geometry_tools.integrated_geometry_tool import measure_rectangle
    >>> result = measure_rectangle(img, roi_info, params)
    >>> print(result['width'], result['height'])
"""
from __future__ import annotations

import cv2
import numpy as np

from dataclasses import dataclass
from typing import Tuple, List, Dict, Any

__all__ = [
    #'measure_rectangle',  # 删除矩形卡尺测量导出
    'measure_circle',
    'measure_arc',
    'measure_cross_caliper',
    'GeometryMeasureParams',
    'MeasureTask',
    'run_tasks',
    'measure_fillet_radius',
    'measure_angle',
]


@dataclass
class GeometryMeasureParams:
    """几何测量参数"""
    scan_lines: int = 12        # 扫描线数量
    line_width: int = 8         # 线宽
    edge_threshold: int = 15    # 边缘阈值
    polarity: str = "BOTH"      # 边缘极性: BOTH, POSITIVE, NEGATIVE
    roi_expand_ratio: float = 1.0  # ROI扩张比例
    fillet_roi_radius: float = 0.0  # 圆角局部ROI半径(像素)，<=0 表示自动(diag*0.35)
    debug_log: bool = False        # 是否输出调试日志

@dataclass
class MeasureTask:
    """测量任务配置"""
    type: str            # 'cross_caliper' | 'circle' | 'arc' | 'fillet_radius'
    params: dict | None = None  # 可覆盖默认 GeometryMeasureParams


# ------------------------- 简易扫描线卡尺算法 (Metrology-Demo 改写) -------------------------

from collections import namedtuple



def _rotate_and_crop(img: np.ndarray, center: Tuple[float, float], len_x: float, len_y: float, angle_deg: float):
    """将 ROI 旋转到水平，返回旋转后的图像及仿射矩阵"""
    h, w = img.shape[:2]
    M = cv2.getRotationMatrix2D(center, angle_deg, 1.0)  # 顺时针需要正角度
    rot = cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_LINEAR)
    # 计算矩形四角在旋转后的位置
    c = np.cos(np.radians(angle_deg)); s = np.sin(np.radians(angle_deg))
    dx, dy = len_x / 2, len_y / 2
    pts = np.array([[ -dx, -dy], [ dx, -dy], [ dx, dy], [ -dx, dy]], dtype=np.float32)
    R = np.array([[c, -s],[s, c]])
    pts_rot = (pts @ R.T) + np.array(center)
    x_coords, y_coords = pts_rot[:,0], pts_rot[:,1]
    x_min, x_max = int(x_coords.min()), int(x_coords.max())
    y_min, y_max = int(y_coords.min()), int(y_coords.max())
    crop = rot[y_min:y_max, x_min:x_max].copy()
    return crop, (x_min, y_min), M

def _extract_edges_1d(profile: np.ndarray, edge_threshold: float, polarity: str):
    """在一维灰度 profile 中寻找左右/上下两个主要边缘位置

    原实现仅取第一个满足阈值的正/负梯度，容易受到噪声干扰。
    新实现策略：
    1. 计算一阶差分 diff。
    2. 选取 diff >  th 作为上升沿候选（进入亮区），diff < -th 作为下降沿候选（离开亮区）。
    3. 取 *最左侧* 上升沿作为 left/top；取 *最右侧* 下降沿作为 right/bottom。
       这样可避免背景噪声导致的错误配对。
    4. 若只检测到一种极性，返回 (None, None) 让上层逻辑丢弃该扫描线。
    """
    diff = np.diff(profile.astype(np.float32))

    pos_candidates = []
    neg_candidates = []

    if polarity in ('POSITIVE', 'BOTH'):
        pos_candidates = np.where(diff > edge_threshold)[0]
    if polarity in ('NEGATIVE', 'BOTH'):
        neg_candidates = np.where(diff < -edge_threshold)[0]

    if len(pos_candidates) == 0 or len(neg_candidates) == 0:
        # 没有检测到双边缘，返回 None 交由上层过滤
        return None, None

    left = int(pos_candidates[0])
    right = int(neg_candidates[-1])

    # 确保顺序正确
    if left >= right:
        # 极少数情况下顺序不对，尝试重新配对
        # 使用全局最大梯度法：取最显著上升沿和下降沿
        if len(pos_candidates):
            left = int(pos_candidates[np.argmax(diff[pos_candidates])])
        if len(neg_candidates):
            right = int(neg_candidates[np.argmin(diff[neg_candidates])])
        if left >= right:
            return None, None

    return left, right

def _scan_caliper(crop: np.ndarray, n_lines: int, line_width: int, edge_threshold: int, polarity: str):
    """在 crop 水平扫描 n_lines 条，返回水平宽度集合、水平边缘点列表"""
    h, w = crop.shape[:2]
    ys = np.linspace(line_width, h - line_width - 1, n_lines, dtype=int)
    widths = []
    h_edges = []
    for y in ys:
        # 取 line_width 行平均，降噪
        band = crop[y - line_width//2 : y + line_width//2 + 1, :]
        profile = band.mean(axis=0)
        left, right = _extract_edges_1d(profile, edge_threshold, polarity)
        if left is not None and right is not None:
            width = right - left
            widths.append(width)
            h_edges.append((left, y))
            h_edges.append((right, y))
    return widths, h_edges

def _scan_caliper_vertical(crop: np.ndarray, n_lines: int, line_width: int, edge_threshold: int, polarity: str):
    w, h = crop.shape[1], crop.shape[0]
    xs = np.linspace(line_width, w - line_width - 1, n_lines, dtype=int)
    heights = []
    v_edges = []
    for x in xs:
        band = crop[:, x - line_width//2 : x + line_width//2 + 1]
        profile = band.mean(axis=1)
        top, bottom = _extract_edges_1d(profile, edge_threshold, polarity)
        if top is not None and bottom is not None:
            height = bottom - top
            heights.append(height)
            v_edges.append((x, top))
            v_edges.append((x, bottom))
    return heights, v_edges



def measure_circle(img: np.ndarray, roi_info: Dict[str, Any],
                   params: GeometryMeasureParams):
    """简单圆测量：在 ROI 内用 Canny+轮廓点拟合圆
    Returns {'ok': bool, 'center': (x,y), 'radius': r, 'edge_points': list[(x,y)], 'error': str}
    """
    try:
        cx, cy = roi_info['center']
        len_x = roi_info['len_x']
        len_y = roi_info['len_y']
        ang = roi_info.get('angle', 0)

        # 拉正 ROI
        crop, (off_x, off_y), M = _rotate_and_crop(img, (cx, cy), len_x, len_y, ang)
        if crop.size == 0:
            return {'ok': False, 'error': 'crop empty'}

        gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY) if crop.ndim == 3 else crop
        edges = cv2.Canny(gray, 50, 150)
        cnts, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        if not cnts:
            return {'ok': False, 'error': 'no contour'}
        pts = np.vstack(cnts).squeeze()
        if pts.ndim != 2 or pts.shape[0] < 5:
            return {'ok': False, 'error': 'too few points'}

        # 拟合外接圆 (OpenCV 返回 center(tuple), radius)
        (xc, yc), r = cv2.minEnclosingCircle(pts.astype(np.float32))

        # 残差评估
        dists = np.sqrt(((pts[:,0]-xc)**2 + (pts[:,1]-yc)**2))
        rmse = float(np.mean(np.abs(dists - r)))
        if rmse > 5:  # 简单阈值
            return {'ok': False, 'error': f'rmse too high {rmse:.1f}'}

        # 逆变换回原图
        M_inv = cv2.invertAffineTransform(M)
        center_orig = (
            M_inv[0,0]*(xc+off_x) + M_inv[0,1]*(yc+off_y) + M_inv[0,2],
            M_inv[1,0]*(xc+off_x) + M_inv[1,1]*(yc+off_y) + M_inv[1,2]
        )
        edge_points = [(
            M_inv[0,0]*(p[0]+off_x) + M_inv[0,1]*(p[1]+off_y) + M_inv[0,2],
            M_inv[1,0]*(p[0]+off_x) + M_inv[1,1]*(p[1]+off_y) + M_inv[1,2]
        ) for p in pts[::max(1,pts.shape[0]//200)]]  # 采样部分点减少数据量

        return {'ok': True, 'center': center_orig, 'radius': float(r), 'edge_points': edge_points}
    except Exception as e:
        import traceback; traceback.print_exc()
        return {'ok': False, 'error': str(e)}


def measure_arc(img: np.ndarray, roi_info: Dict[str, Any],
                params: GeometryMeasureParams):
    """圆弧测量：基于同圆拟合并计算覆盖角度"""
    res = measure_circle(img, roi_info, params)
    if not res.get('ok'):
        return res
    # 计算弧角度
    try:
        center = np.array(res['center'])
        pts = np.array(res['edge_points'])
        if pts.shape[0] < 3:
            return {**res, 'ok': False, 'error': 'not enough arc pts'}
        vecs = pts - center
        angles = np.degrees(np.arctan2(vecs[:,1], vecs[:,0]))
        ang_span = float((angles.max() - angles.min()) % 360.0)
        res['arc_angle'] = ang_span if ang_span>0 else 360.0
        return res
    except Exception as e:
        import traceback; traceback.print_exc()
        return {**res, 'ok': False, 'error': str(e)}


def measure_cross_caliper(img: np.ndarray, roi_info: Dict[str, Any],
                         params: GeometryMeasureParams, ui_params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    十字卡尺测量 - 扫描线卡尺算法（纯Python + NumPy 实现）

    Args:
        img: 灰度图像
        roi_info: ROI信息 {'center': (x,y), 'len_x': w, 'len_y': h, 'angle': deg}
        params: 测量参数

    Returns:
        {'ok': bool, 'width': float, 'height': float, 'edge_points': dict, 'error': str}
    """
    try:
        # 读取参数
        scan_lines = params.scan_lines
        line_width = params.line_width
        edge_th = params.edge_threshold
        polarity = params.polarity.upper()
        expand_ratio = params.roi_expand_ratio

        if ui_params:
            scan_lines = int(ui_params.get('scan_lines', scan_lines))
            line_width = int(ui_params.get('line_width', line_width))
            edge_th = float(ui_params.get('edge_threshold', edge_th))
            polarity = ui_params.get('polarity', polarity)
            expand_ratio = float(ui_params.get('roi_expand_ratio', expand_ratio))

        # ------ 解析 ROI ------
        cx, cy = roi_info['center']
        len_x, len_y = roi_info['len_x'] * expand_ratio, roi_info['len_y'] * expand_ratio
        angle_deg = roi_info['angle']
        print(f"🔍 使用原始ROI: 中心({cx:.0f}, {cy:.0f}), 尺寸{len_x:.1f}x{len_y:.1f}, 角度{angle_deg:.1f}°")

        # 灰度图
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img

        # ------ 旋转并裁剪 ROI ------
        crop, (off_x, off_y), M = _rotate_and_crop(gray, (cx, cy), len_x, len_y, angle_deg)
        print(f"🔍 旋转裁剪后 ROI 大小: {crop.shape[1]}x{crop.shape[0]}")

        # ------ 扫描卡尺 ------
        widths, h_edges_crop = _scan_caliper(crop, scan_lines, line_width, edge_th, polarity)
        heights, v_edges_crop = _scan_caliper_vertical(crop, scan_lines, line_width, edge_th, polarity)

        # ---------- 离群值过滤 ----------
        def _filter_pairs(values, points):
            if len(values) < 3:
                return values, points
            med = np.median(values)
            abs_dev = np.abs(values - med)
            mad = np.median(abs_dev) if np.median(abs_dev) > 1e-3 else 1e-3
            mask = abs_dev < 3 * mad  # Hampel filter approx.
            new_vals = values[mask]
            new_pts = [p for i, p in enumerate(points) if mask[i//2]]  # 每个宽度对应两个点
            return new_vals, new_pts

        widths_np = np.array(widths)
        heights_np = np.array(heights)
        widths_np, h_edges_crop = _filter_pairs(widths_np, h_edges_crop)
        heights_np, v_edges_crop = _filter_pairs(heights_np, v_edges_crop)

        widths = widths_np.tolist()
        heights = heights_np.tolist()

        if len(widths) == 0 or len(heights) == 0:
            return {'ok': False, 'error': '未找到足够边缘点'}

        width_px = float(np.median(widths))
        height_px = float(np.median(heights))
        print(f"✅ 卡尺测量成功: 宽度{width_px:.2f}, 高度{height_px:.2f}")

        # ------ 边缘点坐标逆变换回原图 ------
        M_inv = cv2.invertAffineTransform(M)
        def back_transform(pt):
            x_rot = pt[0] + off_x
            y_rot = pt[1] + off_y
            x_orig = M_inv[0,0]*x_rot + M_inv[0,1]*y_rot + M_inv[0,2]
            y_orig = M_inv[1,0]*x_rot + M_inv[1,1]*y_rot + M_inv[1,2]
            return (x_orig, y_orig)

        h_edges = [back_transform(p) for p in h_edges_crop]
        v_edges = [back_transform(p) for p in v_edges_crop]

        # ---------- 过滤落在 ROI 外的点 ----------
        def _inside_rot_rect(pt, center, w_len, h_len, ang_deg):
            ang_rad = np.radians(-ang_deg)
            cos_a, sin_a = np.cos(ang_rad), np.sin(ang_rad)
            x = pt[0] - center[0]
            y = pt[1] - center[1]
            xr = x * cos_a - y * sin_a
            yr = x * sin_a + y * cos_a
            return (abs(xr) <= w_len/2) and (abs(yr) <= h_len/2)

        h_edges = [p for p in h_edges if _inside_rot_rect(p, (cx, cy), len_x, len_y, angle_deg)]
        v_edges = [p for p in v_edges if _inside_rot_rect(p, (cx, cy), len_x, len_y, angle_deg)]

        # 可视化
        result_vis = _draw_cross_caliper_visualization(img, (cx, cy), len_x, len_y, angle_deg,
                                                       np.array([e[0] for e in h_edges+v_edges]),
                                                       np.array([e[1] for e in h_edges+v_edges]),
                                                       {'width': width_px, 'height': height_px,
                                                        'edge_points': {'horizontal': h_edges, 'vertical': v_edges}})

        return {
            'ok': True,
            'width': width_px,
            'height': height_px,
            'edge_points': {
                'horizontal': h_edges,
                'vertical': v_edges
            },
            'visualization': {
                'result_image': result_vis,
                'roi_center': (cx, cy),
                'roi_size': (len_x, len_y),
                'roi_angle': angle_deg
            }
        }
    except Exception as e:
        import traceback
        error_msg = f'十字卡尺测量异常: {str(e)}'
        traceback.print_exc()
        return {'ok': False, 'error': error_msg}


# ------------------------- 倒角圆弧（圆角）测量 -------------------------

def _map_pt_to_crop(pt_abs, M, off):
    """将绝对坐标 pt_abs 映射到 crop 内坐标"""
    x_r = M[0,0]*pt_abs[0] + M[0,1]*pt_abs[1] + M[0,2]
    y_r = M[1,0]*pt_abs[0] + M[1,1]*pt_abs[1] + M[1,2]
    return x_r - off[0], y_r - off[1]


def measure_fillet_radius(img: np.ndarray, roi_info: Dict[str, Any], params: GeometryMeasureParams,
                          corner_id: int | None = None):
    """测量矩形/条形零件 4 个圆角的半径；如给 corner_id(0-3) 则只测指定角。
    返回 {'ok', 'corners':[{'center':(x,y),'radius':r,'rmse':e},...], 'error'}
    """
    try:
        cx, cy = roi_info['center']
        len_x = roi_info['len_x']
        len_y = roi_info['len_y']
        ang = roi_info.get('angle', 0)

        # 扩大ROI以包含完整的圆角
        expand_ratio = params.roi_expand_ratio if params.roi_expand_ratio > 1.0 else 1.1  # 默认扩大10%
        expanded_len_x = len_x * expand_ratio
        expanded_len_y = len_y * expand_ratio

        if params.debug_log:
            print(f"[fillet] ROI扩张: 原始({len_x:.1f}x{len_y:.1f}) -> 扩张({expanded_len_x:.1f}x{expanded_len_y:.1f})")

        crop, (off_x, off_y), M = _rotate_and_crop(img, (cx, cy), expanded_len_x, expanded_len_y, ang)
        if crop.size == 0:
            return {'ok': False, 'error': 'crop empty'}
        gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY) if crop.ndim==3 else crop

        if params.debug_log:
            print(f"[fillet] 开始轮廓检测，图像尺寸: {gray.shape}")
            print(f"[fillet] 图像像素值范围: [{gray.min()}, {gray.max()}]")

        # 对于高对比度二值图像，直接使用轮廓检测
        # 避免Canny产生的双边缘问题
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

        if params.debug_log:
            print(f"[fillet] 二值化后像素值范围: [{binary.min()}, {binary.max()}]")

        # 轻微的形态学操作，填补小间隙
        kernel = np.ones((3,3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # 直接从二值图像检测轮廓，避免Canny的双边缘问题
        cnts, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        if not cnts:
            return {'ok': False, 'error': 'no contour'}

        # 选择最大的轮廓（应该是物体的外轮廓）
        cnt = max(cnts, key=cv2.contourArea).squeeze()
        if cnt.ndim != 2 or cnt.shape[0] < 10:
            return {'ok': False, 'error': 'bad contour'}
        pts_all = cnt.astype(np.float32)

        if params.debug_log:
            print(f"[fillet] 轮廓检测成功，轮廓点数: {len(pts_all)}")
            # 显示轮廓点的分布
            x_coords = pts_all[:, 0]
            y_coords = pts_all[:, 1]
            print(f"[fillet] 完整轮廓范围: X[{x_coords.min():.1f}, {x_coords.max():.1f}], Y[{y_coords.min():.1f}, {y_coords.max():.1f}]")

            # 检查轮廓点是否在边界上（调试用）
            sample_pts = pts_all[::max(1, len(pts_all)//10)]  # 采样10个点检查
            print(f"[fillet] 采样轮廓点检查:")
            for i, pt in enumerate(sample_pts):
                x, y = int(pt[0]), int(pt[1])
                if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                    pixel_val = gray[y, x]
                    print(f"[fillet]   点{i}: ({x},{y}) -> 像素值={pixel_val}")
                else:
                    print(f"[fillet]   点{i}: ({x},{y}) -> 超出图像范围")

        corners: list[Dict[str, Any]] = []
        predicted = roi_info.get('corners')
        # 若上级 ROI 未给出四角点，自动按矩形几何推算 4 个角顶点（逆时针）
        if (predicted is None or len(predicted)==0) and len_x>0 and len_y>0:
            # 计算旋转矩形四角坐标（与 roi_info._draw_measurement_result 相同逻辑）
            angle_rad = np.radians(ang)
            cos_a, sin_a = np.cos(angle_rad), np.sin(angle_rad)
            half_x, half_y = len_x/2.0, len_y/2.0
            predicted = []
            for dx,dy in [(-half_x,-half_y),(half_x,-half_y),(half_x,half_y),(-half_x,half_y)]:
                rx = dx*cos_a - dy*sin_a + cx
                ry = dx*sin_a + dy*cos_a + cy
                predicted.append((rx,ry))
        # -------------------------------- 第一阶段：利用形状匹配先验做局部拟合 --------------------------------
        if predicted is not None and len(predicted):
            if params.fillet_roi_radius and params.fillet_roi_radius > 0:
                win_r = params.fillet_roi_radius
            else:
                diag = float(np.hypot(len_x, len_y))
                win_r = diag * 0.35  # 自动窗口
            for idx, pt_abs in enumerate(predicted):
                if corner_id is not None and idx != corner_id:
                    continue
                x_c, y_c = _map_pt_to_crop(pt_abs, M, (off_x, off_y))
                # 在窗口内选点
                # 先按半径窗口筛选
                dists = np.hypot(pts_all[:,0]-x_c, pts_all[:,1]-y_c)
                local_pts = pts_all[dists < win_r]
                # 对于拟合-筛选-重拟合方法，不需要严格的象限筛选
                # 因为迭代筛选过程会自动去除不合适的点
                print(f"[fillet] 角点 {idx}: 半径窗口内轮廓点数 {local_pts.shape[0]}")
                if local_pts.shape[0] < 3:  # 降低最小点数要求
                    print(f"[fillet] 角点 {idx}: 轮廓点不足，跳过")
                    continue
                # ---------------- 拟合-筛选-重拟合方法 ----------------
                print(f"[fillet] 角点 {idx}: 使用拟合-筛选-重拟合方法")  # 强制输出调试

                # 使用轮廓点进行迭代拟合
                result = _fit_fillet_iterative(
                    corner_pt=(x_c, y_c),
                    edge_points=local_pts,
                    roi_radius=win_r,
                    debug_log=True  # 强制开启调试
                )

                if result is not None:
                    center_crop, radius, final_pts, rmse = result

                    if params.debug_log:
                        print(f"[fillet] 角点 {idx}: 拟合成功，圆心({center_crop[0]:.1f},{center_crop[1]:.1f}), 半径{radius:.1f}, RMSE{rmse:.3f}")

                    corners.append({
                        'center_crop': center_crop,
                        'radius_px': float(radius),
                        'rmse': float(rmse),
                        'pts': final_pts,
                        'roi_radius_px': win_r,
                        'roi_center': pt_abs
                    })
                else:
                    if params.debug_log:
                        print(f"[fillet] 角点 {idx}: 拟合失败")
        # 若依旧未拟合出圆角则失败
        if not corners:
            return {'ok': False, 'error': 'corner_fit_failed'}
        # 若只需一个角 (在无先验情况下 labels顺序不保证, 维持 idx 选择)
        if corner_id is not None and len(corners) > 1:
            corners = [corners[min(corner_id, len(corners)-1)]]
        # 回原图坐标
        M_inv = cv2.invertAffineTransform(M)
        for c in corners:
            xc, yc = c['center_crop']
            c['center'] = (
                M_inv[0,0]*(xc+off_x)+M_inv[0,1]*(yc+off_y)+M_inv[0,2],
                M_inv[1,0]*(xc+off_x)+M_inv[1,1]*(yc+off_y)+M_inv[1,2]
            )
            c['radius'] = c['radius_px']  # 已经是像素，若需 real-world 由上层转换

        # 保留真实测量值，不进行任何校正

        print(f'[fillet] success corners={len(corners)}')
        return {'ok': True, 'corners': corners}
    except Exception as e:
        import traceback; traceback.print_exc()
        return {'ok': False, 'error': str(e)}

def _report_radius_consistency(corners, debug_log=False):
    """报告圆角半径的一致性情况，但不修改真实测量值"""
    if len(corners) <= 1:
        return

    # 提取所有半径
    radii = [c['radius'] for c in corners]

    if debug_log:
        print(f"[fillet] 检测到的真实半径: {[f'{r:.2f}' for r in radii]}")

        # 计算统计信息
        mean_radius = np.mean(radii)
        median_radius = np.median(radii)
        std_radius = np.std(radii)
        min_radius = np.min(radii)
        max_radius = np.max(radii)

        print(f"[fillet] 半径统计: 平均{mean_radius:.2f}, 中位数{median_radius:.2f}, 标准差{std_radius:.2f}")
        print(f"[fillet] 半径范围: {min_radius:.2f} - {max_radius:.2f}, 差异{max_radius-min_radius:.2f}")

        # 评估一致性
        if std_radius < 2.0:
            print(f"[fillet] 一致性评估: 优秀 (标准差 < 2.0)")
        elif std_radius < 5.0:
            print(f"[fillet] 一致性评估: 良好 (标准差 < 5.0)")
        else:
            print(f"[fillet] 一致性评估: 需要注意 (标准差 >= 5.0)")


# ------------------------- 拟合-筛选-重拟合方法 -------------------------

def _fit_fillet_iterative(corner_pt, edge_points, roi_radius, debug_log=False):
    """使用改进的圆角拟合方法：曲率分析 + RANSAC拟合

    Args:
        corner_pt: 角点坐标 (x, y)
        edge_points: 候选边缘点
        roi_radius: ROI半径
        debug_log: 是否输出调试信息

    Returns:
        (center, radius, final_points, rmse) 或 None
    """
    if len(edge_points) < 5:
        if debug_log:
            print(f"[fillet] 边缘点不足 ({len(edge_points)} < 5)")
        return None

    corner_pt = np.array(corner_pt)

    if debug_log:
        print(f"[fillet] 开始改进拟合，初始点数: {len(edge_points)}")
        x_coords = edge_points[:, 0]
        y_coords = edge_points[:, 1]
        print(f"[fillet] 轮廓点范围: X[{x_coords.min():.1f}, {x_coords.max():.1f}], Y[{y_coords.min():.1f}, {y_coords.max():.1f}]")
        print(f"[fillet] 角点位置: ({corner_pt[0]:.1f}, {corner_pt[1]:.1f})")

    # 第一步：基于角点位置的渐进式筛选
    corner_distances = np.linalg.norm(edge_points - corner_pt, axis=1)

    # 渐进式搜索：从小到大逐步扩大搜索半径
    search_radii = [20, 30, 40, 50]  # 逐步扩大的搜索半径
    close_points = None
    local_radius = 0

    for radius in search_radii:
        close_mask = corner_distances <= radius
        candidate_points = edge_points[close_mask]

        if len(candidate_points) >= 15:  # 找到足够的点就停止
            close_points = candidate_points
            local_radius = radius
            break

    # 如果还是没找到足够的点，使用所有点
    if close_points is None or len(close_points) < 5:
        close_points = edge_points
        local_radius = roi_radius

    if debug_log:
        print(f"[fillet] 渐进式筛选: 半径{local_radius:.1f}, 保留{len(close_points)}/{len(edge_points)}个点")

    # 第二步：使用卡尺方法检测圆角
    best_result = _caliper_fillet_detection(edge_points, corner_pt, roi_radius, debug_log)

    if best_result is None:
        if debug_log:
            print(f"[fillet] RANSAC拟合失败，降级到最小二乘法")
        # 降级到原方法
        if len(close_points) < 3:
            if debug_log:
                print(f"[fillet] 点数不足，无法拟合")
            return None

        try:
            current_center, current_radius = _fit_circle_least_squares(close_points)

            # 检查降级结果的合理性
            if current_radius < 15 or current_radius > 50:
                if debug_log:
                    print(f"[fillet] 降级拟合半径不合理: {current_radius:.1f}")
                return None

            distances = np.linalg.norm(close_points - current_center, axis=1)
            residuals = np.abs(distances - current_radius)
            rmse = np.sqrt(np.mean(residuals**2))
            return (current_center[0], current_center[1]), current_radius, close_points, rmse
        except:
            if debug_log:
                print(f"[fillet] 降级拟合失败")
            return None

    return best_result


def _caliper_fillet_detection(edge_points, corner_pt, roi_radius, debug_log=False):
    """使用卡尺方法检测圆角半径 - 最可靠的方法"""
    if len(edge_points) < 5:
        return None

    corner_pt = np.array(corner_pt)

    if debug_log:
        print(f"[fillet] 卡尺检测: 角点({corner_pt[0]:.1f},{corner_pt[1]:.1f}), 候选点数{len(edge_points)}")

    # 第一步：在角点周围设置多条卡尺线
    caliper_results = []

    # 设置卡尺线的方向和长度 - 从角点向外辐射
    angles = np.linspace(0, 2*np.pi, 12)  # 12个方向，减少数量但提高质量
    caliper_length = min(roi_radius * 0.8, 50)  # 适中的卡尺长度

    for angle in angles:
        # 计算卡尺线的起点和终点 - 从角点向外
        dx = caliper_length * np.cos(angle)
        dy = caliper_length * np.sin(angle)

        start_pt = corner_pt  # 从角点开始
        end_pt = corner_pt + np.array([dx, dy])  # 向外延伸

        # 在这条卡尺线上寻找边缘点
        edge_pt = _find_edge_on_caliper_line(edge_points, start_pt, end_pt, debug_log)

        if edge_pt is not None:
            distance = np.linalg.norm(edge_pt - corner_pt)
            caliper_results.append({
                'edge_point': edge_pt,
                'distance': distance,
                'angle': angle
            })

    if len(caliper_results) < 3:  # 降低最小要求到3个点
        if debug_log:
            print(f"[fillet] 卡尺检测到的边缘点不足: {len(caliper_results)}")
        return None

    # 第二步：从卡尺检测到的边缘点拟合圆
    edge_points_caliper = np.array([r['edge_point'] for r in caliper_results])

    try:
        center, radius = _fit_circle_least_squares(edge_points_caliper)

        # 验证拟合结果 - 更严格的半径约束
        expected_radius = 35  # 基于实际产品的预期半径
        tolerance = 10  # ±10像素的容差

        if radius < expected_radius - tolerance or radius > expected_radius + tolerance:
            if debug_log:
                print(f"[fillet] 卡尺拟合半径偏离预期: {radius:.1f} (预期{expected_radius}±{tolerance})")
            return None

        # 计算拟合质量
        distances = np.linalg.norm(edge_points_caliper - center, axis=1)
        rmse = np.sqrt(np.mean((distances - radius)**2))

        if debug_log:
            print(f"[fillet] 卡尺检测成功: 圆心({center[0]:.1f},{center[1]:.1f}), 半径{radius:.1f}, RMSE{rmse:.3f}")

        return (center[0], center[1]), radius, edge_points_caliper, rmse

    except Exception as e:
        if debug_log:
            print(f"[fillet] 卡尺拟合失败: {e}")
        return None


def _find_edge_on_caliper_line(edge_points, start_pt, end_pt, debug_log=False):
    """在卡尺线上寻找最近的边缘点"""
    if len(edge_points) == 0:
        return None

    # 计算所有边缘点到卡尺线的距离
    line_vec = end_pt - start_pt
    line_length = np.linalg.norm(line_vec)

    if line_length < 1e-6:
        return None

    line_unit = line_vec / line_length

    # 找到距离卡尺线最近的边缘点
    min_distance = float('inf')
    closest_point = None

    for pt in edge_points:
        # 计算点到直线的距离
        pt_vec = pt - start_pt
        projection_length = np.dot(pt_vec, line_unit)

        # 检查投影是否在线段范围内（稍微放宽范围）
        if -line_length*0.2 <= projection_length <= line_length*1.2:
            projection_pt = start_pt + projection_length * line_unit
            distance = np.linalg.norm(pt - projection_pt)

            if distance < min_distance and distance < 8:  # 放宽距离阈值到8像素
                min_distance = distance
                closest_point = pt

    return closest_point


def _constrained_circle_fit(points, corner_pt, expected_radius, debug_log=False):
    """基于圆弧段的圆拟合 - 从圆弧段推断完整圆"""
    if len(points) < 5:
        return None

    corner_pt = np.array(corner_pt)

    if debug_log:
        print(f"[fillet] 圆弧拟合: 预期半径{expected_radius}, 轮廓点数{len(points)}")

    # 第一步：识别真正的圆弧段
    arc_points = _identify_arc_segment(points, corner_pt, debug_log)

    if len(arc_points) < 5:
        if debug_log:
            print(f"[fillet] 圆弧段点数不足: {len(arc_points)}")
        return None

    # 第二步：从圆弧段拟合完整圆
    try:
        center, radius = _fit_circle_least_squares(arc_points)

        # 验证拟合结果的合理性
        if radius < 20 or radius > 60:  # 圆角半径应该在合理范围内
            if debug_log:
                print(f"[fillet] 拟合半径不合理: {radius:.1f}")
            return None

        # 计算拟合质量
        distances = np.linalg.norm(arc_points - center, axis=1)
        rmse = np.sqrt(np.mean((distances - radius)**2))

        if debug_log:
            print(f"[fillet] 圆弧拟合成功: 圆心({center[0]:.1f},{center[1]:.1f}), 半径{radius:.1f}, RMSE{rmse:.3f}")

        return (center[0], center[1]), radius, arc_points, rmse

    except Exception as e:
        if debug_log:
            print(f"[fillet] 圆弧拟合失败: {e}")
        return None


def _identify_arc_segment(points, corner_pt, debug_log=False):
    """识别真正的圆弧段 - 基于连续性和一致曲率"""
    if len(points) < 7:
        return points

    corner_pt = np.array(corner_pt)

    # 第一步：找到距离角点最近的点作为起始点
    distances_to_corner = np.linalg.norm(points - corner_pt, axis=1)
    closest_idx = np.argmin(distances_to_corner)

    if debug_log:
        print(f"[fillet] 最近点索引: {closest_idx}, 距离: {distances_to_corner[closest_idx]:.1f}")

    # 第二步：从最近点开始，向两个方向扩展，寻找连续的圆弧段
    arc_indices = [closest_idx]

    # 向前扩展
    for i in range(closest_idx + 1, min(closest_idx + 20, len(points))):
        if _is_arc_point(points, i, arc_indices):
            arc_indices.append(i)
        else:
            break

    # 向后扩展
    for i in range(closest_idx - 1, max(closest_idx - 20, -1), -1):
        if _is_arc_point(points, i, arc_indices):
            arc_indices.insert(0, i)
        else:
            break

    # 第三步：提取圆弧段点
    arc_indices.sort()
    arc_points = points[arc_indices]

    if debug_log:
        print(f"[fillet] 连续圆弧段: 索引范围[{min(arc_indices)}, {max(arc_indices)}], 点数{len(arc_points)}")

    return arc_points


def _is_arc_point(points, idx, existing_indices):
    """判断一个点是否属于圆弧段"""
    if len(existing_indices) < 2:
        return True

    # 检查距离连续性：新点与已有点的距离应该合理
    existing_points = points[existing_indices]
    new_point = points[idx]

    # 计算到已有点的最小距离
    min_distance = np.min(np.linalg.norm(existing_points - new_point, axis=1))

    # 距离应该在合理范围内（不能太远，表示不连续）
    if min_distance > 10:  # 超过10像素认为不连续
        return False

    # 检查曲率一致性：如果有足够的点，检查曲率
    if len(existing_indices) >= 3:
        # 简单检查：新点与相邻点形成的角度应该合理
        try:
            # 取最近的两个已有点
            distances = np.linalg.norm(existing_points - new_point, axis=1)
            nearest_indices = np.argsort(distances)[:2]
            p1 = existing_points[nearest_indices[0]]
            p2 = existing_points[nearest_indices[1]]
            p3 = new_point

            # 计算角度
            v1 = p1 - p2
            v2 = p3 - p2

            if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                cos_angle = np.clip(cos_angle, -1, 1)
                angle = np.arccos(cos_angle)

                # 角度应该表明这是一个弯曲的点（不是直线）
                if angle < np.pi / 6:  # 小于30度认为太直
                    return False
        except:
            pass

    return True


def _filter_by_curvature_simple(points, debug_log=False):
    """简单的曲率筛选，保留弯曲的点"""
    if len(points) < 7:
        return points

    # 计算每个点的局部曲率
    curvatures = []
    window = 2  # 使用较小的窗口

    for i in range(len(points)):
        if i < window or i >= len(points) - window:
            curvatures.append(0)
            continue

        # 计算三点角度
        p1 = points[i - window]
        p2 = points[i]
        p3 = points[i + window]

        v1 = p1 - p2
        v2 = p3 - p2

        # 计算角度变化
        try:
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1, 1)
            angle = np.arccos(cos_angle)
            curvatures.append(angle)
        except:
            curvatures.append(0)

    curvatures = np.array(curvatures)

    # 筛选高曲率的点
    if len(curvatures) > 0 and np.max(curvatures) > 0:
        threshold = np.percentile(curvatures, 50)  # 取中位数作为阈值
        arc_mask = curvatures >= threshold
        arc_points = points[arc_mask]

        # 确保至少保留一些点
        if len(arc_points) < len(points) * 0.3:
            threshold = np.percentile(curvatures, 30)
            arc_mask = curvatures >= threshold
            arc_points = points[arc_mask]
    else:
        arc_points = points

    return arc_points if len(arc_points) >= 5 else points


def _filter_arc_points_by_curvature(points, debug_log=False):
    """通过曲率分析筛选出圆弧段的点，去除直线段"""
    if len(points) < 7:  # 需要足够的点来计算曲率
        return points

    # 计算每个点的曲率（使用相邻点的角度变化）
    curvatures = []
    window = 3  # 使用前后3个点计算曲率

    for i in range(len(points)):
        if i < window or i >= len(points) - window:
            curvatures.append(0)  # 边界点设为0
            continue

        # 取前后window个点
        p1 = points[i - window]
        p2 = points[i]
        p3 = points[i + window]

        # 计算两个向量
        v1 = p1 - p2
        v2 = p3 - p2

        # 计算角度变化（曲率的近似）
        try:
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
            angle = np.arccos(cos_angle)
            curvature = angle / np.linalg.norm(p3 - p1)  # 归一化曲率
            curvatures.append(curvature)
        except:
            curvatures.append(0)

    curvatures = np.array(curvatures)

    # 筛选高曲率的点（圆弧段）- 使用更保守的阈值
    curvature_threshold = np.percentile(curvatures, 75)  # 取75%分位数作为阈值
    arc_mask = curvatures > curvature_threshold

    # 确保至少保留一些点
    if np.sum(arc_mask) < len(points) * 0.2:
        # 如果筛选太严格，降低阈值
        curvature_threshold = np.percentile(curvatures, 50)
        arc_mask = curvatures > curvature_threshold

    # 如果还是太少，进一步降低
    if np.sum(arc_mask) < len(points) * 0.1:
        curvature_threshold = np.percentile(curvatures, 30)
        arc_mask = curvatures > curvature_threshold

    arc_points = points[arc_mask]

    if debug_log:
        print(f"[fillet] 曲率筛选: 阈值{curvature_threshold:.4f}, 保留{len(arc_points)}/{len(points)}个点")

    return arc_points if len(arc_points) >= 5 else points


def _ransac_circle_fit(points, max_radius, debug_log=False, max_iterations=100):
    """使用RANSAC算法拟合圆，抗直线段干扰"""
    if len(points) < 5:
        return None

    best_inliers = []
    best_center = None
    best_radius = 0
    best_score = 0

    # RANSAC参数 - 针对圆角的严格设置
    min_inliers = max(8, min(20, len(points) * 0.3))  # 至少30%的点支持，确保质量
    distance_threshold = 2.0  # 内点距离阈值，保持严格

    for iteration in range(max_iterations):
        # 随机选择3个点拟合圆
        sample_indices = np.random.choice(len(points), 3, replace=False)
        sample_points = points[sample_indices]

        # 拟合圆
        circle_result = _fit_circle_3pts(sample_points[0], sample_points[1], sample_points[2])
        if circle_result is None:
            continue

        cx, cy, r = circle_result

        # 检查半径合理性 - 针对圆角的严格约束
        if r < 15 or r > 50:  # 圆角半径应该在15-50像素范围内
            continue

        # 计算所有点到圆的距离
        center = np.array([cx, cy])
        distances = np.linalg.norm(points - center, axis=1)
        residuals = np.abs(distances - r)

        # 找到内点
        inliers_mask = residuals < distance_threshold
        inliers = points[inliers_mask]

        # 评估这个模型
        if len(inliers) >= min_inliers:
            # 使用内点重新拟合圆
            try:
                refined_center, refined_radius = _fit_circle_least_squares(inliers)
                refined_distances = np.linalg.norm(inliers - refined_center, axis=1)
                refined_rmse = np.sqrt(np.mean((refined_distances - refined_radius)**2))

                # 评分：内点数量 + 拟合质量
                score = len(inliers) - refined_rmse * 10

                if score > best_score:
                    best_score = score
                    best_inliers = inliers
                    best_center = refined_center
                    best_radius = refined_radius
            except:
                continue

    if len(best_inliers) < min_inliers:
        if debug_log:
            print(f"[fillet] RANSAC失败: 最佳内点数{len(best_inliers)} < 最小要求{min_inliers}")
        return None

    # 计算最终RMSE
    final_distances = np.linalg.norm(best_inliers - best_center, axis=1)
    final_rmse = np.sqrt(np.mean((final_distances - best_radius)**2))

    if debug_log:
        print(f"[fillet] RANSAC成功: 内点{len(best_inliers)}, 半径{best_radius:.1f}, RMSE{final_rmse:.3f}")

    return (best_center[0], best_center[1]), best_radius, best_inliers, final_rmse


def _fit_circle_least_squares(points):
    """使用最小二乘法拟合圆"""
    # 转换为齐次坐标进行拟合
    x = points[:, 0]
    y = points[:, 1]

    # 构建矩阵 A
    A = np.column_stack([x, y, np.ones(len(points))])
    b = x**2 + y**2

    # 求解 Ax = b
    try:
        coeffs = np.linalg.lstsq(A, b, rcond=None)[0]
        cx = coeffs[0] / 2
        cy = coeffs[1] / 2
        r = np.sqrt(coeffs[2] + cx**2 + cy**2)
        return np.array([cx, cy]), r
    except:
        # 降级到最小外接圆
        (cx, cy), r = cv2.minEnclosingCircle(points)
        return np.array([cx, cy]), r

# ------------------------- 圆弧测量 -------------------------

def _fit_circle_3pts(p1, p2, p3):
    """返回 (xc,yc,r) 或 None"""
    A = np.array([p1, p2, p3], dtype=float)
    x1,y1 = A[0]; x2,y2 = A[1]; x3,y3 = A[2]
    temp = x2**2 + y2**2
    bc = (x1**2 + y1**2 - temp) / 2.0
    cd = (temp - x3**2 - y3**2) / 2.0
    det = (x1 - x2)*(y2 - y3) - (x2 - x3)*(y1 - y2)
    if abs(det) < 1e-6:
        return None
    # center
    cx = (bc*(y2 - y3) - cd*(y1 - y2)) / det
    cy = ((x1 - x2)*cd - (x2 - x3)*bc) / det
    r = np.sqrt((cx - x1)**2 + (cy - y1)**2)
    return cx, cy, r

def _ransac_circles(pts: np.ndarray, max_circles:int=2, residual_th:float=2.0,
                    min_inliers:int=30, max_trials:int=300):
    """简易RANSAC拟多圆, 返回 list[{'center':(x,y),'radius':r,'inliers':mask}]"""
    remaining = pts.copy()
    remaining_idx = np.arange(pts.shape[0])
    circles = []
    rng = np.random.default_rng()
    while remaining.shape[0] >= min_inliers and len(circles) < max_circles:
        best_model = None
        best_inliers = None
        for _ in range(max_trials):
            samp = remaining[rng.choice(remaining.shape[0], 3, replace=False)]
            model = _fit_circle_3pts(*samp)
            if model is None:
                continue
            cx, cy, r = model
            dists = np.sqrt(((remaining[:,0]-cx)**2 + (remaining[:,1]-cy)**2))
            inlier_mask = np.abs(dists - r) < residual_th
            if inlier_mask.sum() >= min_inliers and (best_inliers is None or inlier_mask.sum() > best_inliers.sum()):
                best_model = (cx, cy, r)
                best_inliers = inlier_mask
        if best_model is None:
            break
        cx, cy, r = best_model
        mask_full = np.zeros(pts.shape[0], dtype=bool)
        mask_full[remaining_idx[best_inliers]] = True
        circles.append({'center_crop':(cx, cy), 'radius_px':float(r), 'inlier_mask':mask_full})
        remaining_idx = remaining_idx[~best_inliers]
        remaining = remaining[~best_inliers]
    return circles

def measure_arc(img: np.ndarray, roi_info: Dict[str, Any], params: GeometryMeasureParams, options: Dict[str, Any] | None = None):
    """测量圆弧
    """
    try:
        cx, cy = roi_info['center']
        len_x = roi_info['len_x']
        len_y = roi_info['len_y']
        ang = roi_info.get('angle', 0)
        crop, (off_x, off_y), M = _rotate_and_crop(img, (cx, cy), len_x, len_y, ang)
        if crop.size == 0:
            return {'ok': False, 'error': 'crop empty'}
        gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY) if crop.ndim==3 else crop
        # 二值分割
        _, bin_img = cv2.threshold(gray, 0, 255, cv2.THRESH_OTSU+cv2.THRESH_BINARY)
        cnts, _ = cv2.findContours(bin_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        if not cnts:
            return {'ok': False, 'error': 'no contour'}
        cnt = max(cnts, key=cv2.contourArea).squeeze()
        if cnt.ndim!=2:
            return {'ok': False, 'error': 'bad contour'}
        # 直接使用全部轮廓点（采样减少点数）进行 RANSAC
        step = max(1, cnt.shape[0] // 1500)
        pts_curv = cnt[::step].astype(np.float32)
        # ---- RANSAC 多圆 ----
        arcs = []
        min_roi_len = min(len_x, len_y)
        circles = _ransac_circles(pts_curv, max_circles=4, residual_th=3.0, min_inliers=10)
        if not circles:
            circles = _ransac_circles(pts_curv, max_circles=4, residual_th=4.0, min_inliers=8)
        for c in circles:
            xc, yc = c['center_crop']
            r = c['radius_px']
            inliers = pts_curv[c['inlier_mask']]
            if inliers.shape[0] < 10:
                continue
            vecs = inliers - np.array([xc, yc])
            angs = np.degrees(np.arctan2(vecs[:,1], vecs[:,0]))
            cover = (angs.max() - angs.min()) % 360.0
            if cover < 30:
                continue
            rmse = float(np.mean(np.abs(np.sqrt(((inliers[:,0]-xc)**2 + (inliers[:,1]-yc)**2)) - r)))
            arcs.append({'center_crop':(xc, yc), 'radius_px':float(r), 'rmse':rmse,
                         'cover_deg': cover, 'pts': inliers})
        if not arcs:
            return {'ok': False, 'error': 'no arc candidate'}

        # 回原图坐标
        M_inv = cv2.invertAffineTransform(M)
        for a in arcs:
            xc, yc = a['center_crop']
            a['center'] = (
                M_inv[0,0]*(xc+off_x)+M_inv[0,1]*(yc+off_y)+M_inv[0,2],
                M_inv[1,0]*(xc+off_x)+M_inv[1,1]*(yc+off_y)+M_inv[1,2]
            )
            a['radius'] = a['radius_px']  # 已经是像素，若需 real-world 由上层转换
        # --- 可视化 ---
        # 若原图是灰度/二值，转换为 BGR 以正确显示彩色绘制
        vis = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR) if img.ndim == 2 or (img.ndim==3 and img.shape[2]==1) else img.copy()
        for a in arcs:
            cx_o, cy_o = map(int, a['center'])
            r_px = int(round(a['radius']))
            cv2.circle(vis, (cx_o, cy_o), r_px, (0,255,0), 2)
            cv2.circle(vis, (cx_o, cy_o), 3, (0,0,255), -1)
        result_dict = {'all_arcs': arcs}
        # 端选择
        if options:
            sel = None
            end = options.get('end') or options.get('side')
            idx = options.get('index')
            if idx is not None and 0 <= idx < len(arcs):
                sel = arcs[idx]
            elif end in ('head', 'tail') and len(arcs)>=2:
                # 计算 ROI 主轴方向向量（长边方向）
                ang_rad = np.radians(ang)
                major_vec = np.array([np.cos(ang_rad), np.sin(ang_rad)])
                for arc_obj in arcs:
                    v = np.array(arc_obj['center']) - np.array([cx, cy])
                    proj = v.dot(major_vec)
                    if (end=='tail' and proj>0) or (end=='head' and proj<0):
                        sel = arc_obj; break
            if sel:
                result_dict['arc'] = sel
                # 可视化仅高亮选中
                vis_sel = vis.copy()
                cv2.circle(vis_sel, (int(sel['center'][0]), int(sel['center'][1])), int(round(sel['radius'])), (0,255,255), 2)
                result_dict['visualization'] = {'result_image': vis_sel}
            else:
                result_dict['visualization'] = {'result_image': vis}
        else:
            result_dict['visualization'] = {'result_image': vis}
        result_dict['ok'] = True
        return result_dict
    except Exception as e:
        import traceback; traceback.print_exc()
        return {'ok': False, 'error': str(e)}

# ------------------------- 任务调度 -------------------------

def run_tasks(img: np.ndarray, template_img: np.ndarray, tasks: list[MeasureTask],
              matcher_fn=None, global_params: GeometryMeasureParams | None = None):
    """执行任务队列，先模板匹配得到 ROI，再逐任务测量
    matcher_fn: 可注入形状/模板匹配函数，默认使用 cv2.matchTemplate (需提供 center,len_x,len_y,angle)
    Returns {'ok': bool, 'roi_info':..., 'tasks': dict}
    """
    if matcher_fn is None:
        def _default_matcher(img, tpl):
            gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if img.ndim==3 else img
            res = cv2.matchTemplate(gray_img, tpl, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(res)
            h, w = tpl.shape[:2]
            return {'ok': max_val>0.5, 'center': (max_loc[0]+w//2, max_loc[1]+h//2),
                    'len_x': w, 'len_y': h, 'angle': 0, 'score': max_val}
        matcher_fn = _default_matcher
    roi_info = matcher_fn(img, template_img)
    if not roi_info.get('ok'):
        return {'ok': False, 'error': 'template_match_failed', 'score': roi_info.get('score',0)}

    results: Dict[str, Any] = {}
    for idx, task in enumerate(tasks):
        p = global_params or GeometryMeasureParams()
        if task.params:
            p = GeometryMeasureParams(**{**p.__dict__, **task.params})
        if task.type == 'cross_caliper':
            res = measure_cross_caliper(img, roi_info, p)
        elif task.type == 'circle':
            res = measure_circle(img, roi_info, p)
        elif task.type == 'arc':
            res = measure_arc(img, roi_info, p, task.params)
        elif task.type == 'fillet_radius':
            cid = task.params.get('corner_id') if task.params else None
            res = measure_fillet_radius(img, roi_info, p, corner_id=cid)
        else:
            res = {'ok': False, 'error': f'unknown task {task.type}'}
        results[f'{idx}_{task.type}'] = res
    return {'ok': True, 'roi_info': roi_info, 'tasks': results}

# ------------------------- 可视化绘制函数 -------------------------

def _draw_cross_caliper_visualization(img, roi_center, len_x, len_y, angle_deg, xs, ys, result_info=None):
    """绘制十字卡尺测量结果，可用于调试/展示

    Args:
        img: 原始图像（BGR 或灰度）
        roi_center: (cx, cy)
        len_x: ROI 长边
        len_y: ROI 短边
        angle_deg: 旋转角度
        xs: 边缘点 x 坐标数组
        ys: 边缘点 y 坐标数组
        result_info: 包含 width/height 等信息的字典，可为空

    Returns:
        vis_img: 绘制后的 BGR 图像
    """
    import cv2
    import numpy as np

    # 转成彩色图
    if img.ndim == 2 or img.shape[2] == 1:
        vis = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
    else:
        vis = img.copy()

    cx, cy = roi_center

    # 绘制旋转矩形 ROI
    box = cv2.boxPoints(((float(cx), float(cy)), (float(len_x), float(len_y)), float(angle_deg)))
    box = np.int32(box)
    cv2.polylines(vis, [box], isClosed=True, color=(0, 255, 0), thickness=2)

    # 绘制边缘点
    for x, y in zip(xs, ys):
        cv2.circle(vis, (int(round(x)), int(round(y))), 3, (0, 0, 255), -1)

    # 绘制测量结果文本
    if result_info and result_info.get('width') and result_info.get('height'):
        text = f"W:{result_info['width']:.1f} H:{result_info['height']:.1f}"
        cv2.putText(vis, text, (int(cx) + 10, int(cy) - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

    return vis


# ------------------------- 角度测量 -------------------------

def measure_angle(img: np.ndarray, roi_info: Dict[str, Any], params: GeometryMeasureParams, options: Dict[str, Any] = None):
    """测量角度

    支持多种角度测量方法：
    1. 使用拟合框角度（最简单）
    2. 基于边缘直线拟合（推荐，精度更高）
    3. 基于轮廓主方向分析

    Args:
        img: 输入图像
        roi_info: ROI信息，包含center, len_x, len_y, angle等
        params: 测量参数
        options: 可选参数，包含angle_method等

    Returns:
        dict: 测量结果
    """
    try:
        if options is None:
            options = {}

        angle_method = options.get('angle_method', 'shape_match')  # 默认使用形状匹配角度

        if params.debug_log:
            print(f"[angle] 角度测量方法: {angle_method}")

        if angle_method == 'shape_match':
            # 方法1：直接使用形状匹配的角度
            result = _measure_angle_from_shape_match(roi_info, params.debug_log)
        elif angle_method == 'edge_fitting':
            # 方法2：基于边缘直线拟合
            result = _measure_angle_from_edge_fitting(img, roi_info, params)
        elif angle_method == 'contour_analysis':
            # 方法3：基于轮廓主方向分析
            result = _measure_angle_from_contour_analysis(img, roi_info, params)
        else:
            return {'ok': False, 'error': f'不支持的角度测量方法: {angle_method}'}

        if result['ok'] and params.debug_log:
            print(f"[angle] 测量成功: 角度={result['angle']:.2f}°")

        return result

    except Exception as e:
        import traceback
        traceback.print_exc()
        return {'ok': False, 'error': str(e)}


def _measure_angle_from_shape_match(roi_info: Dict[str, Any], debug_log: bool = False):
    """方法1：基于角点计算角度（最简单直接的方法）"""
    try:
        # 获取角点坐标
        corners = roi_info.get('corners')
        if not corners or len(corners) < 4:
            # 如果没有角点，使用形状匹配的角度
            angle = roi_info.get('angle', 0.0)
            angle = angle % 360

            if debug_log:
                print(f"[angle] 使用形状匹配角度: {angle:.2f}°")

            return {
                'ok': True,
                'angle': angle,
                'method': 'shape_match',
                'precision': 'medium',
                'roi_info': roi_info
            }

        # 基于角点计算角度 - 为每个角点分配ID和计算角度
        corner_details = []

        # 计算每个角的角度
        for i in range(4):
            # 当前角点作为顶点，前一个和后一个角点作为边的端点
            p_prev = np.array(corners[(i - 1) % 4])  # 前一个角点
            p_curr = np.array(corners[i])            # 当前角点（顶点）
            p_next = np.array(corners[(i + 1) % 4])  # 下一个角点

            # 计算两个向量
            v1 = p_prev - p_curr  # 从当前点指向前一个点
            v2 = p_next - p_curr  # 从当前点指向下一个点

            # 计算角度
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1, 1)
            angle_rad = np.arccos(cos_angle)
            angle_deg = np.degrees(angle_rad)

            # 存储角点详细信息
            corner_detail = {
                'id': i + 1,  # 角点ID（1-4）
                'position': corners[i],  # 角点坐标
                'angle': angle_deg,      # 角点角度
                'x': corners[i][0],      # X坐标
                'y': corners[i][1]       # Y坐标
            }
            corner_details.append(corner_detail)

        # 提取角度列表（保持向后兼容）
        corner_angles = [detail['angle'] for detail in corner_details]

        # 计算主要边的方向角
        # 使用第一条边的方向作为主角度
        edge_vector = np.array(corners[1]) - np.array(corners[0])
        main_angle = np.degrees(np.arctan2(edge_vector[1], edge_vector[0]))

        # 标准化到0-360度
        if main_angle < 0:
            main_angle += 360

        if debug_log:
            print(f"[angle] 角点详细信息:")
            for detail in corner_details:
                print(f"[angle]   角点{detail['id']}: 位置({detail['x']:.1f},{detail['y']:.1f}), 角度{detail['angle']:.1f}°")
            print(f"[angle] 主边方向角: {main_angle:.2f}°")

        return {
            'ok': True,
            'angle': main_angle,           # 主边方向角
            'corner_angles': corner_angles, # 角度列表（向后兼容）
            'corner_details': corner_details, # 详细的角点信息
            'method': 'corner_based',
            'precision': 'high',  # 基于角点的精度很高
            'roi_info': roi_info
        }

    except Exception as e:
        return {'ok': False, 'error': f'角点角度计算失败: {e}'}


def _measure_angle_from_edge_fitting(img: np.ndarray, roi_info: Dict[str, Any], params: GeometryMeasureParams):
    """方法2：基于边缘直线拟合测量角度（推荐方法）"""
    try:
        if params.debug_log:
            print(f"[angle] 开始边缘直线拟合角度测量")

        # 获取ROI区域
        cx, cy = roi_info['center']
        len_x = roi_info['len_x']
        len_y = roi_info['len_y']
        angle_deg = roi_info.get('angle', 0)

        # 旋转和裁剪图像
        crop, (off_x, off_y), M = _rotate_and_crop(img, (cx, cy), len_x, len_y, angle_deg)

        if params.debug_log:
            print(f"[angle] 裁剪图像尺寸: {crop.shape}")

        # 边缘检测
        edges = cv2.Canny(crop, 50, 150)

        # 霍夫直线检测
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=30, maxLineGap=10)

        if lines is None or len(lines) == 0:
            return {'ok': False, 'error': '未检测到足够的直线'}

        # 计算所有直线的角度
        line_angles = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi
            # 标准化角度到0-180度
            if angle < 0:
                angle += 180
            line_angles.append(angle)

        if params.debug_log:
            print(f"[angle] 检测到{len(line_angles)}条直线，角度范围: {min(line_angles):.1f}° - {max(line_angles):.1f}°")

        # 使用主要角度（众数或中位数）
        main_angle = np.median(line_angles)

        # 加上原始旋转角度
        final_angle = (main_angle + angle_deg) % 360

        return {
            'ok': True,
            'angle': final_angle,
            'method': 'edge_fitting',
            'precision': 'high',  # 精度高
            'line_count': len(line_angles),
            'line_angles': line_angles,
            'roi_info': roi_info
        }

    except Exception as e:
        return {'ok': False, 'error': f'边缘拟合角度测量失败: {e}'}


def _measure_angle_from_contour_analysis(img: np.ndarray, roi_info: Dict[str, Any], params: GeometryMeasureParams):
    """方法3：基于轮廓主方向分析测量角度"""
    try:
        if params.debug_log:
            print(f"[angle] 开始轮廓主方向分析")

        # 获取ROI区域
        cx, cy = roi_info['center']
        len_x = roi_info['len_x']
        len_y = roi_info['len_y']
        angle_deg = roi_info.get('angle', 0)

        # 旋转和裁剪图像
        crop, (off_x, off_y), M = _rotate_and_crop(img, (cx, cy), len_x, len_y, angle_deg)

        # 二值化
        _, binary = cv2.threshold(crop, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return {'ok': False, 'error': '未找到轮廓'}

        # 选择最大轮廓
        main_contour = max(contours, key=cv2.contourArea)

        # 计算轮廓的最小外接矩形
        rect = cv2.minAreaRect(main_contour)
        angle = rect[2]

        # 标准化角度
        if angle < -45:
            angle += 90

        # 加上原始旋转角度
        final_angle = (angle + angle_deg) % 360

        if params.debug_log:
            print(f"[angle] 轮廓主方向角度: {angle:.2f}°, 最终角度: {final_angle:.2f}°")

        return {
            'ok': True,
            'angle': final_angle,
            'method': 'contour_analysis',
            'precision': 'medium',  # 精度中等
            'contour_area': cv2.contourArea(main_contour),
            'roi_info': roi_info
        }

    except Exception as e:
        return {'ok': False, 'error': f'轮廓分析角度测量失败: {e}'}
