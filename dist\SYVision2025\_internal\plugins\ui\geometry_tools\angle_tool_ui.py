"""交互式角度测量 UI。

操作流程：
1. 点击三点：依次是顶点(P0)→第1射线端点(P1)→第2射线端点(P2)
2. 系统立即计算夹角，显示在左侧列表，可多角测量。
3. 支持缩放、拖拽、结果保存/加载，与 ArcToolUI 风格保持一致。
"""
from __future__ import annotations
import pathlib, sys, math
from typing import Optional, Tuple, Any, Dict
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
import yaml
import numpy as np
import cv2
from PIL import Image, ImageTk

try:
    from plugins.geometry_tools.angle_tool import measure_angle_pts, measure_angle_fitted_lines
except ImportError:
    _root = pathlib.Path(__file__).resolve().parents[3]
    if str(_root) not in sys.path:
        sys.path.insert(0, str(_root))
    from plugins.geometry_tools.angle_tool import measure_angle_pts, measure_angle_fitted_lines

PREVIEW_SIZE = 550
CFG_DIR = Path(__file__).resolve().parents[3] / 'configs' / 'angle_measure'
CFG_DIR.mkdir(parents=True, exist_ok=True)

class AngleToolUI(tk.Frame):
    # 三点测量模式状态
    _ST_WAIT_P0 = 0  # 需点击顶点
    _ST_WAIT_P1 = 1  # 需点击第1点
    _ST_WAIT_P2 = 2  # 需点击第2点 完成测量

    # 拟合测量模式状态
    _ST_WAIT_LINE1 = 10  # 等待第一条直线的点
    _ST_WAIT_LINE2 = 11  # 等待第二条直线的点

    # ROI拟合模式状态
    _ST_WAIT_ROI = 20   # 等待拖拽ROI
    _ST_ROI_SET = 21    # ROI已设置，等待拟合

    # 测量模式
    _MODE_THREE_POINTS = 0  # 三点测量模式
    _MODE_FITTED_LINES = 1  # 拟合直线测量模式
    _MODE_ROI_FITTING = 2   # ROI拟合模式

    def __init__(self, master: Optional[tk.Widget] = None, img: Optional[np.ndarray] = None):
        super().__init__(master)
        self.master = master

        # 如果master是根窗口，设置关闭协议
        if isinstance(master, tk.Tk):
            master.protocol('WM_DELETE_WINDOW', self._on_close)

        # 图像数据
        self.img_orig: Optional[np.ndarray] = None  # 原图 BGR
        self.img_display: Optional[np.ndarray] = None  # 显示用 RGB

        # 交互
        self.measure_mode = self._MODE_THREE_POINTS  # 当前测量模式
        self.state = self._ST_WAIT_P0
        self.tmp_pts: list[Tuple[float, float]] = []  # 当前正在输入的 3 pts

        # 拟合模式相关
        self.line1_pts: list[Tuple[float, float]] = []  # 第一条直线的点
        self.line2_pts: list[Tuple[float, float]] = []  # 第二条直线的点
        self.ransac_thresh = 2.0  # RANSAC阈值

        # ROI拟合模式相关
        self.roi_rect: Optional[Tuple[float, float, float, float]] = None  # (x0, y0, x1, y1)
        self._dragging_roi = False
        self._roi_start: Optional[Tuple[float, float]] = None
        self.canny_th1 = 50
        self.canny_th2 = 150

        # ROI 列表
        self.rois: list[dict[str, Any]] = []
        self.current_idx: Optional[int] = None
        self.next_id = 1

        # 视图控制
        self._zoom = 1.0
        self._offset = np.array([0.0, 0.0])  # canvas 坐标 = img*zoom + offset
        self._pan_start: Optional[Tuple[int, int]] = None

        self._build_ui()
        if img is not None:
            self.load_image(img)

        # 填充整个父窗口
        self.pack(fill=tk.BOTH, expand=True)

    # ------------------ UI ------------------
    def _build_ui(self):
        # 配置主框架
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # 左侧控制面板
        p_frame = ttk.Frame(self, padding=6)
        p_frame.grid(row=0, column=0, sticky='ns')

        # 按钮区域
        ttk.Button(p_frame, text='打开图像', command=self._open_image).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='清除标记', command=self._clear_marks).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='保存配置', command=self._save_yaml).pack(fill=tk.X, pady=2)
        ttk.Button(p_frame, text='载入配置', command=self._load_yaml).pack(fill=tk.X, pady=2)

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)

        # 测量模式选择
        ttk.Label(p_frame, text='测量模式', font=('Arial', 9, 'bold')).pack(anchor='w')
        mode_frame = ttk.Frame(p_frame)
        mode_frame.pack(fill=tk.X, pady=2)

        self.mode_var = tk.IntVar(value=self._MODE_THREE_POINTS)
        ttk.Radiobutton(mode_frame, text='三点测量', variable=self.mode_var,
                       value=self._MODE_THREE_POINTS, command=self._on_mode_change).pack(anchor='w')
        ttk.Radiobutton(mode_frame, text='拟合直线', variable=self.mode_var,
                       value=self._MODE_FITTED_LINES, command=self._on_mode_change).pack(anchor='w')
        ttk.Radiobutton(mode_frame, text='ROI拟合', variable=self.mode_var,
                       value=self._MODE_ROI_FITTING, command=self._on_mode_change).pack(anchor='w')

        # 拟合参数
        param_frame = ttk.Frame(p_frame)
        param_frame.pack(fill=tk.X, pady=2)

        # RANSAC参数
        ransac_frame = ttk.Frame(param_frame)
        ransac_frame.pack(fill=tk.X)
        ttk.Label(ransac_frame, text='RANSAC:').pack(side='left')
        self.ransac_var = tk.DoubleVar(value=2.0)
        ttk.Entry(ransac_frame, textvariable=self.ransac_var, width=6).pack(side='left', padx=(5,10))

        # Canny参数（ROI模式用）
        canny_frame = ttk.Frame(param_frame)
        canny_frame.pack(fill=tk.X, pady=(2,0))
        ttk.Label(canny_frame, text='Canny1:').pack(side='left')
        self.canny1_var = tk.IntVar(value=50)
        ttk.Entry(canny_frame, textvariable=self.canny1_var, width=6).pack(side='left', padx=(5,5))
        ttk.Label(canny_frame, text='Canny2:').pack(side='left')
        self.canny2_var = tk.IntVar(value=150)
        ttk.Entry(canny_frame, textvariable=self.canny2_var, width=6).pack(side='left', padx=(5,0))

        # 拟合模式控制按钮
        self.fit_control_frame = ttk.Frame(p_frame)
        self.fit_control_frame.pack(fill=tk.X, pady=2)
        self.line1_btn = ttk.Button(self.fit_control_frame, text='采集直线1', command=self._start_line1)
        self.line1_btn.pack(side='left', padx=(0,5))
        self.line2_btn = ttk.Button(self.fit_control_frame, text='采集直线2', command=self._start_line2)
        self.line2_btn.pack(side='left', padx=(0,5))
        self.calc_btn = ttk.Button(self.fit_control_frame, text='计算角度', command=self._calc_fitted_angle)
        self.calc_btn.pack(side='left')

        # ROI拟合模式控制按钮
        self.roi_control_frame = ttk.Frame(p_frame)
        self.roi_control_frame.pack(fill=tk.X, pady=2)
        self.clear_roi_btn = ttk.Button(self.roi_control_frame, text='清除ROI', command=self._clear_roi)
        self.clear_roi_btn.pack(side='left', padx=(0,5))
        self.fit_roi_btn = ttk.Button(self.roi_control_frame, text='拟合角度', command=self._fit_roi_angle)
        self.fit_roi_btn.pack(side='left')

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)

        # 状态显示
        self.status_var = tk.StringVar(value='点击三个点：顶点 → 端点1 → 端点2')
        ttk.Label(p_frame, textvariable=self.status_var, wraplength=200, justify='left').pack(anchor='w', pady=2)

        ttk.Separator(p_frame, orient='horizontal').pack(fill=tk.X, pady=4)

        # 角度列表
        ttk.Label(p_frame, text='角度列表').pack(anchor='w')
        self.tree = ttk.Treeview(p_frame, columns=('type', 'deg'), show='headings', height=8)
        self.tree.heading('type', text='类型')
        self.tree.heading('deg', text='角度(°)')
        self.tree.column('type', width=60, anchor='center')
        self.tree.column('deg', width=80, anchor='center')
        self.tree.pack(fill=tk.BOTH, expand=False)
        self.tree.bind('<<TreeviewSelect>>', self._on_tree_select)

        self.result_var = tk.StringVar(value='角度: - °')
        ttk.Label(p_frame, textvariable=self.result_var, font=('Helvetica', 12)).pack(pady=8)

        # 画布
        self.canvas = tk.Canvas(self, width=PREVIEW_SIZE, height=PREVIEW_SIZE, bg='black', cursor='cross')
        self.canvas.grid(row=0, column=1)
        self.canvas.bind('<Button-1>', self._on_click)
        self.canvas.bind('<Button-3>', self._on_right_click)
        self.canvas.bind('<MouseWheel>', self._on_zoom)
        self.canvas.bind('<ButtonPress-2>', self._on_pan_start)  # 中键拖拽
        self.canvas.bind('<B2-Motion>', self._on_pan_move)
        # ROI拖拽事件
        self.canvas.bind('<ButtonPress-1>', self._on_roi_start)
        self.canvas.bind('<B1-Motion>', self._on_roi_drag)
        self.canvas.bind('<ButtonRelease-1>', self._on_roi_end)

        # 初始化界面状态（在canvas创建后）
        self._on_mode_change()

    def _on_mode_change(self):
        """测量模式切换"""
        self.measure_mode = self.mode_var.get()
        self._clear_marks()

        if self.measure_mode == self._MODE_THREE_POINTS:
            self.state = self._ST_WAIT_P0
            self.status_var.set('点击三个点：顶点 → 端点1 → 端点2')
            self.fit_control_frame.pack_forget()
            self.roi_control_frame.pack_forget()
        elif self.measure_mode == self._MODE_FITTED_LINES:
            self.state = self._ST_WAIT_LINE1
            self.status_var.set('点击"采集直线1"开始采集第一条直线的点')
            self.fit_control_frame.pack(fill=tk.X, pady=2)
            self.roi_control_frame.pack_forget()
        else:  # _MODE_ROI_FITTING
            self.state = self._ST_WAIT_ROI
            self.status_var.set('拖拽鼠标框选包含角度的ROI区域')
            self.fit_control_frame.pack_forget()
            self.roi_control_frame.pack(fill=tk.X, pady=2)

    def _start_line1(self):
        """开始采集第一条直线的点"""
        self.line1_pts.clear()
        self.state = self._ST_WAIT_LINE1
        self.status_var.set('点击多个点定义第一条直线（右键结束）')
        self._draw_overlays()

    def _start_line2(self):
        """开始采集第二条直线的点"""
        if len(self.line1_pts) < 2:
            messagebox.showwarning('警告', '第一条直线需要至少2个点')
            return
        self.line2_pts.clear()
        self.state = self._ST_WAIT_LINE2
        self.status_var.set('点击多个点定义第二条直线（右键结束）')
        self._draw_overlays()

    def _calc_fitted_angle(self):
        """计算拟合直线的角度"""
        if len(self.line1_pts) < 2:
            messagebox.showwarning('警告', '第一条直线需要至少2个点')
            return
        if len(self.line2_pts) < 2:
            messagebox.showwarning('警告', '第二条直线需要至少2个点')
            return

        # 使用拟合算法计算角度
        result = measure_angle_fitted_lines(self.line1_pts, self.line2_pts, self.ransac_var.get())

        if 'error' in result:
            messagebox.showerror('错误', result['error'])
            return

        # 保存结果
        roi = {
            'id': self.next_id,
            'type': 'fitted',
            'line1_pts': self.line1_pts.copy(),
            'line2_pts': self.line2_pts.copy(),
            'angle_deg': result['angle_deg'],
            'result': result
        }
        self.rois.append(roi)
        self.tree.insert('', 'end', iid=str(self.next_id),
                        values=('拟合', f"{result['angle_deg']:.2f}"))
        self.next_id += 1

        # 清除临时点，准备下一次测量
        self.line1_pts.clear()
        self.line2_pts.clear()
        self.state = self._ST_WAIT_LINE1
        self.status_var.set('点击"采集直线1"开始下一次测量')

        self.result_var.set(f"角度: {result['angle_deg']:.2f} °")
        self._draw_overlays()

    def _clear_roi(self):
        """清除ROI"""
        self.roi_rect = None
        self.state = self._ST_WAIT_ROI
        self.status_var.set('拖拽鼠标框选包含角度的ROI区域')
        self._draw_overlays()

    def _fit_roi_angle(self):
        """拟合ROI区域的角度"""
        if self.roi_rect is None:
            messagebox.showwarning('警告', '请先框选ROI区域')
            return

        if self.img_orig is None:
            messagebox.showwarning('警告', '请先加载图像')
            return

        try:
            # 提取ROI区域
            x0, y0, x1, y1 = self.roi_rect
            x0, y0, x1, y1 = int(x0), int(y0), int(x1), int(y1)

            # 确保坐标在图像范围内
            h, w = self.img_orig.shape[:2]
            x0, y0 = max(0, x0), max(0, y0)
            x1, y1 = min(w, x1), min(h, y1)

            if x1 - x0 < 10 or y1 - y0 < 10:
                messagebox.showwarning('警告', 'ROI区域太小')
                return

            roi_img = self.img_orig[y0:y1, x0:x1]

            # 预处理ROI图像
            gray = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY)

            # 高斯模糊减少噪声
            gray = cv2.GaussianBlur(gray, (3, 3), 0)

            # 检测黑白交接的边沿线
            edge_lines = self._detect_edge_lines(gray, x0, y0)

            if len(edge_lines) < 2:
                messagebox.showwarning('警告', f'ROI区域中检测到的边沿线太少({len(edge_lines)}条)，请调整参数或重新选择ROI')
                return

            print(f"检测到 {len(edge_lines)} 条边沿线")

            # 直接使用检测到的边沿线拟合角度
            result = self._fit_angle_from_edge_lines(edge_lines)

            if 'error' in result:
                messagebox.showerror('错误', result['error'])
                return

            # 添加边沿线信息用于可视化
            result['edge_lines'] = edge_lines
            result['roi_rect'] = self.roi_rect

            # 保存结果
            roi = {
                'id': self.next_id,
                'type': 'roi_fitted',
                'roi_rect': self.roi_rect,
                'angle_deg': result['angle_deg'],
                'result': result
            }
            self.rois.append(roi)
            self.tree.insert('', 'end', iid=str(self.next_id),
                           values=('ROI', f"{result['angle_deg']:.2f}"))
            self.next_id += 1

            self.result_var.set(f"角度: {result['angle_deg']:.2f} °")
            self._draw_overlays()

        except Exception as e:
            messagebox.showerror('错误', f'拟合失败: {str(e)}')

    def _detect_edge_lines(self, gray_roi, x_offset, y_offset):
        """检测ROI中的黑白交接边沿线"""
        try:
            h, w = gray_roi.shape

            # 方法1: 基于梯度的边沿检测
            edge_lines = self._detect_gradient_edges(gray_roi, x_offset, y_offset)

            if len(edge_lines) >= 2:
                return edge_lines

            print("梯度方法检测边沿线不足，尝试轮廓方法")

            # 方法2: 基于轮廓的边沿检测
            edge_lines = self._detect_contour_edges(gray_roi, x_offset, y_offset)

            return edge_lines

        except Exception as e:
            print(f"边沿线检测失败: {str(e)}")
            return []

    def _detect_gradient_edges(self, gray_roi, x_offset, y_offset):
        """基于梯度检测黑白交界边沿"""
        try:
            # 计算梯度
            grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)

            # 计算梯度幅值和方向
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            direction = np.arctan2(grad_y, grad_x)

            # 找到强梯度点（黑白交界处）
            threshold = np.percentile(magnitude, 85)  # 取85%分位数作为阈值
            strong_edges = magnitude > threshold

            # 获取强边缘点的坐标
            edge_points = np.column_stack(np.nonzero(strong_edges))

            if len(edge_points) < 10:
                print(f"强梯度点太少: {len(edge_points)}")
                return []

            print(f"检测到 {len(edge_points)} 个强梯度点")

            # 根据梯度方向聚类边缘点
            directions = direction[strong_edges]

            # 将方向转换为0-180度
            directions_deg = np.degrees(directions) % 180

            # 使用方向聚类分离不同的边沿线
            edge_lines = self._cluster_by_direction(edge_points, directions_deg, x_offset, y_offset)

            return edge_lines

        except Exception as e:
            print(f"梯度边沿检测失败: {str(e)}")
            return []

    def _detect_contour_edges(self, gray_roi, x_offset, y_offset):
        """基于轮廓检测黑白交界边沿"""
        try:
            # 二值化
            _, binary = cv2.threshold(gray_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                print("未找到轮廓")
                return []

            # 找到最大的轮廓
            largest_contour = max(contours, key=cv2.contourArea)

            if len(largest_contour) < 10:
                print("轮廓点太少")
                return []

            # 使用Douglas-Peucker算法简化轮廓为直线段
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            approx = cv2.approxPolyDP(largest_contour, epsilon, True)

            if len(approx) < 3:
                print("简化后的轮廓点太少")
                return []

            # 将轮廓段转换为边沿线
            edge_lines = []
            for i in range(len(approx)):
                p1 = approx[i][0]
                p2 = approx[(i+1) % len(approx)][0]

                # 计算线段长度
                length = np.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2)

                if length < 15:  # 过滤太短的线段
                    continue

                # 转换为图像坐标
                x1_img, y1_img = p1[0] + x_offset, p1[1] + y_offset
                x2_img, y2_img = p2[0] + x_offset, p2[1] + y_offset

                # 计算角度
                angle = np.degrees(np.arctan2(p2[1]-p1[1], p2[0]-p1[0])) % 180

                edge_lines.append({
                    'start': (x1_img, y1_img),
                    'end': (x2_img, y2_img),
                    'length': length,
                    'angle': angle,
                    'roi_start': (p1[0], p1[1]),
                    'roi_end': (p2[0], p2[1])
                })

            print(f"轮廓方法检测到 {len(edge_lines)} 条边沿线")
            for i, line in enumerate(edge_lines):
                print(f"  线{i+1}: 长度={line['length']:.1f}, 角度={line['angle']:.1f}°")

            return edge_lines

        except Exception as e:
            print(f"轮廓边沿检测失败: {str(e)}")
            return []

    def _cluster_by_direction(self, edge_points, directions, x_offset, y_offset):
        """根据梯度方向聚类边缘点"""
        try:
            # 将方向分为几个主要区间
            direction_bins = np.arange(0, 180, 20)  # 每20度一个区间

            clustered_lines = []

            for i in range(len(direction_bins)-1):
                bin_start, bin_end = direction_bins[i], direction_bins[i+1]

                # 找到在这个方向区间内的点
                mask = (directions >= bin_start) & (directions < bin_end)
                cluster_points = edge_points[mask]

                if len(cluster_points) < 5:
                    continue

                # 对这些点进行直线拟合
                try:
                    # 转换坐标 (row, col) -> (x, y)
                    points_xy = cluster_points[:, [1, 0]]  # 交换行列

                    # 使用最小二乘法拟合直线
                    if len(points_xy) >= 2:
                        # 计算直线的两个端点
                        x_coords = points_xy[:, 0]
                        y_coords = points_xy[:, 1]

                        x_min, x_max = x_coords.min(), x_coords.max()
                        y_min, y_max = y_coords.min(), y_coords.max()

                        # 选择变化范围更大的坐标作为主轴
                        if (x_max - x_min) > (y_max - y_min):
                            # 以x为主轴
                            A = np.vstack([x_coords, np.ones(len(x_coords))]).T
                            m, b = np.linalg.lstsq(A, y_coords, rcond=None)[0]

                            start_x, start_y = x_min, m * x_min + b
                            end_x, end_y = x_max, m * x_max + b
                        else:
                            # 以y为主轴
                            A = np.vstack([y_coords, np.ones(len(y_coords))]).T
                            m, b = np.linalg.lstsq(A, x_coords, rcond=None)[0]

                            start_y, start_x = y_min, m * y_min + b
                            end_y, end_x = y_max, m * y_max + b

                        # 转换为图像坐标
                        start_img = (start_x + x_offset, start_y + y_offset)
                        end_img = (end_x + x_offset, end_y + y_offset)

                        length = np.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
                        angle = np.degrees(np.arctan2(end_y - start_y, end_x - start_x)) % 180

                        if length > 15:  # 过滤太短的线段
                            clustered_lines.append({
                                'start': start_img,
                                'end': end_img,
                                'length': length,
                                'angle': angle,
                                'roi_start': (start_x, start_y),
                                'roi_end': (end_x, end_y),
                                'point_count': len(cluster_points)
                            })

                except Exception as e:
                    print(f"拟合方向{bin_start}-{bin_end}度的直线失败: {str(e)}")
                    continue

            # 按点数和长度排序
            clustered_lines.sort(key=lambda x: x['point_count'] * x['length'], reverse=True)

            print(f"方向聚类得到 {len(clustered_lines)} 条边沿线")
            for i, line in enumerate(clustered_lines):
                print(f"  线{i+1}: 长度={line['length']:.1f}, 角度={line['angle']:.1f}°, 点数={line['point_count']}")

            return clustered_lines

        except Exception as e:
            print(f"方向聚类失败: {str(e)}")
            return []

    def _fit_angle_from_edge_lines(self, edge_lines):
        """从检测到的边沿线拟合角度"""
        try:
            if len(edge_lines) < 2:
                return {'error': '需要至少2条边沿线'}

            # 选择最长的两条线，且角度差异较大的
            best_lines = self._select_best_line_pair(edge_lines)

            if best_lines is None:
                return {'error': '无法找到合适的线对'}

            line1, line2 = best_lines

            # 将线段转换为点列表进行拟合
            points1 = [line1['start'], line1['end']]
            points2 = [line2['start'], line2['end']]

            # 使用现有的拟合函数
            result = measure_angle_fitted_lines(points1, points2, self.ransac_var.get())

            # 添加线段信息
            result['selected_lines'] = [line1, line2]
            result['line1_points'] = points1
            result['line2_points'] = points2

            return result

        except Exception as e:
            return {'error': f'角度拟合失败: {str(e)}'}

    def _select_best_line_pair(self, edge_lines):
        """选择最佳的线对用于角度测量"""
        if len(edge_lines) < 2:
            return None

        # 尝试不同的线对组合
        best_pair = None
        best_score = 0

        for i in range(len(edge_lines)):
            for j in range(i+1, len(edge_lines)):
                line1, line2 = edge_lines[i], edge_lines[j]

                # 计算角度差
                angle_diff = abs(line1['angle'] - line2['angle'])
                if angle_diff > 90:
                    angle_diff = 180 - angle_diff

                # 角度差应该在合理范围内（20-160度）
                if angle_diff < 20 or angle_diff > 160:
                    continue

                # 计算评分：长度 + 角度差异
                length_score = (line1['length'] + line2['length']) / 2
                angle_score = min(angle_diff, 180 - angle_diff)  # 偏向90度

                score = length_score + angle_score * 2

                if score > best_score:
                    best_score = score
                    best_pair = (line1, line2)

        if best_pair:
            print(f"选择最佳线对: 角度差={abs(best_pair[0]['angle'] - best_pair[1]['angle']):.1f}°, 评分={best_score:.1f}")

        return best_pair

    def _cluster_and_fit_lines(self, points):
        """聚类边缘点并拟合两条直线"""
        try:
            # 首先尝试使用sklearn进行聚类
            from sklearn.cluster import DBSCAN
            import numpy as np

            # 使用DBSCAN聚类分离两条直线
            # 调整参数以更好地分离直线
            clustering = DBSCAN(eps=8, min_samples=5).fit(points)
            labels = clustering.labels_

            # 找到两个最大的聚类
            unique_labels = np.unique(labels)
            cluster_sizes = [(label, np.sum(labels == label)) for label in unique_labels if label != -1]

            if len(cluster_sizes) < 2:
                print(f"聚类结果: {len(cluster_sizes)}个聚类，尝试简单分离方法")
                return self._simple_line_separation(points)

            # 按大小排序，取前两个
            cluster_sizes.sort(key=lambda x: x[1], reverse=True)
            label1, label2 = cluster_sizes[0][0], cluster_sizes[1][0]

            points1 = points[labels == label1]
            points2 = points[labels == label2]

            print(f"聚类结果: 直线1有{len(points1)}个点，直线2有{len(points2)}个点")

            if len(points1) < 5 or len(points2) < 5:
                return {'error': f'直线点数太少: 直线1={len(points1)}, 直线2={len(points2)}'}

            # 拟合两条直线并计算角度
            result = measure_angle_fitted_lines(
                [(p[0], p[1]) for p in points1],
                [(p[0], p[1]) for p in points2],
                self.ransac_var.get()
            )

            # 添加聚类信息和点信息用于可视化
            result['cluster_info'] = {
                'points1_count': len(points1),
                'points2_count': len(points2),
                'total_clusters': len(cluster_sizes)
            }
            result['line1_points'] = [(p[0], p[1]) for p in points1]
            result['line2_points'] = [(p[0], p[1]) for p in points2]

            return result

        except ImportError:
            print("sklearn不可用，使用简单分离方法")
            return self._simple_line_separation(points)
        except Exception as e:
            print(f"聚类失败: {str(e)}，尝试简单分离方法")
            return self._simple_line_separation(points)

    def _simple_line_separation(self, points):
        """简单的直线分离方法（不依赖sklearn）"""
        try:
            print(f"使用简单分离方法，总点数: {len(points)}")

            # 方法1: 基于角度的分离
            result = self._angle_based_separation(points)
            if 'error' not in result:
                return result

            print("角度分离失败，尝试PCA分离")

            # 方法2: 基于PCA的分离
            center = np.mean(points, axis=0)
            centered_points = points - center

            # 使用PCA找主方向
            cov_matrix = np.cov(centered_points.T)
            _, eigenvectors = np.linalg.eigh(cov_matrix)
            main_direction = eigenvectors[:, -1]

            # 将点投影到垂直于主方向的直线上
            perpendicular = np.array([-main_direction[1], main_direction[0]])
            projections = np.dot(centered_points, perpendicular)

            # 使用更智能的分割方法
            # 尝试找到两个峰值
            hist, bin_edges = np.histogram(projections, bins=20)
            peak_indices = []
            for i in range(1, len(hist)-1):
                if hist[i] > hist[i-1] and hist[i] > hist[i+1] and hist[i] > len(points) * 0.05:
                    peak_indices.append(i)

            if len(peak_indices) >= 2:
                # 使用峰值分离
                peak1_bin = bin_edges[peak_indices[0]]
                peak2_bin = bin_edges[peak_indices[-1]]
                threshold = (peak1_bin + peak2_bin) / 2
            else:
                # 使用中位数分离
                threshold = np.median(projections)

            mask1 = projections <= threshold
            mask2 = projections > threshold

            points1 = points[mask1]
            points2 = points[mask2]

            print(f"PCA分离结果: 直线1={len(points1)}点, 直线2={len(points2)}点")

            if len(points1) < 5 or len(points2) < 5:
                return {'error': f'分离后的直线点数太少: 直线1={len(points1)}, 直线2={len(points2)}'}

            # 拟合两条直线
            result = measure_angle_fitted_lines(
                [(p[0], p[1]) for p in points1],
                [(p[0], p[1]) for p in points2],
                self.ransac_var.get()
            )

            # 添加点信息用于可视化
            result['line1_points'] = [(p[0], p[1]) for p in points1]
            result['line2_points'] = [(p[0], p[1]) for p in points2]

            return result

        except Exception as e:
            return {'error': f'简单分离失败: {str(e)}'}

    def _angle_based_separation(self, points):
        """基于角度的点分离方法"""
        try:
            # 计算每个点相对于中心的角度
            center = np.mean(points, axis=0)
            centered_points = points - center

            # 计算角度
            angles = np.arctan2(centered_points[:, 1], centered_points[:, 0])
            angles = np.degrees(angles) % 360  # 转换为0-360度

            # 使用角度直方图找到两个主要方向
            hist, bin_edges = np.histogram(angles, bins=36)  # 每10度一个bin

            # 找到两个最大的峰值
            peak_indices = []
            for i in range(len(hist)):
                if hist[i] > len(points) * 0.1:  # 至少包含10%的点
                    peak_indices.append((i, hist[i]))

            if len(peak_indices) < 2:
                return {'error': '无法找到两个明显的角度方向'}

            # 按点数排序，取前两个
            peak_indices.sort(key=lambda x: x[1], reverse=True)
            peak1_bin, peak2_bin = peak_indices[0][0], peak_indices[1][0]

            # 计算分离角度
            angle1 = bin_edges[peak1_bin] + 5  # bin中心
            angle2 = bin_edges[peak2_bin] + 5

            # 确保角度差足够大
            angle_diff = abs(angle1 - angle2)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            if angle_diff < 30:  # 角度差太小
                return {'error': f'两个方向角度差太小: {angle_diff:.1f}度'}

            # 根据角度分离点
            tolerance = 30  # 角度容差
            mask1 = np.abs((angles - angle1 + 180) % 360 - 180) <= tolerance
            mask2 = np.abs((angles - angle2 + 180) % 360 - 180) <= tolerance

            points1 = points[mask1]
            points2 = points[mask2]

            print(f"角度分离结果: 方向1={angle1:.1f}°({len(points1)}点), 方向2={angle2:.1f}°({len(points2)}点)")

            if len(points1) < 5 or len(points2) < 5:
                return {'error': f'角度分离后点数太少: 方向1={len(points1)}, 方向2={len(points2)}'}

            # 拟合两条直线
            result = measure_angle_fitted_lines(
                [(p[0], p[1]) for p in points1],
                [(p[0], p[1]) for p in points2],
                self.ransac_var.get()
            )

            # 添加点信息用于可视化
            result['line1_points'] = [(p[0], p[1]) for p in points1]
            result['line2_points'] = [(p[0], p[1]) for p in points2]

            return result

        except Exception as e:
            return {'error': f'角度分离失败: {str(e)}'}

    # ------------------ 图像处理 ------------------
    def load_image(self, img: np.ndarray):
        self.img_orig = img.copy()
        h, w = img.shape[:2]
        self._zoom = PREVIEW_SIZE / max(h, w)
        self._offset = np.array([(PREVIEW_SIZE - w*self._zoom)/2, (PREVIEW_SIZE - h*self._zoom)/2])
        self._update_canvas_image()

    def _open_image(self):
        f = filedialog.askopenfilename(filetypes=[('Image','*.png;*.jpg;*.bmp')])
        if not f:
            return
        img = cv2.imread(f)
        if img is None:
            messagebox.showerror('错误', '无法读取图像')
            return
        self.load_image(img)
        self.rois.clear(); self.tree.delete(*self.tree.get_children()); self.current_idx=None; self.next_id=1

    def _update_canvas_image(self):
        if self.img_orig is None:
            return
        rgb = cv2.cvtColor(self.img_orig, cv2.COLOR_BGR2RGB)
        h, w = rgb.shape[:2]
        disp = cv2.resize(rgb, (int(w*self._zoom), int(h*self._zoom)), interpolation=cv2.INTER_AREA)
        self.img_display = disp
        img_pil = Image.fromarray(disp)
        self.tk_img = ImageTk.PhotoImage(img_pil)
        self.canvas.create_image(self._offset[0], self._offset[1], anchor='nw', image=self.tk_img, tags='img')
        self._draw_overlays()

    # ------------------ 交互 ------------------
    def _on_click(self, e):
        if self.img_orig is None:
            return
        # canvas -> img coords
        x_img = (e.x - self._offset[0]) / self._zoom
        y_img = (e.y - self._offset[1]) / self._zoom
        if not (0 <= x_img < self.img_orig.shape[1] and 0 <= y_img < self.img_orig.shape[0]):
            return

        if self.measure_mode == self._MODE_THREE_POINTS:
            self._handle_three_point_click(x_img, y_img)
        elif self.measure_mode == self._MODE_FITTED_LINES:
            self._handle_fitted_line_click(x_img, y_img)
        elif self.measure_mode == self._MODE_ROI_FITTING:
            self._handle_roi_click(x_img, y_img)

    def _handle_three_point_click(self, x_img, y_img):
        """处理三点测量模式的点击"""
        self.tmp_pts.append((x_img, y_img))
        if len(self.tmp_pts) == 3:
            res = measure_angle_pts(*self.tmp_pts)
            if 'error' in res:
                messagebox.showerror('错误', res['error'])
                self.tmp_pts.clear()
                return
            roi = {
                'id': self.next_id,
                'type': 'three_point',
                'pts': self.tmp_pts.copy(),
                'angle_deg': res['angle_deg'],
            }
            self.rois.append(roi)
            self.tree.insert('', 'end', iid=str(self.next_id),
                           values=('三点', f"{res['angle_deg']:.2f}"))
            self.next_id += 1
            self.tmp_pts.clear()
            self.result_var.set(f"角度: {res['angle_deg']:.2f} °")
            self._draw_overlays()
        else:
            self._draw_overlays()

    def _handle_fitted_line_click(self, x_img, y_img):
        """处理拟合直线模式的点击"""
        if self.state == self._ST_WAIT_LINE1:
            self.line1_pts.append((x_img, y_img))
            self.status_var.set(f'第一条直线: {len(self.line1_pts)}个点 (右键结束)')
        elif self.state == self._ST_WAIT_LINE2:
            self.line2_pts.append((x_img, y_img))
            self.status_var.set(f'第二条直线: {len(self.line2_pts)}个点 (右键结束)')
        self._draw_overlays()

    def _handle_roi_click(self, x_img, y_img):
        """处理ROI模式的点击（这个方法实际不会被调用，因为ROI使用拖拽）"""
        pass

    def _on_roi_start(self, e):
        """开始ROI拖拽"""
        if self.measure_mode != self._MODE_ROI_FITTING or self.img_orig is None:
            return

        # 转换为图像坐标
        x_img = (e.x - self._offset[0]) / self._zoom
        y_img = (e.y - self._offset[1]) / self._zoom

        if 0 <= x_img < self.img_orig.shape[1] and 0 <= y_img < self.img_orig.shape[0]:
            self._dragging_roi = True
            self._roi_start = (x_img, y_img)
            self.roi_rect = None

    def _on_roi_drag(self, e):
        """ROI拖拽过程"""
        if not self._dragging_roi or self._roi_start is None:
            return

        # 转换为图像坐标
        x_img = (e.x - self._offset[0]) / self._zoom
        y_img = (e.y - self._offset[1]) / self._zoom

        if 0 <= x_img < self.img_orig.shape[1] and 0 <= y_img < self.img_orig.shape[0]:
            x0, y0 = self._roi_start
            self.roi_rect = (min(x0, x_img), min(y0, y_img), max(x0, x_img), max(y0, y_img))
            self._draw_overlays()

    def _on_roi_end(self, e):
        """结束ROI拖拽"""
        if self._dragging_roi:
            self._dragging_roi = False
            if self.roi_rect is not None:
                x0, y0, x1, y1 = self.roi_rect
                if abs(x1 - x0) > 10 and abs(y1 - y0) > 10:
                    self.state = self._ST_ROI_SET
                    self.status_var.set('ROI已设置，点击"拟合角度"进行测量')
                else:
                    self.roi_rect = None
                    self.status_var.set('ROI太小，请重新拖拽')
            self._draw_overlays()

    def _on_right_click(self, e):
        """右键点击处理 - 结束直线采集"""
        if self.measure_mode == self._MODE_FITTED_LINES:
            if self.state == self._ST_WAIT_LINE1 and len(self.line1_pts) >= 2:
                self.status_var.set('第一条直线采集完成，点击"采集直线2"')
            elif self.state == self._ST_WAIT_LINE2 and len(self.line2_pts) >= 2:
                self.status_var.set('第二条直线采集完成，点击"计算角度"')
            self._draw_overlays()

    def _on_tree_select(self, evt):
        sel = self.tree.selection()
        if not sel:
            self.current_idx = None
        else:
            idx = int(sel[0])
            for i, roi in enumerate(self.rois):
                if roi['id'] == idx:
                    self.current_idx = i
                    self.result_var.set(f"角度: {roi['angle_deg']:.2f} °")
                    break
        self._draw_overlays()

    # ------------------ 缩放/拖拽 ------------------
    def _on_zoom(self, e):
        if self.img_orig is None:
            return
        factor = 1.1 if e.delta > 0 else 0.9
        self._zoom *= factor
        # 以鼠标点为中心
        mx, my = e.x, e.y
        ox, oy = self._offset
        self._offset = np.array([mx - (mx-ox)*factor, my - (my-oy)*factor])
        self._update_canvas_image()

    def _on_pan_start(self, e):
        self._pan_start = (e.x, e.y)
    def _on_pan_move(self, e):
        if self._pan_start is None:
            return
        dx = e.x - self._pan_start[0]
        dy = e.y - self._pan_start[1]
        self._offset += np.array([dx, dy])
        self._pan_start = (e.x, e.y)
        self._update_canvas_image()

    # ------------------ 文件 ------------------
    def _to_py(self, o):
        if isinstance(o, np.ndarray):
            return o.tolist()
        return o
    def _save_yaml(self):
        if not self.rois:
            messagebox.showinfo('提示','没有数据')
            return
        f = filedialog.asksaveasfilename(initialdir=CFG_DIR, defaultextension='.yaml', filetypes=[('YAML','*.yaml')])
        if not f:
            return
        data = [{k:self._to_py(v) for k,v in roi.items()} for roi in self.rois]
        with open(f, 'w', encoding='utf-8') as fp:
            yaml.safe_dump(data, fp, allow_unicode=True)
        messagebox.showinfo('保存','已保存')
    def _load_yaml(self):
        f = filedialog.askopenfilename(initialdir=CFG_DIR, filetypes=[('YAML','*.yaml')])
        if not f:
            return
        with open(f, 'r', encoding='utf-8') as fp:
            data = yaml.safe_load(fp)
        self.rois.clear(); self.tree.delete(*self.tree.get_children()); self.current_idx=None; self.next_id=1
        for item in data:
            pts = item['pts']; deg = item['angle_deg']
            roi = {'id': self.next_id, 'pts': pts, 'angle_deg': deg}
            self.rois.append(roi)
            self.tree.insert('', 'end', iid=str(self.next_id), values=(f"{deg:.2f}",))
            self.next_id += 1
        self._draw_overlays()

    # ------------------ 绘制 ------------------
    def _draw_overlays(self):
        if not hasattr(self, 'canvas'):
            return
        self.canvas.delete('ov')
        if self.img_orig is None:
            return

        # 已完成的测量结果
        for roi in self.rois:
            is_selected = self.current_idx is not None and self.rois[self.current_idx]['id'] == roi['id']
            color = 'lime' if is_selected else 'green'

            if roi['type'] == 'three_point':
                # 三点测量显示
                pts = roi['pts']
                self._draw_point(pts[0], color=color, r=4)  # 顶点
                self._draw_point(pts[1], color=color)
                self._draw_point(pts[2], color=color)
                self._draw_line(pts[0], pts[1], color)
                self._draw_line(pts[0], pts[2], color)
            elif roi['type'] == 'fitted':
                # 拟合直线显示
                self._draw_fitted_lines(roi, color, is_selected)
            elif roi['type'] == 'roi_fitted':
                # ROI拟合显示
                self._draw_roi_fitted(roi, color, is_selected)

        # 正在输入的临时点（三点模式）
        if self.measure_mode == self._MODE_THREE_POINTS and self.tmp_pts:
            for pt in self.tmp_pts:
                self._draw_point(pt, color='cyan')
            if len(self.tmp_pts) == 2:
                self._draw_line(self.tmp_pts[0], self.tmp_pts[1], 'cyan')

        # 正在采集的直线点（拟合模式）
        if self.measure_mode == self._MODE_FITTED_LINES:
            # 第一条直线的点
            for pt in self.line1_pts:
                self._draw_point(pt, color='red', r=2)
            if len(self.line1_pts) >= 2:
                self._draw_fitted_line_preview(self.line1_pts, 'red')

            # 第二条直线的点
            for pt in self.line2_pts:
                self._draw_point(pt, color='blue', r=2)
            if len(self.line2_pts) >= 2:
                self._draw_fitted_line_preview(self.line2_pts, 'blue')

        # 正在拖拽的ROI（ROI拟合模式）
        if self.measure_mode == self._MODE_ROI_FITTING and self.roi_rect is not None:
            self._draw_roi_rect(self.roi_rect, 'cyan', 2)

        self.canvas.tag_raise('ov')

    def _draw_fitted_lines(self, roi, color, is_selected):
        """绘制拟合直线的结果"""
        result = roi.get('result', {})
        if 'line1_result' not in result or 'line2_result' not in result:
            return

        # 绘制原始点
        for pt in roi['line1_pts']:
            self._draw_point(pt, color='red', r=1)
        for pt in roi['line2_pts']:
            self._draw_point(pt, color='blue', r=1)

        # 绘制拟合直线
        self._draw_fitted_line_from_result(result['line1_result'], 'red' if is_selected else 'darkred')
        self._draw_fitted_line_from_result(result['line2_result'], 'blue' if is_selected else 'darkblue')

        # 绘制交点
        if result.get('intersection'):
            self._draw_point(result['intersection'], color=color, r=4)

    def _draw_fitted_line_preview(self, points, color):
        """绘制正在采集的直线预览"""
        if len(points) < 2:
            return
        # 简单连线预览
        for i in range(len(points) - 1):
            self._draw_line(points[i], points[i+1], color, width=1)

    def _draw_fitted_line_from_result(self, line_result, color):
        """根据拟合结果绘制直线"""
        if 'point_on_line' not in line_result or 'direction' not in line_result:
            return

        x0, y0 = line_result['point_on_line']
        dx, dy = line_result['direction']

        # 计算直线在图像范围内的端点
        h, w = self.img_orig.shape[:2]

        # 扩展直线到图像边界
        t_max = max(w, h) * 2
        x1, y1 = x0 - dx * t_max, y0 - dy * t_max
        x2, y2 = x0 + dx * t_max, y0 + dy * t_max

        self._draw_line((x1, y1), (x2, y2), color, width=2)

    def _draw_roi_fitted(self, roi, color, is_selected):
        """绘制ROI拟合的结果"""
        # 绘制ROI矩形
        roi_rect = roi['roi_rect']
        self._draw_roi_rect(roi_rect, color, 2 if is_selected else 1)

        # 绘制拟合结果
        result = roi.get('result', {})
        if 'line1_result' in result and 'line2_result' in result:
            # 绘制限制在ROI内的拟合直线
            self._draw_fitted_line_in_roi(result['line1_result'], roi_rect, 'red' if is_selected else 'darkred')
            self._draw_fitted_line_in_roi(result['line2_result'], roi_rect, 'blue' if is_selected else 'darkblue')

            # 如果选中，显示边缘点
            if is_selected:
                self._draw_roi_edge_points(result, roi_rect)

            # 绘制交点
            if result.get('intersection'):
                self._draw_point(result['intersection'], color=color, r=4)

    def _draw_roi_rect(self, roi_rect, color, width=1):
        """绘制ROI矩形"""
        x0, y0, x1, y1 = roi_rect
        # 转换为画布坐标
        x0_c, y0_c = self._img_to_canvas((x0, y0))
        x1_c, y1_c = self._img_to_canvas((x1, y1))
        self.canvas.create_rectangle(x0_c, y0_c, x1_c, y1_c, outline=color, width=width, tags='ov')

    def _draw_fitted_line_in_roi(self, line_result, roi_rect, color):
        """在ROI区域内绘制拟合直线"""
        if 'point_on_line' not in line_result or 'direction' not in line_result:
            return

        x0_roi, y0_roi, x1_roi, y1_roi = roi_rect
        px, py = line_result['point_on_line']
        dx, dy = line_result['direction']

        # 计算直线与ROI边界的交点
        intersections = []

        # 与左边界的交点 (x = x0_roi)
        if abs(dx) > 1e-10:
            t = (x0_roi - px) / dx
            y = py + t * dy
            if y0_roi <= y <= y1_roi:
                intersections.append((x0_roi, y))

        # 与右边界的交点 (x = x1_roi)
        if abs(dx) > 1e-10:
            t = (x1_roi - px) / dx
            y = py + t * dy
            if y0_roi <= y <= y1_roi:
                intersections.append((x1_roi, y))

        # 与上边界的交点 (y = y0_roi)
        if abs(dy) > 1e-10:
            t = (y0_roi - py) / dy
            x = px + t * dx
            if x0_roi <= x <= x1_roi:
                intersections.append((x, y0_roi))

        # 与下边界的交点 (y = y1_roi)
        if abs(dy) > 1e-10:
            t = (y1_roi - py) / dy
            x = px + t * dx
            if x0_roi <= x <= x1_roi:
                intersections.append((x, y1_roi))

        # 去除重复点
        unique_intersections = []
        for pt in intersections:
            is_duplicate = False
            for existing_pt in unique_intersections:
                if abs(pt[0] - existing_pt[0]) < 1 and abs(pt[1] - existing_pt[1]) < 1:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_intersections.append(pt)

        # 绘制直线段
        if len(unique_intersections) >= 2:
            pt1, pt2 = unique_intersections[0], unique_intersections[1]
            self._draw_line(pt1, pt2, color, width=2)
        elif len(unique_intersections) == 1:
            # 如果只有一个交点，从ROI中心到交点绘制
            center_x = (x0_roi + x1_roi) / 2
            center_y = (y0_roi + y1_roi) / 2
            self._draw_line((center_x, center_y), unique_intersections[0], color, width=2)

    def _draw_roi_edge_points(self, result, roi_rect):
        """绘制ROI内的边沿线（用于调试）"""
        # 绘制检测到的所有边沿线（灰色）
        if 'edge_lines' in result:
            for line in result['edge_lines']:
                self._draw_line(line['start'], line['end'], 'gray', width=1)

        # 绘制选中的边沿线
        if 'selected_lines' in result:
            for i, line in enumerate(result['selected_lines']):
                color = 'red' if i == 0 else 'blue'
                self._draw_line(line['start'], line['end'], color, width=3)

        # 绘制拟合的直线端点
        if 'line1_points' in result and 'line2_points' in result:
            for pt in result['line1_points']:
                self._draw_point(pt, color='red', r=3)
            for pt in result['line2_points']:
                self._draw_point(pt, color='blue', r=3)

    def _img_to_canvas(self, pt):
        x, y = pt
        return x*self._zoom + self._offset[0], y*self._zoom + self._offset[1]
    def _draw_point(self, pt, color='red', r: int = 3):
        xc, yc = self._img_to_canvas(pt)
        self.canvas.create_oval(xc-r, yc-r, xc+r, yc+r, outline=color, fill=color, tags='ov')
    def _draw_line(self, p1, p2, color='yellow', width=2):
        x1, y1 = self._img_to_canvas(p1)
        x2, y2 = self._img_to_canvas(p2)
        self.canvas.create_line(x1, y1, x2, y2, fill=color, width=width, tags='ov')

    # ------------------ utils ------------------
    def _clear_marks(self):
        self.rois.clear()
        self.tmp_pts.clear()
        self.line1_pts.clear()
        self.line2_pts.clear()
        self.roi_rect = None
        self._dragging_roi = False
        self._roi_start = None
        self.tree.delete(*self.tree.get_children())
        self.current_idx = None
        self.next_id = 1
        self.result_var.set('角度: - °')
        if hasattr(self, 'status_var'):
            if self.measure_mode == self._MODE_THREE_POINTS:
                self.status_var.set('点击三个点：顶点 → 端点1 → 端点2')
            elif self.measure_mode == self._MODE_FITTED_LINES:
                self.status_var.set('点击"采集直线1"开始采集第一条直线的点')
            else:  # _MODE_ROI_FITTING
                self.status_var.set('拖拽鼠标框选包含角度的ROI区域')
        self._draw_overlays()

    def _on_close(self):
        if self.master and isinstance(self.master, tk.Tk):
            self.master.quit()
            self.master.destroy()

def launch(img=None):
    """启动角度测量工具

    Args:
        img: 输入图像 (可选)

    Returns:
        测量结果字典
    """
    # 创建主窗口
    root = tk.Tk()
    root.title("角度测量工具")
    root.geometry("1000x750")

    # 创建角度测量工具作为主窗口的内容
    app = AngleToolUI(root, img)

    # 启动主循环
    root.mainloop()

    # 返回测量结果 (可以从app中获取)
    results = []
    for roi in app.rois:
        if 'angle_deg' in roi:
            results.append({
                'type': roi.get('type', 'unknown'),
                'angle_deg': roi['angle_deg'],
                'angle_rad': roi.get('angle_rad', roi['angle_deg'] * 3.141592653589793 / 180.0),
                'id': roi['id']
            })

    return {
        'measurements': results,
        'total_count': len(results),
        'tool': 'angle_tool'
    }

if __name__ == '__main__':
    # 允许直接 python angle_tool_ui.py [image]
    img_path = sys.argv[1] if len(sys.argv) > 1 else None
    img = cv2.imread(img_path) if img_path else None

    # 启动工具
    result = launch(img)
    print("测量结果:", result)
