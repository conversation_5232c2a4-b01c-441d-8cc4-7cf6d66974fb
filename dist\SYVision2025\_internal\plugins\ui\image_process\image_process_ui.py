"""Image Source UI：中文界面支持实时预览、拍照、文件/文件夹选择。"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
from pathlib import Path

import sys

BASE_DIR = Path(__file__).resolve().parents[3]
if str(BASE_DIR) not in sys.path:
    sys.path.append(str(BASE_DIR))

from core.camera_hikvision import CameraHikvision
from plugins.core.image_process.image_source import create_source, ImageSource

PREVIEW = 400

class ImageSourceUI(tk.Toplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title('图像采集源')
        self.geometry('800x600')
        self._src: ImageSource | None = None
        self._photo = None
        self._build_ui()
        self._running = False  # 相机实时刷新标志
        self.protocol('WM_DELETE_WINDOW', self._on_close)

    # ---------------- UI -----------------
    def _build_ui(self):
        left = ttk.Frame(self)
        left.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        right = ttk.Frame(self)
        right.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        ttk.Label(left, text='模式').pack(anchor=tk.W)
        self._mode_map = {'相机':'camera', '文件夹':'folder', '单图':'file'}
        self.mode_var = tk.StringVar(value='相机')
        self.mode_cb = ttk.Combobox(left, textvariable=self.mode_var, values=list(self._mode_map.keys()), state='readonly')
        self.mode_cb.pack(anchor=tk.W)
        # 选项变化时刷新界面
        self.mode_cb.bind('<<ComboboxSelected>>', lambda _e: self._mode_changed())
        # 变量直接修改（如程序设置）时也刷新
        self.mode_var.trace_add('write', lambda *_: self._mode_changed())

        ttk.Label(left, text='路径/参数').pack(anchor=tk.W, pady=(10,0))
        self.path_var = tk.StringVar()
        self.path_entry = ttk.Entry(left, textvariable=self.path_var, width=25)
        self.path_entry.pack(anchor=tk.W)
        self.device_var = tk.StringVar()
        self.device_cb = ttk.Combobox(left, textvariable=self.device_var, state='readonly')
        # 提示标签
        self.tip_lbl = ttk.Label(left, text='（相机模式可选设备）', foreground='gray')
        self.tip_lbl.pack(anchor=tk.W)
        self.device_cb.pack(anchor=tk.W)
        ttk.Button(left, text='浏览...', command=self._browse).pack(anchor=tk.W, pady=2)

        ttk.Button(left, text='打开', command=self._open).pack(anchor=tk.W, pady=5)
        ttk.Button(left, text='下一帧', command=self._next).pack(anchor=tk.W, pady=2)
        ttk.Button(left, text='拍照保存', command=self._snap).pack(anchor=tk.W, pady=2)
        ttk.Button(left, text='关闭', command=self._close_src).pack(anchor=tk.W, pady=5)

        ttk.Label(left, text='状态:').pack(anchor=tk.W, pady=(10,0))
        self.stat_var = tk.StringVar(value='未打开')
        ttk.Label(left, textvariable=self.stat_var, wraplength=150).pack(anchor=tk.W)

        # 预览
        self.canvas = tk.Canvas(right, width=PREVIEW, height=PREVIEW, bg='gray')
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 根据默认模式初始化控件可见性
        self._mode_changed()

    # ---------------- callbacks -----------------
    def _mode_changed(self):
        mode = self.mode_var.get()
        if mode == '相机':
            # 隐藏路径输入和浏览按钮
            self.path_entry.forget()
            self.tip_lbl.forget()
            # 刷新设备列表
            self._refresh_devices()
            self.device_cb.pack(anchor=tk.W)
        else:
            # 显示路径输入
            self.device_cb.forget()
            self.path_entry.pack(anchor=tk.W)
            self.tip_lbl.pack(anchor=tk.W)

    def _refresh_devices(self):
        try:
            infos = CameraHikvision.enum_devices()
            values = []
            self._label_to_idx = {}
            if not infos:
                values.append('无设备')
            else:
                for i, d in enumerate(infos):
                    label = f"cam{i+1:02d} {d.get('ip','')}"
                    values.append(label)
                    self._label_to_idx[label] = i
            self.device_cb['values'] = values
            self.device_var.set(values[0] if values else '')
        except Exception as e:
            self.device_cb['values'] = ['枚举失败']
            self.device_var.set('枚举失败')
            print('枚举相机失败', e)

    def _browse(self):
        mode = self.mode_var.get()
        if mode == '文件夹':
            path = filedialog.askdirectory()
        elif mode == '单图':
            path = filedialog.askopenfilename(filetypes=[('Image','*.jpg;*.png;*.bmp')])
        else:
            return
        if path:
            self.path_var.set(path)

    def _open(self):
        self._close_src()
        mode_key = self._mode_map[self.mode_var.get()]
        if mode_key == 'camera':
            sel = self.device_var.get()
            idx = self._label_to_idx.get(sel, 0)
            path = str(idx)
        else:
            path = self.path_var.get().strip() or None
        try:
            self._src = create_source(mode_key, path)
            if not self._src.open():
                err = getattr(self._src, 'last_error', None) or '打开源失败'
                raise RuntimeError(err)
            self.stat_var.set('已打开')
            # 相机模式启动自动刷新
            if mode_key == 'camera':
                self._running = True
                self.after(10, self._auto_next)
            else:
                self._running = False
            self._next()
        except Exception as e:
            messagebox.showerror('错误', str(e))
            self.stat_var.set('打开失败')
            self._src = None
            # 清空画布
            self.canvas.delete('all')
            self._photo = None

    def _auto_next(self):
        if not self._running:
            return
        self._next()
        # 30fps ~33ms
        self.after(33, self._auto_next)

    def _next(self):
        if not self._src:
            return
        img = self._src.next_frame()
        # 相机可能首次返回 None，尝试多抓几次
        if img is None:
            from plugins.core.image_process.image_source import CameraSource
            if isinstance(self._src, CameraSource):
                for _ in range(3):
                    img = self._src.next_frame()
                    if img is not None:
                        break
        if img is None:
            return
        self._show(img)

    def _snap(self):
        if not self._src:
            return
        path = self._src.snapshot()
        if path:
            messagebox.showinfo('保存', f'已保存到 {path}')

    def _close_src(self):
        if self._src:
            self._src.close()
            self._src = None
            self.stat_var.set('已关闭')
            self._running = False
            # 清空画布
            self.canvas.delete('all')
            self._photo = None

    def _on_close(self):
        # 先关闭资源再销毁窗口
        self._close_src()
        self.destroy()
        # 若父级是隐藏的 root，则一并退出主循环
        if isinstance(self.master, tk.Tk):
            # 若没有其他顶层窗口则销毁 root
            if not self.master.winfo_children():
                self.master.destroy()

    # ---------------- helper -----------------
    def _show(self, img):
        h, w = img.shape[:2]
        scale = min(PREVIEW/w, PREVIEW/h, 1.0)
        import cv2
        img_rs = cv2.resize(img, (int(w*scale), int(h*scale)))
        img_rgb = cv2.cvtColor(img_rs, cv2.COLOR_BGR2RGB)
        from PIL import Image, ImageTk
        self._photo = ImageTk.PhotoImage(Image.fromarray(img_rgb))
        self.canvas.delete('all')
        self.canvas.create_image(PREVIEW//2, PREVIEW//2, image=self._photo)

# -------- standalone test --------
if __name__ == '__main__':
    root = tk.Tk(); root.withdraw()
    ImageSourceUI(root)
    root.mainloop()
