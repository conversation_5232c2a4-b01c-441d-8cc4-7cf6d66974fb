"""Arc 工具插件包装.
调用 geometry_tools.arc_tool.measure_arc
"""
from __future__ import annotations
from typing import Any, Dict
import cv2
import numpy as np

from plugins.plugin_base import PluginBase
from plugins.geometry_tools.arc_tool import measure_arc

class ArcToolPlugin(PluginBase):
    name = "arc_tool"
    label = "弧度测量"
    category = "几何测量"

    params: Dict[str, Any] = {
        "cx": 0.0,
        "cy": 0.0,
        "r_est": 50.0,
        "debug_draw": True,
        "canny_th1": 50,
        "canny_th2": 150,
        # ROI 稳定相关
        "smooth_alpha": 0.2,   # ROI 指数平滑系数，0 表示关闭
        "score_th": 0.8,       # template_match 分数低于此阈值则沿用上一帧 ROI
    }

    param_labels = {
        "cx": "中心 X",
        "cy": "中心 Y",
        "r_est": "估计半径",
        "canny_th1": "Canny阈值1",
        "canny_th2": "Canny阈值2",
        "smooth_alpha": "ROI平滑α",
        "score_th": "ROI更新阈值"
    }

    def process(self, img, ctx):
        """支持单 ROI 与多 ROI 两种模式。"""
        if img is None:
            return img, ctx

        rois: list[dict] | None = None
        if isinstance(self.params.get("rois"), list) and self.params["rois"]:
            rois = self.params["rois"]  # 多 ROI 模式
        else:
            # 兼容旧版单 ROI 参数
            rois = [{"cx": self.cx, "cy": self.cy, "r": self.r_est, "id": 0}]

        # ---- 根据 template_match 分数判断是否沿用上一帧 ROI ----
        tm_score = None
        if isinstance(ctx.get("template_match"), dict):
            tm_score = ctx["template_match"].get("score")
        score_th = self.params.get("score_th", 0.8)
        if tm_score is not None and tm_score < score_th and getattr(self, "_last_rois", None):
            # 匹配不可靠，使用上一帧 ROI
            rois = [dict(r) for r in self._last_rois]

        # ---- 坐标自适应：根据上游定位结果将 ROI 坐标映射到当前图像 ----
        import numpy as _np, logging
        _log = logging.getLogger("ArcTool")
        H = None  # 3×3 单应矩阵
        pose = None  # {'angle': deg, 'center': (x,y)}

        # 优先使用 homography（通用）
        if isinstance(ctx.get("homography"), (list, tuple, _np.ndarray)):
            mat = _np.asarray(ctx["homography"], dtype=float)
            if mat.shape == (3, 3):
                H = mat

        # 次选读取 pose（来自 rect_tool 或其他定位插件）
        if pose is None and isinstance(ctx.get("rect_tool"), dict):
            pose = ctx["rect_tool"].get("pose")
        if pose is None and isinstance(ctx.get("pose"), dict):
            pose = ctx.get("pose")
        # 最后尝试从参数中获取（例如流程编辑器离线调试时写在 params 内）
        if pose is None and isinstance(self.params.get("pose"), dict):
            pose = self.params["pose"]

        def _apply_transform(x: float, y: float):
            if H is not None:
                v = _np.array([x, y, 1.0])
                x2, y2, w = H @ v
                return float(x2 / w), float(y2 / w)
            if pose and "angle" in pose and "center" in pose:
                ang = _np.deg2rad(-pose["angle"])
                cx0, cy0 = pose["center"]
                dx, dy = x - cx0, y - cy0
                cos_a, sin_a = _np.cos(ang), _np.sin(ang)
                x2 = dx * cos_a - dy * sin_a + cx0
                y2 = dx * sin_a + dy * cos_a + cy0
                return float(x2), float(y2)
            return x, y

        if H is not None or pose is not None:
            _log.debug("Apply transform: H is %s, pose is %s", H is not None, bool(pose))
            for roi in rois:
                before = (roi.get("cx",0), roi.get("cy",0))
                roi["cx"], roi["cy"] = _apply_transform(*before)
                _log.debug("ROI %s pos %s -> %s", roi.get("id"), before, (roi["cx"], roi["cy"]))

        # ---- ROI 指数平滑 ----
        alpha = self.params.get("smooth_alpha", 0.0)
        if alpha and getattr(self, "_last_rois", None):
            for roi in rois:
                last = next((r for r in self._last_rois if r.get("id") == roi.get("id")), None)
                if last:
                    roi["cx"] = (1 - alpha) * last["cx"] + alpha * roi["cx"]
                    roi["cy"] = (1 - alpha) * last["cy"] + alpha * roi["cy"]

        def _run_detect(rois_to_use):
            results = []
            import cv2
            for rroi in rois_to_use:
                res = measure_arc(
                    img,
                    rroi.get("cx", 0),
                    rroi.get("cy", 0),
                    rroi.get("r", self.r_est),
                    canny_th1=self.canny_th1,
                    canny_th2=self.canny_th2,
                )
                res["id"] = rroi.get("id")
                _log.debug("Detect ROI %s result: %s", rroi.get("id"), res)
                results.append(res)
            return results

        # 第一次用映射后的 ROI
        all_results: list[dict] = _run_detect(rois)
        # 如全部结果都没有 center，则回退使用原始 ROI（未映射）再测一次
        if not any("center" in r for r in all_results):
            all_results = _run_detect(self.params.get("rois", rois))
        mm_val = self.params.get("mm_per_px") or ctx.get("mm_per_px")

        # ---- 可视化 ----
        import cv2  # 延迟导入以加速启动
        if bool(self.params.get("debug_draw", True)):
            try:
                for res in all_results:
                    if "center" in res and "radius" in res:
                        center = tuple(map(int, map(round, res["center"])))
                        cv2.circle(img, center, int(round(res["radius"])), (0, 255, 0), 1)
                        txt = (
                            f"R:{res['radius'] * mm_val:.3f}mm"
                            if mm_val else f"R:{res['radius']:.1f}px"
                        )
                        cv2.putText(img, txt, (center[0]+5, center[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255, 0), 1, cv2.LINE_AA)
            except Exception as _e:
                print(f"[ArcTool] debug draw failed: {_e}")

        # 将结果写回 ctx
        ctx[self.name] = all_results if len(all_results) > 1 else all_results[0]
        # 为下游步骤提供简单别名（第一个 ROI）
        if all_results and "center" in all_results[0]:
            ctx["arc_center"] = tuple(all_results[0]["center"])
            ctx["radius_px"] = float(all_results[0].get("radius", 0))
            if mm_val:
                ctx["radius_mm"] = ctx["radius_px"] * mm_val

        # 保存本帧 ROI 供下一帧使用
        try:
            self._last_rois = [dict(r) for r in rois]
        except Exception:
            pass

        return img, ctx

    def __init__(self, **kwargs):
        # 将 YAML 中传入的参数合并进 self.params，保证后续 self.params.get('rois') 等取值正确
        merged = {**self.params, **kwargs}
        # 保存上一帧 ROI
        self._last_rois: list[dict] | None = None
        # 保存回实例级属性
        self.params = merged
        # 同时把每个字段设置为属性，便于直接使用 self.cx 等成员
        for k, v in merged.items():
            setattr(self, k, v)

    # ---------------- 专属参数界面 -----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: np.ndarray | None = None):
        """打开完整交互 UI（plugins.ui.geometry_tools.arc_tool_ui.ArcToolUI）。"""
        import importlib
        mod = importlib.import_module('plugins.ui.geometry_tools.arc_tool_ui')
        ui_cls = getattr(mod, 'ArcToolUI', None)
        if ui_cls is None:
            from tkinter import messagebox
            messagebox.showerror('错误', '未找到 ArcToolUI')
            return
        recipe_path = None
        if isinstance(params, dict):
            recipe_path = params.get('_recipe_path')
        # 若未传入，则尝试从编辑器主窗口获取（RecipeEditor -> PipelineEditor -> Plugin）
        if recipe_path is None:
            # PipelineEditor 直接持有当前 workstation pipeline 路径
            recipe_path = getattr(master, 'pipeline_path', None)
            if recipe_path is None and hasattr(master, 'master'):
                # 从 RecipeEditor 再取主配方路径（次优）
                parent = master.master
                recipe_path = getattr(parent, 'recipe_path', None) if parent else None
        if preview_img is not None:
            ui_cls(master, img=preview_img, recipe_path=recipe_path)
        else:
            ui_cls(master, recipe_path=recipe_path)  # 界面自身负责交互与保存，不直接修改流程参数


_ = ArcToolPlugin()
