"""
逻辑判断插件UI界面
提供可视化的条件配置、逻辑表达式编辑和IO输出设置
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from typing import Dict, List, Any


class LogicJudgmentFrame(ttk.Frame):
    """逻辑判断配置界面"""

    def __init__(self, parent, params: Dict[str, Any] = None):
        super().__init__(parent)
        self.params = params or {}

        # 条件列表
        self.conditions = []
        self.io_outputs = []

        # 创建界面
        self._create_widgets()
        self._load_params()

    def _create_widgets(self):
        """创建界面控件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 基本设置页
        self._create_basic_tab(notebook)

        # 判断条件页
        self._create_conditions_tab(notebook)

        # 逻辑表达式页
        self._create_logic_tab(notebook)

        # IO输出页
        self._create_io_tab(notebook)

        # 统计设置页
        self._create_statistics_tab(notebook)

    def _create_basic_tab(self, notebook):
        """创建基本设置页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="基本设置")

        # 启用判断
        self.enable_judgment_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(frame, text="启用逻辑判断",
                       variable=self.enable_judgment_var).pack(anchor=tk.W, pady=5)

        # 判断模式
        ttk.Label(frame, text="判断模式:").pack(anchor=tk.W, pady=(10, 0))
        self.judgment_mode_var = tk.StringVar(value="strict")
        mode_frame = ttk.Frame(frame)
        mode_frame.pack(anchor=tk.W, pady=5)

        ttk.Radiobutton(mode_frame, text="严格模式", variable=self.judgment_mode_var,
                       value="strict").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(mode_frame, text="宽松模式", variable=self.judgment_mode_var,
                       value="tolerant").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(mode_frame, text="自定义", variable=self.judgment_mode_var,
                       value="custom").pack(side=tk.LEFT)

        # 默认结果
        ttk.Label(frame, text="默认结果:").pack(anchor=tk.W, pady=(10, 0))
        self.default_result_var = tk.StringVar(value="UNKNOWN")
        result_combo = ttk.Combobox(frame, textvariable=self.default_result_var,
                                   values=["OK", "NG", "WARN", "ERROR", "UNKNOWN"],
                                   state="readonly", width=15)
        result_combo.pack(anchor=tk.W, pady=5)

        # 报警设置
        alarm_frame = ttk.LabelFrame(frame, text="报警设置", padding=10)
        alarm_frame.pack(fill=tk.X, pady=10)

        ttk.Label(alarm_frame, text="连续NG报警阈值:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.ng_threshold_var = tk.IntVar(value=5)
        ttk.Spinbox(alarm_frame, from_=1, to=100, textvariable=self.ng_threshold_var,
                   width=10).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        ttk.Label(alarm_frame, text="连续WARN报警阈值:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.warn_threshold_var = tk.IntVar(value=10)
        ttk.Spinbox(alarm_frame, from_=1, to=100, textvariable=self.warn_threshold_var,
                   width=10).grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

        # 配置管理按钮 - 放在基本设置页面
        config_frame = ttk.LabelFrame(frame, text="配置管理", padding=10)
        config_frame.pack(fill=tk.X, pady=10)

        config_btn_frame = ttk.Frame(config_frame)
        config_btn_frame.pack(fill=tk.X)

        ttk.Button(config_btn_frame, text="加载配置", command=self._load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(config_btn_frame, text="保存配置", command=self._save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(config_btn_frame, text="重置配置", command=self._reset_config).pack(side=tk.LEFT, padx=5)

    def _create_conditions_tab(self, notebook):
        """创建判断条件页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="判断条件")

        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, pady=5)

        ttk.Button(toolbar, text="添加条件",
                  command=self._add_condition).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="删除条件",
                  command=self._delete_condition).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="编辑条件",
                  command=self._edit_condition).pack(side=tk.LEFT, padx=5)

        # 条件列表
        list_frame = ttk.Frame(frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建Treeview
        columns = ("name", "source", "operator", "target", "enabled")
        self.conditions_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)

        # 设置列标题
        self.conditions_tree.heading("name", text="条件名称")
        self.conditions_tree.heading("source", text="数据源")
        self.conditions_tree.heading("operator", text="操作符")
        self.conditions_tree.heading("target", text="目标值")
        self.conditions_tree.heading("enabled", text="启用")

        # 设置列宽
        self.conditions_tree.column("name", width=120)
        self.conditions_tree.column("source", width=150)
        self.conditions_tree.column("operator", width=80)
        self.conditions_tree.column("target", width=100)
        self.conditions_tree.column("enabled", width=60)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.conditions_tree.yview)
        self.conditions_tree.configure(yscrollcommand=scrollbar.set)

        self.conditions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_logic_tab(self, notebook):
        """创建逻辑表达式页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="逻辑表达式")

        # 说明文本
        info_text = """逻辑表达式说明:
• 使用 C1, C2, C3... 表示条件
• 支持 AND, OR, NOT 逻辑操作符
• 支持括号分组
• 示例: (C1 AND C2) OR C3
• 示例: NOT C1 AND (C2 OR C3)"""

        ttk.Label(frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W, pady=10)

        # 逻辑表达式输入
        ttk.Label(frame, text="逻辑表达式:").pack(anchor=tk.W, pady=(10, 0))
        self.logic_expression_var = tk.StringVar()

        expr_frame = ttk.Frame(frame)
        expr_frame.pack(fill=tk.X, pady=5)

        self.logic_entry = ttk.Entry(expr_frame, textvariable=self.logic_expression_var, font=("Consolas", 10))
        self.logic_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(expr_frame, text="验证", command=self._validate_expression).pack(side=tk.RIGHT, padx=(5, 0))

        # 快速插入按钮
        quick_frame = ttk.LabelFrame(frame, text="快速插入", padding=10)
        quick_frame.pack(fill=tk.X, pady=10)

        btn_frame1 = ttk.Frame(quick_frame)
        btn_frame1.pack(fill=tk.X)

        ttk.Button(btn_frame1, text="AND", command=lambda: self._insert_text(" AND ")).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame1, text="OR", command=lambda: self._insert_text(" OR ")).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame1, text="NOT", command=lambda: self._insert_text("NOT ")).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame1, text="(", command=lambda: self._insert_text("(")).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame1, text=")", command=lambda: self._insert_text(")")).pack(side=tk.LEFT, padx=2)

        btn_frame2 = ttk.Frame(quick_frame)
        btn_frame2.pack(fill=tk.X, pady=(5, 0))

        for i in range(1, 6):
            ttk.Button(btn_frame2, text=f"C{i}",
                      command=lambda x=i: self._insert_text(f"C{x}")).pack(side=tk.LEFT, padx=2)

        # 表达式验证结果
        self.validation_label = ttk.Label(frame, text="", foreground="green")
        self.validation_label.pack(anchor=tk.W, pady=5)

    def _create_io_tab(self, notebook):
        """创建IO输出页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="IO输出")

        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, pady=5)

        ttk.Button(toolbar, text="添加输出",
                  command=self._add_io_output).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="删除输出",
                  command=self._delete_io_output).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="编辑输出",
                  command=self._edit_io_output).pack(side=tk.LEFT, padx=5)

        # IO输出列表
        list_frame = ttk.Frame(frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建Treeview
        columns = ("name", "type", "address", "ok_value", "ng_value", "enabled")
        self.io_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=8)

        # 设置列标题
        self.io_tree.heading("name", text="输出名称")
        self.io_tree.heading("type", text="IO类型")
        self.io_tree.heading("address", text="地址")
        self.io_tree.heading("ok_value", text="OK值")
        self.io_tree.heading("ng_value", text="NG值")
        self.io_tree.heading("enabled", text="启用")

        # 设置列宽
        self.io_tree.column("name", width=100)
        self.io_tree.column("type", width=80)
        self.io_tree.column("address", width=100)
        self.io_tree.column("ok_value", width=80)
        self.io_tree.column("ng_value", width=80)
        self.io_tree.column("enabled", width=60)

        # 添加滚动条
        scrollbar2 = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.io_tree.yview)
        self.io_tree.configure(yscrollcommand=scrollbar2.set)

        self.io_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar2.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_statistics_tab(self, notebook):
        """创建统计设置页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="统计设置")

        # 启用统计
        self.enable_statistics_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(frame, text="启用统计功能",
                       variable=self.enable_statistics_var).pack(anchor=tk.W, pady=5)

        # 统计窗口大小
        ttk.Label(frame, text="统计窗口大小:").pack(anchor=tk.W, pady=(10, 0))
        self.statistics_window_var = tk.IntVar(value=100)
        ttk.Spinbox(frame, from_=10, to=1000, textvariable=self.statistics_window_var,
                   width=15).pack(anchor=tk.W, pady=5)

        # 日志设置
        log_frame = ttk.LabelFrame(frame, text="日志设置", padding=10)
        log_frame.pack(fill=tk.X, pady=10)

        self.enable_logging_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_frame, text="启用日志记录",
                       variable=self.enable_logging_var).pack(anchor=tk.W, pady=2)

        ttk.Label(log_frame, text="日志级别:").pack(anchor=tk.W, pady=(5, 0))
        self.log_level_var = tk.StringVar(value="INFO")
        log_combo = ttk.Combobox(log_frame, textvariable=self.log_level_var,
                                values=["DEBUG", "INFO", "WARN", "ERROR"],
                                state="readonly", width=15)
        log_combo.pack(anchor=tk.W, pady=2)

    def _load_params(self):
        """加载参数到界面"""
        # 基本设置
        self.enable_judgment_var.set(self.params.get("enable_judgment", True))
        self.judgment_mode_var.set(self.params.get("judgment_mode", "strict"))
        self.default_result_var.set(self.params.get("default_result", "UNKNOWN"))
        self.ng_threshold_var.set(self.params.get("ng_threshold", 5))
        self.warn_threshold_var.set(self.params.get("warn_threshold", 10))

        # 逻辑表达式
        self.logic_expression_var.set(self.params.get("logic_expression", ""))

        # 统计设置
        self.enable_statistics_var.set(self.params.get("enable_statistics", True))
        self.statistics_window_var.set(self.params.get("statistics_window", 100))
        self.enable_logging_var.set(self.params.get("enable_logging", True))
        self.log_level_var.set(self.params.get("log_level", "INFO"))

        # 加载条件列表
        try:
            conditions_json = self.params.get("conditions", "[]")
            if isinstance(conditions_json, str):
                self.conditions = json.loads(conditions_json)
            else:
                self.conditions = conditions_json
            self._refresh_conditions_tree()
        except Exception as e:
            print(f"加载条件列表失败: {e}")
            self.conditions = []

        # 加载IO输出列表
        try:
            io_outputs_json = self.params.get("io_outputs", "[]")
            if isinstance(io_outputs_json, str):
                self.io_outputs = json.loads(io_outputs_json)
            else:
                self.io_outputs = io_outputs_json
            self._refresh_io_tree()
        except Exception as e:
            print(f"加载IO输出列表失败: {e}")
            self.io_outputs = []

    def get_params(self) -> Dict[str, Any]:
        """获取界面参数"""
        return {
            # 基本设置
            "enable_judgment": self.enable_judgment_var.get(),
            "judgment_mode": self.judgment_mode_var.get(),
            "default_result": self.default_result_var.get(),
            "ng_threshold": self.ng_threshold_var.get(),
            "warn_threshold": self.warn_threshold_var.get(),

            # 逻辑表达式
            "logic_expression": self.logic_expression_var.get(),

            # 条件和IO输出（JSON格式）
            "conditions": json.dumps(self.conditions, ensure_ascii=False),
            "io_outputs": json.dumps(self.io_outputs, ensure_ascii=False),

            # 统计设置
            "enable_statistics": self.enable_statistics_var.get(),
            "statistics_window": self.statistics_window_var.get(),
            "enable_logging": self.enable_logging_var.get(),
            "log_level": self.log_level_var.get(),
        }

    # -------------------- 条件管理 --------------------
    def _add_condition(self):
        """添加判断条件"""
        dialog = ConditionEditDialog(self, title="添加判断条件")
        if dialog.result:
            self.conditions.append(dialog.result)
            self._refresh_conditions_tree()

    def _edit_condition(self):
        """编辑判断条件"""
        selection = self.conditions_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要编辑的条件")
            return

        index = int(selection[0]) - 1
        if 0 <= index < len(self.conditions):
            dialog = ConditionEditDialog(self, title="编辑判断条件",
                                       condition=self.conditions[index])
            if dialog.result:
                self.conditions[index] = dialog.result
                self._refresh_conditions_tree()

    def _delete_condition(self):
        """删除判断条件"""
        selection = self.conditions_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要删除的条件")
            return

        if messagebox.askyesno("确认", "确定要删除选中的条件吗？"):
            index = int(selection[0]) - 1
            if 0 <= index < len(self.conditions):
                del self.conditions[index]
                self._refresh_conditions_tree()

    def _refresh_conditions_tree(self):
        """刷新条件列表"""
        # 清空现有项目
        for item in self.conditions_tree.get_children():
            self.conditions_tree.delete(item)

        # 添加条件项目
        for i, condition in enumerate(self.conditions):
            self.conditions_tree.insert("", "end", iid=str(i+1), values=(
                condition.get("name", ""),
                f"{condition.get('source_plugin', '')}.{condition.get('source_key', '')}",
                condition.get("operator", ""),
                str(condition.get("target_value", "")),
                "是" if condition.get("enabled", True) else "否"
            ))

    # -------------------- IO输出管理 --------------------
    def _add_io_output(self):
        """添加IO输出"""
        dialog = IOOutputEditDialog(self, title="添加IO输出")
        if dialog.result:
            self.io_outputs.append(dialog.result)
            self._refresh_io_tree()

    def _edit_io_output(self):
        """编辑IO输出"""
        selection = self.io_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要编辑的IO输出")
            return

        index = int(selection[0]) - 1
        if 0 <= index < len(self.io_outputs):
            dialog = IOOutputEditDialog(self, title="编辑IO输出",
                                      io_output=self.io_outputs[index])
            if dialog.result:
                self.io_outputs[index] = dialog.result
                self._refresh_io_tree()

    def _delete_io_output(self):
        """删除IO输出"""
        selection = self.io_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要删除的IO输出")
            return

        if messagebox.askyesno("确认", "确定要删除选中的IO输出吗？"):
            index = int(selection[0]) - 1
            if 0 <= index < len(self.io_outputs):
                del self.io_outputs[index]
                self._refresh_io_tree()

    def _refresh_io_tree(self):
        """刷新IO输出列表"""
        # 清空现有项目
        for item in self.io_tree.get_children():
            self.io_tree.delete(item)

        # 添加IO输出项目
        for i, io_output in enumerate(self.io_outputs):
            self.io_tree.insert("", "end", iid=str(i+1), values=(
                io_output.get("name", ""),
                io_output.get("io_type", ""),
                io_output.get("address", ""),
                str(io_output.get("ok_value", "")),
                str(io_output.get("ng_value", "")),
                "是" if io_output.get("enabled", True) else "否"
            ))

    # -------------------- 逻辑表达式 --------------------
    def _insert_text(self, text: str):
        """插入文本到逻辑表达式"""
        current_pos = self.logic_entry.index(tk.INSERT)
        self.logic_entry.insert(current_pos, text)
        self.logic_entry.focus_set()

    def _validate_expression(self):
        """验证逻辑表达式"""
        expression = self.logic_expression_var.get().strip()
        if not expression:
            self.validation_label.config(text="表达式为空", foreground="orange")
            return

        try:
            # 简单的语法检查
            # 检查括号匹配
            if expression.count("(") != expression.count(")"):
                raise ValueError("括号不匹配")

            # 检查是否包含有效的条件引用
            import re
            conditions_found = re.findall(r'C\d+', expression)
            if not conditions_found:
                raise ValueError("未找到有效的条件引用 (C1, C2, ...)")

            # 检查条件编号是否超出范围
            max_condition = len(self.conditions)
            for cond in conditions_found:
                cond_num = int(cond[1:])
                if cond_num > max_condition:
                    raise ValueError(f"条件 {cond} 超出范围 (最大: C{max_condition})")

            self.validation_label.config(text="✓ 表达式语法正确", foreground="green")

        except Exception as e:
            self.validation_label.config(text=f"✗ 语法错误: {str(e)}", foreground="red")

    # -------------------- 配置管理 --------------------
    def _load_config(self):
        """加载配置文件"""
        from tkinter import filedialog
        from pathlib import Path
        import yaml

        # 默认配置目录
        cfg_dir = Path(__file__).resolve().parents[2] / 'configs' / 'logic_judgment'
        cfg_dir.mkdir(parents=True, exist_ok=True)

        file_path = filedialog.askopenfilename(
            title="加载逻辑判断配置",
            defaultextension=".yml",
            initialdir=str(cfg_dir),
            filetypes=[("YAML文件", "*.yml"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)

                if config_data:
                    # 更新界面参数
                    self._load_config_data(config_data)
                    messagebox.showinfo("成功", f"配置已从文件加载: {Path(file_path).name}")
                else:
                    messagebox.showwarning("警告", "配置文件为空")

            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def _save_config(self):
        """保存配置文件"""
        from tkinter import filedialog
        from pathlib import Path
        import yaml

        # 默认配置目录
        cfg_dir = Path(__file__).resolve().parents[2] / 'configs' / 'logic_judgment'
        cfg_dir.mkdir(parents=True, exist_ok=True)

        file_path = filedialog.asksaveasfilename(
            title="保存逻辑判断配置",
            defaultextension=".yml",
            initialdir=str(cfg_dir),
            initialfile="default.yml",
            filetypes=[("YAML文件", "*.yml"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                config_data = self.get_params()
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.safe_dump(config_data, f, default_flow_style=False, allow_unicode=True)
                messagebox.showinfo("成功", f"配置已保存到: {Path(file_path).name}")

            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def _reset_config(self):
        """重置配置为默认值"""
        if messagebox.askyesno("确认", "确定要重置所有配置为默认值吗？"):
            # 重置为默认值
            self.enable_judgment_var.set(True)
            self.judgment_mode_var.set("strict")
            self.default_result_var.set("UNKNOWN")
            self.ng_threshold_var.set(5)
            self.warn_threshold_var.set(10)
            self.logic_expression_var.set("")
            self.enable_statistics_var.set(True)
            self.statistics_window_var.set(100)
            self.enable_logging_var.set(True)
            self.log_level_var.set("INFO")

            # 清空条件和IO输出
            self.conditions = []
            self.io_outputs = []
            self._refresh_conditions_tree()
            self._refresh_io_tree()

            messagebox.showinfo("完成", "配置已重置为默认值")

    def _load_config_data(self, config_data: dict):
        """从配置数据加载到界面"""
        try:
            # 基本设置
            self.enable_judgment_var.set(config_data.get("enable_judgment", True))
            self.judgment_mode_var.set(config_data.get("judgment_mode", "strict"))
            self.default_result_var.set(config_data.get("default_result", "UNKNOWN"))
            self.ng_threshold_var.set(config_data.get("ng_threshold", 5))
            self.warn_threshold_var.set(config_data.get("warn_threshold", 10))

            # 逻辑表达式
            self.logic_expression_var.set(config_data.get("logic_expression", ""))

            # 统计设置
            self.enable_statistics_var.set(config_data.get("enable_statistics", True))
            self.statistics_window_var.set(config_data.get("statistics_window", 100))
            self.enable_logging_var.set(config_data.get("enable_logging", True))
            self.log_level_var.set(config_data.get("log_level", "INFO"))

            # 条件列表
            conditions_data = config_data.get("conditions", [])
            if isinstance(conditions_data, str):
                conditions_data = json.loads(conditions_data)
            self.conditions = conditions_data
            self._refresh_conditions_tree()

            # IO输出列表
            io_outputs_data = config_data.get("io_outputs", [])
            if isinstance(io_outputs_data, str):
                io_outputs_data = json.loads(io_outputs_data)
            self.io_outputs = io_outputs_data
            self._refresh_io_tree()

        except Exception as e:
            print(f"加载配置数据失败: {e}")
            messagebox.showerror("错误", f"加载配置数据失败: {str(e)}")


class ConditionEditDialog:
    """条件编辑对话框"""

    def __init__(self, parent, title="编辑条件", condition=None):
        self.result = None

        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x350")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (350 // 2)
        self.dialog.geometry(f"400x350+{x}+{y}")

        self._create_widgets(condition)

        # 等待对话框关闭
        self.dialog.wait_window()

    def _create_widgets(self, condition):
        """创建对话框控件"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 条件名称
        ttk.Label(main_frame, text="条件名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar(value=condition.get("name", "") if condition else "")
        ttk.Entry(main_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        # 数据源插件
        ttk.Label(main_frame, text="数据源插件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.source_plugin_var = tk.StringVar(value=condition.get("source_plugin", "") if condition else "")
        plugin_combo = ttk.Combobox(main_frame, textvariable=self.source_plugin_var, width=27)

        # 完整的插件列表（中英文对照）
        plugin_options = [
            "yolo (YOLO检测)",
            "morphology (形态学)",
            "contour_detection (轮廓检测)",
            "contour_analysis (轮廓分析)",
            "geometry_measurement (几何测量)",
            "integrated_geometry_tool (集成几何测量)",
            "template_matching (模板匹配)",
            "blob_analysis (斑点分析)",
            "mm_per_px_calib (像素标定)",
            "logic_judgment (逻辑判断)"
        ]
        plugin_combo['values'] = plugin_options
        plugin_combo.grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

        # 插件选择变化时更新数据源键名选项
        plugin_combo.bind('<<ComboboxSelected>>', self._on_plugin_change)

        # 数据源键名
        ttk.Label(main_frame, text="数据源键名:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.source_key_var = tk.StringVar(value=condition.get("source_key", "") if condition else "")
        self.key_combo = ttk.Combobox(main_frame, textvariable=self.source_key_var, width=27)
        self.key_combo.grid(row=2, column=1, sticky=tk.W, padx=(5, 0))

        # 初始化数据源键名选项
        self._update_key_options()

        # 比较操作符
        ttk.Label(main_frame, text="比较操作符:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.operator_var = tk.StringVar(value=condition.get("operator", "==") if condition else "==")
        operator_combo = ttk.Combobox(main_frame, textvariable=self.operator_var, width=27, state="readonly")
        operator_combo['values'] = ["==", "!=", ">", ">=", "<", "<=", "IN_RANGE", "OUT_RANGE"]
        operator_combo.grid(row=3, column=1, sticky=tk.W, padx=(5, 0))

        # 目标值
        ttk.Label(main_frame, text="目标值:").grid(row=4, column=0, sticky=tk.W, pady=5)
        target_value = condition.get("target_value", "") if condition else ""
        if isinstance(target_value, list):
            target_value = ",".join(map(str, target_value))
        self.target_value_var = tk.StringVar(value=str(target_value))
        ttk.Entry(main_frame, textvariable=self.target_value_var, width=30).grid(row=4, column=1, sticky=tk.W, padx=(5, 0))

        # 目标值说明
        help_text = "数值: 123.45\n范围: 10,20 (用逗号分隔)\n字符串: OK"
        ttk.Label(main_frame, text=help_text, font=("Arial", 8), foreground="gray").grid(row=5, column=1, sticky=tk.W, padx=(5, 0))

        # 权重
        ttk.Label(main_frame, text="权重:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.weight_var = tk.DoubleVar(value=condition.get("weight", 1.0) if condition else 1.0)
        ttk.Spinbox(main_frame, from_=0.1, to=10.0, increment=0.1, textvariable=self.weight_var, width=28).grid(row=6, column=1, sticky=tk.W, padx=(5, 0))

        # 启用状态
        self.enabled_var = tk.BooleanVar(value=condition.get("enabled", True) if condition else True)
        ttk.Checkbutton(main_frame, text="启用此条件", variable=self.enabled_var).grid(row=7, column=1, sticky=tk.W, pady=10, padx=(5, 0))

        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=8, column=0, columnspan=2, pady=20)

        ttk.Button(btn_frame, text="确定", command=self._on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=5)

    def _on_ok(self):
        """确定按钮事件"""
        # 验证输入
        if not self.name_var.get().strip():
            messagebox.showerror("错误", "请输入条件名称")
            return

        # 提取插件名（去掉中文说明）
        plugin_text = self.source_plugin_var.get().strip()
        if not plugin_text:
            messagebox.showerror("错误", "请选择数据源插件")
            return

        if " (" in plugin_text:
            plugin_name = plugin_text.split(" (")[0]
        else:
            plugin_name = plugin_text

        # 提取键名（去掉中文说明）
        key_text = self.source_key_var.get().strip()
        if not key_text:
            messagebox.showerror("错误", "请输入数据源键名")
            return

        if " (" in key_text:
            key_name = key_text.split(" (")[0]
        else:
            key_name = key_text

        # 解析目标值
        target_value_str = self.target_value_var.get().strip()
        try:
            if self.operator_var.get() in ["IN_RANGE", "OUT_RANGE"]:
                # 范围值，解析为列表
                if "," in target_value_str:
                    target_value = [float(x.strip()) for x in target_value_str.split(",")]
                    if len(target_value) != 2:
                        raise ValueError("范围值必须包含两个数值")
                else:
                    raise ValueError("范围值格式错误，请用逗号分隔两个数值")
            else:
                # 尝试解析为数值
                try:
                    target_value = float(target_value_str)
                except ValueError:
                    # 作为字符串处理
                    target_value = target_value_str
        except Exception as e:
            messagebox.showerror("错误", f"目标值格式错误: {str(e)}")
            return

        # 构建结果
        self.result = {
            "name": self.name_var.get().strip(),
            "source_plugin": plugin_name,  # 使用提取的插件名
            "source_key": key_name,        # 使用提取的键名
            "operator": self.operator_var.get(),
            "target_value": target_value,
            "weight": self.weight_var.get(),
            "enabled": self.enabled_var.get(),
        }

        self.dialog.destroy()

    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()

    def _on_plugin_change(self, event=None):
        """插件选择变化时更新数据源键名选项"""
        self._update_key_options()

    def _update_key_options(self):
        """根据选择的插件更新数据源键名选项"""
        plugin_text = self.source_plugin_var.get()

        # 提取插件名（去掉中文说明）
        if " (" in plugin_text:
            plugin_name = plugin_text.split(" (")[0]
        else:
            plugin_name = plugin_text

        # 根据插件类型提供相应的数据键名选项
        key_options = self._get_plugin_data_keys(plugin_name)
        self.key_combo['values'] = key_options

    def _get_plugin_data_keys(self, plugin_name: str) -> list:
        """获取指定插件的数据键名列表"""
        plugin_data_keys = {
            "yolo": [
                "total_count (检测总数)",
                "ng_count (NG数量)",
                "ok_count (OK数量)",
                "total_area_mm2 (总面积mm²)",
                "detections (检测结果列表)",
                "roi_used (是否使用ROI)",
                "roi_matched (ROI匹配状态)"
            ],
            "morphology": [
                "output (输出图像)",
                "area (处理区域面积)",
                "iter (迭代次数)",
                "kernel_size (核大小)",
                "ops_seq (操作序列)"
            ],
            "contour_detection": [
                "stats (轮廓统计)",
                "status (处理状态)",
                "elapsed_time (处理时间)",
                "contours (轮廓列表)",
                "contour_count (轮廓数量)"
            ],
            "contour_analysis": [
                "stats (轮廓统计)",
                "contours (轮廓列表)",
                "area (轮廓面积)",
                "perim (轮廓周长)",
                "roundness (圆度)",
                "center (中心点)",
                "min_rect (最小外接矩形)"
            ],
            "geometry_measurement": [
                "length_mm (长度mm)",
                "width_mm (宽度mm)",
                "angle_deg (角度°)",
                "area_mm2 (面积mm²)",
                "center_x (中心X坐标)",
                "center_y (中心Y坐标)",
                "measurement_type (测量类型)"
            ],
            "integrated_geometry_tool": [
                "measurement_result (测量结果)",
                "length_mm (长度mm)",
                "width_mm (宽度mm)",
                "angle_deg (角度°)",
                "area_mm2 (面积mm²)",
                "measurement_type (测量类型)",
                "confidence (置信度)"
            ],
            "template_matching": [
                "match_score (匹配度)",
                "match_location (匹配位置)",
                "template_found (模板找到)",
                "confidence (置信度)",
                "center_x (中心X)",
                "center_y (中心Y)"
            ],
            "blob_analysis": [
                "blob_count (斑点数量)",
                "total_area (总面积)",
                "avg_area (平均面积)",
                "blob_stats (斑点统计)",
                "largest_blob (最大斑点)",
                "smallest_blob (最小斑点)"
            ],
            "mm_per_px_calib": [
                "mm_per_px (像素比例)",
                "calibration_method (标定方法)",
                "confidence (置信度)",
                "processing_time (处理时间)"
            ],
            "logic_judgment": [
                "result (判断结果)",
                "statistics (统计信息)",
                "processing_time (处理时间)",
                "conditions_evaluated (评估条件数)"
            ]
        }

        return plugin_data_keys.get(plugin_name, [
            "请选择具体的数据键名",
            "或手动输入自定义键名"
        ])


class IOOutputEditDialog:
    """IO输出编辑对话框"""

    def __init__(self, parent, title="编辑IO输出", io_output=None):
        self.result = None

        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")

        self._create_widgets(io_output)

        # 等待对话框关闭
        self.dialog.wait_window()

    def _create_widgets(self, io_output):
        """创建对话框控件"""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 输出名称
        ttk.Label(main_frame, text="输出名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar(value=io_output.get("name", "") if io_output else "")
        ttk.Entry(main_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        # IO类型
        ttk.Label(main_frame, text="IO类型:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.io_type_var = tk.StringVar(value=io_output.get("io_type", "digital") if io_output else "digital")
        type_combo = ttk.Combobox(main_frame, textvariable=self.io_type_var, width=27, state="readonly")
        type_combo['values'] = ["digital", "analog", "network"]
        type_combo.grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

        # IO地址
        ttk.Label(main_frame, text="IO地址:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.address_var = tk.StringVar(value=io_output.get("address", "") if io_output else "")
        ttk.Entry(main_frame, textvariable=self.address_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(5, 0))

        # OK值
        ttk.Label(main_frame, text="OK状态值:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.ok_value_var = tk.StringVar(value=str(io_output.get("ok_value", "1")) if io_output else "1")
        ttk.Entry(main_frame, textvariable=self.ok_value_var, width=30).grid(row=3, column=1, sticky=tk.W, padx=(5, 0))

        # NG值
        ttk.Label(main_frame, text="NG状态值:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.ng_value_var = tk.StringVar(value=str(io_output.get("ng_value", "0")) if io_output else "0")
        ttk.Entry(main_frame, textvariable=self.ng_value_var, width=30).grid(row=4, column=1, sticky=tk.W, padx=(5, 0))

        # WARN值
        ttk.Label(main_frame, text="WARN状态值:").grid(row=5, column=0, sticky=tk.W, pady=5)
        warn_value = io_output.get("warn_value", "") if io_output else ""
        self.warn_value_var = tk.StringVar(value=str(warn_value) if warn_value is not None else "")
        ttk.Entry(main_frame, textvariable=self.warn_value_var, width=30).grid(row=5, column=1, sticky=tk.W, padx=(5, 0))

        # 启用状态
        self.enabled_var = tk.BooleanVar(value=io_output.get("enabled", True) if io_output else True)
        ttk.Checkbutton(main_frame, text="启用此输出", variable=self.enabled_var).grid(row=6, column=1, sticky=tk.W, pady=10, padx=(5, 0))

        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=7, column=0, columnspan=2, pady=20)

        ttk.Button(btn_frame, text="确定", command=self._on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=5)

    def _on_ok(self):
        """确定按钮事件"""
        # 验证输入
        if not self.name_var.get().strip():
            messagebox.showerror("错误", "请输入输出名称")
            return

        if not self.address_var.get().strip():
            messagebox.showerror("错误", "请输入IO地址")
            return

        # 解析输出值
        try:
            io_type = self.io_type_var.get()

            if io_type == "digital":
                ok_value = bool(int(self.ok_value_var.get()))
                ng_value = bool(int(self.ng_value_var.get()))
                warn_value = bool(int(self.warn_value_var.get())) if self.warn_value_var.get() else None
            elif io_type == "analog":
                ok_value = float(self.ok_value_var.get())
                ng_value = float(self.ng_value_var.get())
                warn_value = float(self.warn_value_var.get()) if self.warn_value_var.get() else None
            else:  # network
                ok_value = self.ok_value_var.get()
                ng_value = self.ng_value_var.get()
                warn_value = self.warn_value_var.get() if self.warn_value_var.get() else None

        except ValueError as e:
            messagebox.showerror("错误", f"输出值格式错误: {str(e)}")
            return

        # 构建结果
        self.result = {
            "name": self.name_var.get().strip(),
            "io_type": self.io_type_var.get(),
            "address": self.address_var.get().strip(),
            "ok_value": ok_value,
            "ng_value": ng_value,
            "warn_value": warn_value,
            "enabled": self.enabled_var.get(),
        }

        self.dialog.destroy()

    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()