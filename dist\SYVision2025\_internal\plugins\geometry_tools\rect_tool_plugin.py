"""Rect Tool 插件包装

此模块为 **矩形卡尺测量** 工具在 Pipeline Editor 中的插件壳。
仅负责：
1. 向插件系统注册自身（`name = "rect_tool"`）。
2. 暴露持久参数（目前仅 YAML 配置文件路径）。
3. 在需要时懒加载并打开 `RectToolUI` 交互界面。
4. 在流水线运行阶段透传图像（测量逻辑在 UI/测量函数中进行）。
"""
from __future__ import annotations

from typing import Dict, Any, Optional
import importlib

# 插件基础类（由主程序提供）
from plugins.plugin_base import PluginBase


class RectToolPlugin(PluginBase):
    """矩形卡尺测量插件（供 Pipeline Editor 使用）"""

    # 唯一标识（插件注册名）
    name: str = "rect_tool"
    # 在 UI 中显示的中文名称
    label: str = "矩形测量"
    # 分类（用于左侧树形控件分组）
    category: str = "几何测量"

    # ---------------- 持久参数 -----------------
    # 目前仅保存一个 YAML 配置文件路径，供 UI 保存/加载。
    params: Dict[str, Any] = {
        "debug_draw": True,
    }

    # 参数中文标签（用于 PipelineEditor 参数表格）
    param_labels: Dict[str, str] = {}

    # 缓存最近一次 pose，供 UI 调试显示
    _last_pose: Optional[Dict[str, Any]] = None

    # ---------------- 流水线入口 -----------------
    def process(self, img, ctx):  # type: ignore[override]
        pose = ctx.get("pose") or ctx.get("template_match")
        if pose is None:
            return img, ctx
        # 获取模板矩形四角
        corners = pose.get("corners")
        print(f"[RT] received corners: {corners}")
        if corners is None:
            return img, ctx  # pose 无 corners 不处理
        import numpy as _np
        # 缓存供 UI 使用
        RectToolPlugin._last_pose = pose
        pts = _np.array(corners, dtype=float)
        center = pts.mean(axis=0)
        # 计算尺寸(px) 与角度
        edge1 = pts[1] - pts[0]
        edge2 = pts[2] - pts[1]
        w_px = float(_np.linalg.norm(edge1))
        h_px = float(_np.linalg.norm(edge2))
        # 基于第一条边求角度（水平右方向逆时针）
        angle = float(_np.degrees(_np.arctan2(edge1[1], edge1[0])))
        # --- 将尺寸信息写入 ctx，供下游插件（如 size_check）使用 ---
        ctx["width_px"] = w_px
        ctx["height_px"] = h_px
        ctx["rect_width_px"] = w_px  # 冗余别名
        ctx["rect_height_px"] = h_px
        # 如果已有 mm_per_px，则同时提供 mm 尺寸
        mm_val = self.params.get("mm_per_px") or ctx.get("mm_per_px")
        if mm_val:
            ctx["width_mm"] = w_px * mm_val
            ctx["height_mm"] = h_px * mm_val
        img_pts = [tuple(map(float, p)) for p in pts]
        # 保存 ROI + 测量数据
        ctx.setdefault("roi_dict", {})[self.name] = {
            "corners": img_pts,
            "center": tuple(map(float, center)),
            "width_px": w_px,
            "height_px": h_px,
            "angle": angle,
        }
        # 可视化
        if bool(self.params.get("debug_draw", False)) and img is not None:
            import cv2
            pts_arr = _np.round(pts).astype(int).reshape(-1, 1, 2)
            cv2.polylines(img, [pts_arr], isClosed=True, color=(0, 255, 0), thickness=1)
            if mm_val:
                w_mm = w_px * mm_val
                h_mm = h_px * mm_val
                txt = f"W:{w_mm:.3f}mm  H:{h_mm:.3f}mm  ang:{angle:.1f}"
            else:
                txt = f"W:{w_px:.1f}px  H:{h_px:.1f}px  ang:{angle:.1f}"
            cv2.putText(img, txt, (10, 44), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1, cv2.LINE_AA)
        return img, ctx

    # ---------------- 参数对话框 -----------------
    @staticmethod
    def open_param_dialog(
        master,
        params: Dict[str, Any],
        on_change,
        preview_img: Optional[Any] = None,
    ) -> None:
        """懒加载并弹出 `RectToolUI`。

        Parameters
        ----------
        master : tk.Widget
            父窗口，通常由 PipelineEditor 传入。
        params : Dict[str, Any]
            预设参数（当前未使用，但保留接口）。
        on_change : Callable[[Dict[str, Any]], None]
            参数变更回调（暂未使用）。
        preview_img : np.ndarray | None
            上游传递的预览图像，用于 UI 初始显示。
        """
        try:
            # 为了加快主程序启动速度，这里才 import UI 模块
            mod = importlib.import_module("plugins.ui.geometry_tools.rect_tool_ui")
            ui_cls = getattr(mod, "RectToolUI", None)
            if ui_cls is None:
                # 未找到 UI，弹框提示
                from tkinter import messagebox
                messagebox.showerror("错误", "未找到 RectToolUI")
                return

            # 打开交互窗口
            pose = params.get('pose') or RectToolPlugin._last_pose
            if preview_img is not None:
                ui_cls(master, img=preview_img, pose=pose)
            else:
                ui_cls(master, pose=pose)

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("错误", f"打开矩形测量工具失败: {str(e)}")
            import traceback
            traceback.print_exc()


# --------------- 注册到插件系统 ----------------
# 大多数插件系统在扫描模块时会查找同名实例。创建一次实例以完成注册。
_ = RectToolPlugin()
