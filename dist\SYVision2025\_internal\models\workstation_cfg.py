"""Workstation configuration helper.

Each workstation binds a *camera* and a *pipeline YAML*.
Configuration file example::

    workstations:
      - id: ws1
        name: 工位1
        camera_id: cam01
        pipeline: configs/pipelines/ws1.yaml
"""
from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List
import yaml

_CFG_PATH = Path(__file__).resolve().parents[1] / "config" / "workstation_config.yaml"

@dataclass
class Workstation:
    id: str
    name: str = ""
    camera_id: str = ""
    pipeline: str = ""  # YAML path，可为空

    @property
    def pipeline_path(self) -> Path:
        return Path(self.pipeline) if self.pipeline else Path()

# ---------------------------------------------------------------------------
# IO helpers
# ---------------------------------------------------------------------------

def load_workstations(path: str | Path = _CFG_PATH) -> List[Workstation]:
    p = Path(path)
    data_raw = yaml.safe_load(p.read_text(encoding="utf-8")) if p.is_file() else {}
    # 兼容旧格式：整个 YAML 直接是一组工位列表
    if isinstance(data_raw, list):
        ws_list: List[Dict[str, Any]] = data_raw
    else:
        ws_list = data_raw.get("workstations", [])
    # 兼容旧字段名
    for w in ws_list:
        if "camera" in w and "camera_id" not in w:
            w["camera_id"] = w.pop("camera")
    return [Workstation(**w) for w in ws_list]

def save_workstations(items: List[Workstation], path: str | Path = _CFG_PATH):
    p = Path(path)
    p.parent.mkdir(parents=True, exist_ok=True)
    data = {"workstations": [w.__dict__ for w in items]}
    yaml.safe_dump(data, p.open("w", encoding="utf-8"), allow_unicode=True, sort_keys=False)
