"""尺寸判定插件

读取上游几何测量结果(width_px, height_px) 和 ctx['mm_per_px']，
将像素尺寸换算为毫米并根据阈值给出 OK/NG。
"""
from __future__ import annotations

from typing import Dict, Any, Optional
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox

from plugins.plugin_base import PluginBase

__all__ = ["SizeCheckPlugin"]


class SizeCheckPlugin(PluginBase):
    """尺寸判定 (mm)"""

    name = "size_check"
    label = "尺寸判定"
    category = "测量工具"

    # 默认阈值 (0 表示不检查)
    params: Dict[str, Any] = {
        "width_min_mm": 0.0,
        "width_max_mm": 0.0,
        "height_min_mm": 0.0,
        "height_max_mm": 0.0,
    }

    param_labels = {
        "width_min_mm": "最小宽(mm)",
        "width_max_mm": "最大宽(mm)",
        "height_min_mm": "最小高(mm)",
        "height_max_mm": "最大高(mm)",
    }

    # -------------------------------------------------- 运行期
    def process(self, img, ctx):  # type: ignore[override]
        mm = ctx.get("mm_per_px", 0)
        w_px = ctx.get("width_px") or ctx.get("rect_width_px")
        h_px = ctx.get("height_px") or ctx.get("rect_height_px")
        if not mm or w_px is None or h_px is None:
            return img, ctx  # 缺少信息，直接跳过

        w_mm = w_px * mm
        h_mm = h_px * mm
        ctx["width_mm"] = w_mm
        ctx["height_mm"] = h_mm

        ok_w = True
        ok_h = True
        if self.params["width_min_mm"] > 0:
            ok_w = w_mm >= self.params["width_min_mm"]
        if self.params["width_max_mm"] > 0:
            ok_w = ok_w and (w_mm <= self.params["width_max_mm"])
        if self.params["height_min_mm"] > 0:
            ok_h = h_mm >= self.params["height_min_mm"]
        if self.params["height_max_mm"] > 0:
            ok_h = ok_h and (h_mm <= self.params["height_max_mm"])

        ctx["ok"] = ok_w and ok_h
        return img, ctx

    # -------------------------------------------------- UI
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, **_extra):
        dlg = tk.Toplevel(master)
        dlg.title("尺寸判定参数")
        dlg.geometry("260x180")
        dlg.resizable(False, False)

        vars_ = {
            k: tk.DoubleVar(value=params.get(k, 0.0))
            for k in ("width_min_mm", "width_max_mm", "height_min_mm", "height_max_mm")
        }

        frm = ttk.Frame(dlg, padding=10)
        frm.pack(fill=tk.BOTH, expand=True)

        row = 0
        for key, label in SizeCheckPlugin.param_labels.items():
            ttk.Label(frm, text=f"{label}:").grid(row=row, column=0, sticky=tk.W, pady=2)
            ttk.Entry(frm, textvariable=vars_[key], width=10).grid(row=row, column=1, sticky=tk.W)
            row += 1

        def _save():
            new_params = {k: v.get() for k, v in vars_.items()}
            on_change(new_params)
            dlg.destroy()

        btn_frm = ttk.Frame(frm)
        btn_frm.grid(row=row, column=0, columnspan=2, pady=8)
        ttk.Button(btn_frm, text="保存", command=_save).pack(side=tk.LEFT, padx=4)
        ttk.Button(btn_frm, text="取消", command=dlg.destroy).pack(side=tk.LEFT, padx=4)

        dlg.grab_set()
        dlg.transient(master)


# 注册实例
_ = SizeCheckPlugin()
