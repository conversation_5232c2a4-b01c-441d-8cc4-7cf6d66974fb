# YOLO检测与继电器IO输出集成指南

## 📋 概述

本文档介绍如何配置YOLO缺陷检测与继电器IO输出的集成，实现检测到NG时自动触发继电器输出信号。

## 🎯 功能特点

### ✅ 主要功能
- **条件触发**：根据YOLO检测结果自动触发继电器
- **灵活配置**：支持NG时触发、OK时触发或两者都触发
- **脉冲输出**：可配置脉冲持续时间，自动开启后关闭
- **多通道支持**：支持1-16路继电器通道
- **可视化配置**：提供友好的UI配置界面

### 🔧 支持的触发模式
1. **NG时触发**：检测到缺陷时输出信号（默认）
2. **OK时触发**：检测结果合格时输出信号
3. **混合模式**：同时支持NG和OK触发
4. **直接模式**：立即执行指定动作，不依赖检测结果

## 📦 硬件要求

### 继电器板规格
- **协议**：串口通信，4字节协议 `A0 | addr | op | checksum`
- **接口**：USB转串口或RS232/RS485
- **通道数**：支持2/4/8/16路继电器板
- **波特率**：9600（默认），支持其他标准波特率

### 连接方式
```
PC USB端口 ←→ USB转串口线 ←→ 继电器控制板 ←→ 外部设备
```

## ⚙️ 配置方法

### 1. 管道配置

在配方的管道配置中，将继电器插件添加到YOLO检测插件之后：

```yaml
# recipes/yolo/ws1/pipeline.yaml
- yolo_detect:
    config_file: configs/yolo/default.yml
- io_relay:
    port: "COM3"                # 串口号
    baud: 9600                  # 波特率
    relay_index: 1              # 继电器通道（1-16）
    action: "CONDITIONAL"       # 条件触发模式
    trigger_on_ng: true         # NG时触发
    trigger_on_ok: false        # OK时不触发
    pulse_duration: 100         # 脉冲持续时间(ms)
```

### 2. 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `port` | string | "" | 串口号，如COM3、COM4等 |
| `baud` | int | 9600 | 串口波特率 |
| `relay_index` | int | 1 | 继电器通道号（1-16） |
| `action` | string | "CONDITIONAL" | 动作模式：ON/OFF/TOG/QRY/CONDITIONAL |
| `trigger_on_ng` | bool | true | NG时是否触发 |
| `trigger_on_ok` | bool | false | OK时是否触发 |
| `pulse_duration` | int | 100 | 脉冲持续时间（毫秒） |

### 3. 可视化配置

系统提供了专门的配置界面，可以通过以下方式打开：

1. 在管道编辑器中选择继电器插件
2. 点击"配置"按钮
3. 在弹出的配置界面中设置参数

配置界面包含：
- **基本设置**：串口、波特率、继电器通道
- **触发模式**：选择触发条件和脉冲时间
- **说明文档**：详细的使用说明

## 🚀 使用场景

### 场景1：缺陷检测报警
```yaml
# 检测到缺陷时触发蜂鸣器或指示灯
trigger_on_ng: true
trigger_on_ok: false
pulse_duration: 500  # 500ms报警信号
```

### 场景2：自动分拣
```yaml
# 检测到NG产品时触发分拣气缸
trigger_on_ng: true
trigger_on_ok: false
pulse_duration: 200  # 200ms气缸动作
```

### 场景3：计数统计
```yaml
# 每个产品都触发计数脉冲
trigger_on_ng: true
trigger_on_ok: true
pulse_duration: 50   # 50ms计数脉冲
```

### 场景4：质量指示
```yaml
# 合格产品触发绿灯，不合格产品触发红灯
# 需要配置两个继电器插件实例
# 绿灯继电器：
trigger_on_ng: false
trigger_on_ok: true
relay_index: 1

# 红灯继电器：
trigger_on_ng: true
trigger_on_ok: false
relay_index: 2
```

## 🔍 工作原理

### 数据流程
```
相机图像 → 图像处理 → YOLO检测 → 继电器控制 → IO输出
```

### 触发逻辑
1. **YOLO检测**输出结果包含：
   - `ng_count`: NG检测数量
   - `ok_count`: OK检测数量
   - `total_count`: 总检测数量

2. **继电器插件**读取检测结果：
   - 如果 `trigger_on_ng=true` 且 `ng_count > 0`：触发
   - 如果 `trigger_on_ok=true` 且 `ok_count > 0`：触发

3. **脉冲输出**：
   - 发送开启命令：`A0 | channel | 01 | checksum`
   - 等待指定时间：`pulse_duration` 毫秒
   - 发送关闭命令：`A0 | channel | 00 | checksum`

## 🛠️ 故障排除

### 常见问题

#### 1. 继电器不动作
**可能原因**：
- 串口号配置错误
- 继电器板未上电
- 串口被其他程序占用
- 波特率不匹配

**解决方法**：
- 检查设备管理器中的串口号
- 确认继电器板电源指示灯亮起
- 关闭其他可能占用串口的程序
- 尝试不同的波特率设置

#### 2. 触发条件不正确
**可能原因**：
- YOLO检测结果为空
- 触发条件配置错误
- 检测阈值设置过高

**解决方法**：
- 检查YOLO检测是否正常工作
- 确认 `trigger_on_ng` 和 `trigger_on_ok` 设置
- 调整YOLO检测的置信度阈值

#### 3. 脉冲时间不合适
**可能原因**：
- 脉冲时间过短，外部设备响应不及时
- 脉冲时间过长，影响检测速度

**解决方法**：
- 根据外部设备特性调整 `pulse_duration`
- 一般建议：指示灯50-100ms，气缸100-300ms，蜂鸣器200-500ms

### 调试方法

#### 1. 查看日志输出
系统会输出详细的调试信息：
```
[IO_RELAY] 检测到NG: 1个，触发继电器
[IO_RELAY] 触发脉冲: 通道1, 持续100ms
```

#### 2. 使用继电器调试工具
可以使用独立的继电器调试界面测试硬件连接：
- 运行 `plugins/ui/io_tools/io_relay_ui.py`
- 手动测试各个继电器通道

#### 3. 检查上下文数据
在日志中查看YOLO检测结果：
```
DEBUG: YOLO process完成 - 检测数量: 1, NG: 1, OK: 0
```

## 📈 性能优化

### 建议配置
- **脉冲时间**：根据实际需求设置，避免过长影响检测速度
- **串口波特率**：9600已足够，无需设置更高
- **多工位配置**：不同工位使用不同串口，避免冲突

### 最佳实践
1. **测试先行**：在实际部署前充分测试硬件连接
2. **备用方案**：准备备用串口和继电器通道
3. **定期维护**：检查连接线路和继电器触点状态
4. **文档记录**：记录各工位的串口和通道分配

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 系统版本和配置文件
- 详细的错误日志
- 硬件型号和连接方式
- 问题复现步骤

---

*本文档版本：v1.0*  
*最后更新：2025-07-04*
