# 位置修正工具默认配置
# 支持透视变换、旋转、平移、缩放等多种修正方式

# 修正模式选择
# auto: 自动模式，根据启用的修正类型自动应用
# manual: 手动模式，需要手动设置所有参数
# perspective: 仅透视修正
# rotation: 仅旋转修正  
# translation: 仅平移修正
# scale: 仅缩放修正
# combined: 组合修正，按顺序应用多种修正
correction_mode: auto

# 透视变换修正
enable_perspective: false
# 3x3单应性矩阵，按行展开的9个浮点数
homography: [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0]
dst_width: 640
dst_height: 480

# 旋转修正
enable_rotation: true
rotation_angle: 0.0  # 旋转角度(度)
rotation_center_x: 320.0  # 旋转中心X坐标
rotation_center_y: 240.0  # 旋转中心Y坐标
auto_detect_angle: true  # 自动检测旋转角度

# 平移修正
enable_translation: false
offset_x: 0.0  # X方向偏移量
offset_y: 0.0  # Y方向偏移量

# 缩放修正
enable_scale: false
scale_x: 1.0  # X方向缩放比例
scale_y: 1.0  # Y方向缩放比例

# 自动检测参数
auto_detect_method: contour  # 检测方法: contour, template, feature
canny_th1: 50  # Canny边缘检测低阈值
canny_th2: 150  # Canny边缘检测高阈值
min_contour_area: 1000  # 最小轮廓面积

# 调试和显示选项
debug_draw: true  # 显示调试信息
show_grid: false  # 显示网格
grid_size: 50  # 网格大小

# 常用预设配置示例
presets:
  # 文档扫描预设 - 透视修正
  document_scan:
    correction_mode: perspective
    enable_perspective: true
    enable_rotation: false
    enable_translation: false
    enable_scale: false
    dst_width: 800
    dst_height: 600
    
  # 产品检测预设 - 旋转修正
  product_inspection:
    correction_mode: rotation
    enable_perspective: false
    enable_rotation: true
    enable_translation: false
    enable_scale: false
    auto_detect_angle: true
    
  # 精密测量预设 - 组合修正
  precision_measurement:
    correction_mode: combined
    enable_perspective: true
    enable_rotation: true
    enable_translation: true
    enable_scale: false
    auto_detect_angle: true
    debug_draw: true
    show_grid: true
