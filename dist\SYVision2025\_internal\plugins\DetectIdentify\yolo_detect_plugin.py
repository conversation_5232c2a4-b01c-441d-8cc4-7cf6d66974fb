"""YOLO Detect 插件包装

将 YOLODetector 暴露为流水线插件，可在 UI 中交互设置模型/阈值/ROI 等。
"""
from __future__ import annotations
from typing import Dict, Any
import importlib
import sys
from pathlib import Path

import cv2
import numpy as np
from PIL import Image

from plugins.plugin_base import PluginBase
from plugins.DetectIdentify.yolo_detect import load_model, YOLODetector


class YOLODetectPlugin(PluginBase):
    name = "yolo_detect"
    label = "YOLO 检测"
    category = "检测识别"

    params: Dict[str, Any] = {
        "config_file": "configs/yolo/default.yml",
    }

    param_labels = {
        "config_file": "配置文件",
    }

    def __init__(self, **params):
        self.detector: YOLODetector | None = None
        self.config: dict = {}
        super().__init__(**params)

    # ---------------- lifecycle ----------------
    def setup(self, params: Dict[str, Any]):
        self.detector: YOLODetector | None = None
        self.config: dict = {}  # 存储完整配置
        cfg_path = Path(params.get("config_file", ""))

        print(f"DEBUG: YOLO setup - 参数: {params}")
        print(f"DEBUG: YOLO setup - 配置文件路径: {cfg_path}")
        print(f"DEBUG: YOLO setup - 配置文件存在: {cfg_path.is_file()}")

        # 如果配置文件不存在，尝试使用路径管理模块解析
        if not cfg_path.is_file():
            try:
                import sys
                import os
                # 确保路径管理模块能被找到
                current_dir = os.path.dirname(os.path.abspath(__file__))
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
                if root_dir not in sys.path:
                    sys.path.insert(0, root_dir)
                from path_manager import get_resource_path
                resolved_cfg_path = Path(get_resource_path(str(cfg_path)))
                print(f"DEBUG: YOLO setup - 使用路径管理模块解析: {resolved_cfg_path}")
                print(f"DEBUG: YOLO setup - 解析后文件存在: {resolved_cfg_path.is_file()}")
                if resolved_cfg_path.is_file():
                    cfg_path = resolved_cfg_path
            except Exception as e:
                print(f"DEBUG: YOLO setup - 路径解析失败: {e}")

        if cfg_path.is_file():
            # YOLODetectFrame 会在 UI 内加载; 这里尝试读取模型路径并预加载
            try:
                import yaml
                with open(cfg_path, "r", encoding="utf-8") as f:
                    self.config = yaml.safe_load(f) or {}

                model_path = self.config.get("model", "")
                if model_path:
                    # 处理相对路径，相对于项目根目录
                    model_path_obj = Path(model_path)
                    if not model_path_obj.is_absolute():
                        # 首先尝试相对于当前工作目录
                        if not model_path_obj.is_file():
                            # 尝试相对于配置文件目录
                            model_path_obj = cfg_path.parent.parent.parent / model_path

                        # 如果还是找不到，尝试相对于程序根目录（编译后的情况）
                        if not model_path_obj.is_file():
                            # 使用路径管理模块获取程序根目录
                            try:
                                import sys
                                import os
                                # 确保路径管理模块能被找到
                                current_dir = os.path.dirname(os.path.abspath(__file__))
                                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
                                if root_dir not in sys.path:
                                    sys.path.insert(0, root_dir)
                                from path_manager import get_base_path, get_resource_path
                                model_path_obj = Path(get_resource_path(model_path))
                                print(f"DEBUG: YOLO使用路径管理模块加载模型: {model_path_obj}")
                            except ImportError as e:
                                print(f"DEBUG: YOLO路径管理模块导入失败: {e}")
                                # 回退到原来的逻辑
                                if hasattr(sys, '_MEIPASS'):
                                    base_path = Path(sys._MEIPASS)
                                else:
                                    base_path = Path(__file__).parent.parent.parent
                                model_path_obj = base_path / model_path
                                print(f"DEBUG: YOLO回退到原逻辑加载模型: {model_path_obj}")
                            except Exception as e:
                                print(f"DEBUG: YOLO路径管理模块使用失败: {e}")
                                # 回退到原来的逻辑
                                if hasattr(sys, '_MEIPASS'):
                                    base_path = Path(sys._MEIPASS)
                                else:
                                    base_path = Path(__file__).parent.parent.parent
                                model_path_obj = base_path / model_path
                                print(f"DEBUG: YOLO回退到原逻辑加载模型: {model_path_obj}")

                        # 最后尝试：直接在当前插件目录查找
                        if not model_path_obj.is_file():
                            plugin_dir = Path(__file__).parent
                            model_filename = Path(model_path).name
                            model_path_obj = plugin_dir / model_filename
                            print(f"DEBUG: YOLO尝试插件目录: {model_path_obj}")

                        # 如果还是找不到，尝试在models目录查找
                        if not model_path_obj.is_file():
                            try:
                                from path_manager import get_data_path
                                models_dir = Path(get_data_path()) / "models"
                                model_filename = Path(model_path).name
                                model_path_obj = models_dir / model_filename
                                print(f"DEBUG: YOLO尝试models目录: {model_path_obj}")
                            except Exception as e:
                                print(f"DEBUG: YOLO models目录尝试失败: {e}")

                    print(f"DEBUG: YOLO最终模型路径: {model_path_obj}, 存在: {model_path_obj.is_file()}")
                    if model_path_obj.is_file():
                        try:
                            self.detector = load_model(str(model_path_obj))
                            print(f"DEBUG: YOLO模型加载成功: {model_path_obj}")
                        except ImportError as e:
                            if "ultralytics not installed" in str(e):
                                print(f"YOLO模型加载失败: ultralytics库不可用")
                                print(f"提示: 请使用ONNX格式的模型文件(.onnx)替代PyTorch格式(.pt)")
                                print(f"或者确保ultralytics库已正确安装和打包")
                                # 尝试查找同名的ONNX文件
                                onnx_path = model_path_obj.with_suffix('.onnx')
                                if onnx_path.exists():
                                    print(f"发现同名ONNX文件: {onnx_path}")
                                    try:
                                        self.detector = load_model(str(onnx_path))
                                        print(f"DEBUG: 自动切换到ONNX模型成功: {onnx_path}")
                                        # 更新配置中的模型路径
                                        self.config["model_path"] = str(onnx_path)
                                    except Exception as onnx_e:
                                        print(f"ONNX模型加载也失败: {onnx_e}")
                                        self.detector = None
                                else:
                                    print(f"未找到对应的ONNX文件: {onnx_path}")
                                    self.detector = None
                            else:
                                print(f"YOLO模型导入失败: {e}")
                                self.detector = None
                        except FileNotFoundError as e:
                            print(f"YOLO模型文件不存在: {e}")
                            self.detector = None
                        except Exception as e:
                            print(f"YOLO模型加载失败: {e}")
                            import traceback
                            traceback.print_exc()
                            self.detector = None
                    else:
                        print(f"YOLO模型文件不存在: {model_path_obj}")
                    
                    # 加载ROI模板（如果配置了）
                    template_path = self.config.get("template_npy", "")
                    if template_path:
                        # 如果是相对路径，相对于配置文件目录
                        if not Path(template_path).is_absolute():
                            template_path = str(cfg_path.parent / template_path)
                        
                        if Path(template_path).is_file():
                            try:
                                if template_path.endswith('.npy'):
                                    # 加载轮廓数组文件
                                    import numpy as np
                                    contour_data = np.load(template_path)
                                    self.detector.tpl_cnt = contour_data
                                    self.detector.tpl_threshold = self.config.get("roi_thresh", 0.8)
                                    self.detector.roi_expand = self.config.get("roi_expand", 10.0) / 100.0  # 转换为比例
                                    self.detector.min_contour_area = self.config.get("min_contour_area", 100)  # 最小轮廓面积
                                    print(f"DEBUG: ROI轮廓模板加载成功: {template_path}, 轮廓点数: {len(contour_data)}")
                                else:
                                    # 加载图像模板文件
                                    self.detector.load_template(template_path, self.config.get("roi_thresh", 0.8))
                                    print(f"DEBUG: ROI图像模板加载成功: {template_path}")
                            except Exception as e:
                                print(f"DEBUG: ROI模板加载失败: {e}")
                                import traceback
                                traceback.print_exc()
                        else:
                            print(f"DEBUG: ROI模板文件不存在: {template_path}")
            except Exception as e:
                print(f"DEBUG: YOLO模型加载失败: {e}")
                import traceback
                traceback.print_exc()

    def process(self, img, ctx):
        if img is None or self.detector is None:
            if self.detector is None:
                print(f"[YOLO] 检测器未初始化，尝试重新初始化...")
                # 尝试重新初始化
                try:
                    self.setup(self.params)
                except Exception as e:
                    print(f"[YOLO] 重新初始化失败: {e}")

            if self.detector is None:
                print(f"[YOLO] 检测器不可用，跳过检测")
                print(f"[YOLO] 建议: 请检查模型文件路径或使用ONNX格式模型")
                # 在图像上显示错误信息
                if img is not None:
                    vis_img = img.copy() if hasattr(img, 'copy') else np.array(img)
                    if hasattr(vis_img, 'shape') and len(vis_img.shape) >= 2:
                        cv2.putText(vis_img, "YOLO Detector Not Available", (10, 30),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                        cv2.putText(vis_img, "Check model file or use ONNX format", (10, 60),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                        return vis_img, ctx
                return img, ctx
        
        # 获取配置参数
        use_roi = self.config.get("use_roi", False)
        mm_per_px = self.config.get("mm_per_px", 0.01)
        ng_conf_th = self.config.get("ng_conf_th", 0.5)
        ng_size_th = self.config.get("ng_size_th", 0.3)  # mm²
        roi_thresh = self.config.get("roi_thresh", 0.8)  # ROI匹配阈值
        conf_thresh = self.config.get("conf", 0.25)  # 检测置信度阈值
        iou_thresh = 0.45  # IOU阈值
        print(f"DEBUG: YOLO配置 - use_roi: {use_roi}, mm_per_px: {mm_per_px}, ng_conf_th: {ng_conf_th}, ng_size_th: {ng_size_th}, roi_thresh: {roi_thresh}, conf: {conf_thresh}")
        
        # 确保图像是numpy数组格式
        detect_img = img
        if isinstance(img, Image.Image):
            print(f"DEBUG: 将PIL图像转换为numpy数组用于检测")
            detect_img = np.array(img)
            # PIL图像通常是RGB格式，YOLO需要BGR格式
            if len(detect_img.shape) == 3 and detect_img.shape[2] == 3:
                detect_img = cv2.cvtColor(detect_img, cv2.COLOR_RGB2BGR)
        
        # 先进行ROI匹配（如果启用）
        roi_shape_ng = False
        if use_roi:
            # 执行ROI匹配但不进行YOLO检测，只是为了获取ROI信息
            temp_res = self.detector.detect(detect_img.copy(), conf=conf_thresh, iou=iou_thresh, use_roi=use_roi)

            # 检查ROI形状匹配结果
            if hasattr(self.detector, 'last_score') and self.detector.last_score is not None:
                roi_score = self.detector.last_score
                if roi_score < roi_thresh:
                    roi_shape_ng = True
                    print(f"DEBUG: ROI形状匹配NG({roi_score:.3f} < {roi_thresh:.3f})，跳过YOLO检测以节约时间")
                    res = []  # 跳过YOLO检测，返回空结果
                else:
                    print(f"DEBUG: ROI形状匹配OK({roi_score:.3f} >= {roi_thresh:.3f})，执行YOLO检测")
                    res = temp_res  # 使用已经执行的检测结果
            else:
                print(f"DEBUG: 未获取到ROI匹配分数，执行YOLO检测")
                res = temp_res
        else:
            # 不使用ROI时，直接进行YOLO检测
            print(f"DEBUG: 开始YOLO检测（无ROI），图像形状: {detect_img.shape if hasattr(detect_img, 'shape') else 'unknown'}")
            res = self.detector.detect(detect_img.copy(), conf=conf_thresh, iou=iou_thresh, use_roi=False)

        print(f"DEBUG: YOLO检测完成，结果数量: {len(res)}")
        print(f"DEBUG: YOLO检测结果: {res}")
        
        # 可视化绘制
        vis = img.copy()
        
        # 如果是PIL图像，转换为numpy数组进行绘制
        is_pil = isinstance(vis, Image.Image)
        if is_pil:
            print(f"DEBUG: 将PIL图像转换为numpy数组进行绘制")
            vis_np = np.array(vis)
            # PIL图像通常是RGB格式，OpenCV需要BGR格式
            if len(vis_np.shape) == 3 and vis_np.shape[2] == 3:
                vis_np = cv2.cvtColor(vis_np, cv2.COLOR_RGB2BGR)
        else:
            vis_np = vis
        
        # 绘制ROI区域（产品模板轮廓，用于排除背景干扰）
        roi_drawn = False
        if use_roi:
            print(f"DEBUG: ROI启用，检查产品模板轮廓...")
            
            # 检查是否有ROI轮廓匹配结果
            if hasattr(self.detector, 'last_roi_pts') and self.detector.last_roi_pts is not None:
                # 绘制轮廓ROI（产品模板的旋转矩形）
                roi_pts = self.detector.last_roi_pts
                score = getattr(self.detector, 'last_score', 0.0)

                # 根据匹配分数选择颜色和状态
                if score >= roi_thresh:
                    roi_color = (0, 255, 0)  # 绿色：形状匹配OK
                    status_text = "Shape OK"
                else:
                    roi_color = (0, 0, 255)  # 红色：形状匹配NG
                    status_text = "Shape NG"

                cv2.polylines(vis_np, [roi_pts], True, roi_color, 2)

                # 在轮廓中心添加标签
                center_x = int(roi_pts[:, 0].mean())
                center_y = int(roi_pts[:, 1].mean())
                cv2.putText(vis_np, status_text, (center_x - 30, center_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, roi_color, 2)

                # 显示ROI匹配分数
                score_text = f"Score: {score:.3f}"
                cv2.putText(vis_np, score_text, (center_x - 30, center_y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, roi_color, 1)

                roi_drawn = True
                print(f"DEBUG: 产品轮廓ROI已绘制 - 中心: ({center_x}, {center_y}), 匹配分数: {score:.3f}, 状态: {status_text}")
                
            elif hasattr(self.detector, 'last_roi') and self.detector.last_roi is not None:
                # 绘制矩形ROI（模板匹配结果）
                roi_x, roi_y, roi_w, roi_h = self.detector.last_roi
                roi_color = (255, 255, 0)  # 青色ROI框
                cv2.rectangle(vis_np, (roi_x, roi_y), (roi_x + roi_w, roi_y + roi_h), roi_color, 2)
                cv2.putText(vis_np, "Product ROI", (roi_x, roi_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, roi_color, 2)
                
                # 显示ROI匹配分数
                if hasattr(self.detector, 'last_score') and self.detector.last_score is not None:
                    score_text = f"Match: {self.detector.last_score:.3f}"
                    cv2.putText(vis_np, score_text, (roi_x, roi_y + roi_h + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, roi_color, 1)
                
                roi_drawn = True
                print(f"DEBUG: 产品矩形ROI已绘制 - 位置: ({roi_x}, {roi_y}), 尺寸: {roi_w}x{roi_h}, 匹配分数: {getattr(self.detector, 'last_score', 'N/A')}")
            else:
                print(f"DEBUG: 未找到产品ROI - last_roi: {getattr(self.detector, 'last_roi', 'None')}, last_roi_pts: {getattr(self.detector, 'last_roi_pts', 'None')}")
                
                # 显示ROI配置信息
                has_template = hasattr(self.detector, 'template') and self.detector.template is not None
                has_contour = hasattr(self.detector, 'tpl_cnt') and self.detector.tpl_cnt is not None
                print(f"DEBUG: ROI模板状态 - 灰度模板: {has_template}, 轮廓模板: {has_contour}")
        
        if not roi_drawn and use_roi:
            # 显示ROI匹配失败的详细信息
            if hasattr(self.detector, 'last_score') and self.detector.last_score is not None:
                print(f"DEBUG: ROI匹配失败 - 分数: {self.detector.last_score:.3f}, 阈值: {self.tpl_thresh:.3f}, 使用全图检测")
            else:
                print(f"DEBUG: ROI启用但未找到匹配的产品模板，使用全图检测")
        
        # 处理检测结果，计算尺寸和NG/OK状态
        enhanced_results = []
        total_ng = 0
        total_ok = 0
        total_area_mm2 = 0.0

        # ROI形状匹配结果已在前面判断过了
        # roi_shape_ng 变量已经设置好了
        
        for det in res:
            x1, y1, x2, y2 = map(int, det.get('box', (0, 0, 0, 0)))
            label = str(det.get('label', ''))
            score = det.get('score', 0.0)
            
            # 计算像素尺寸
            width_px = x2 - x1
            height_px = y2 - y1
            area_px = width_px * height_px
            
            # 计算实际尺寸（毫米）
            width_mm = width_px * mm_per_px
            height_mm = height_px * mm_per_px
            area_mm2 = area_px * (mm_per_px ** 2)
            
            # NG/OK判断
            is_ng = score >= ng_conf_th and area_mm2 >= ng_size_th
            status = "NG" if is_ng else "OK"
            
            if is_ng:
                total_ng += 1
            else:
                total_ok += 1
            total_area_mm2 += area_mm2
            
            # 增强的检测结果
            enhanced_det = {
                **det,
                'width_px': width_px,
                'height_px': height_px,
                'area_px': area_px,
                'width_mm': width_mm,
                'height_mm': height_mm,
                'area_mm2': area_mm2,
                'status': status,
                'is_ng': is_ng
            }
            enhanced_results.append(enhanced_det)
            
            # 绘制检测框（根据NG状态使用不同颜色）
            color = (0, 0, 255) if is_ng else (0, 255, 0)  # 红色NG，绿色OK
            cv2.rectangle(vis_np, (x1, y1), (x2, y2), color, 2)
            
            # 绘制多行信息
            info_lines = [
                f"{label}:{score:.2f}",
                f"{width_mm:.1f}x{height_mm:.1f}mm",
                f"Area:{area_mm2:.2f}mm²",
                f"Status:{status}"
            ]
            
            for i, line in enumerate(info_lines):
                y_offset = y1 - 10 - (len(info_lines) - 1 - i) * 15
                if y_offset < 0:
                    y_offset = y2 + 15 + i * 15
                cv2.putText(vis_np, line, (x1, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # 如果原始是PIL图像，转换回PIL格式
        if is_pil:
            print(f"DEBUG: 将numpy数组转换回PIL图像")
            # 转换回RGB格式并创建PIL图像
            if len(vis_np.shape) == 3 and vis_np.shape[2] == 3:
                vis_np = cv2.cvtColor(vis_np, cv2.COLOR_BGR2RGB)
            vis = Image.fromarray(vis_np)
        else:
            vis = vis_np
        
        # 如果ROI形状匹配NG，则整体结果为NG
        if roi_shape_ng:
            total_ng += 1
            if total_ok > 0:
                total_ok -= 1  # 如果之前有OK，需要调整
        elif use_roi and roi_drawn and total_ng == 0:
            # ROI匹配成功且没有检测到缺陷，算作OK
            total_ok = 1

        # 保存增强的结果到上下文
        detection_summary = {
            'detections': enhanced_results,
            'total_count': len(enhanced_results),
            'ng_count': total_ng,
            'ok_count': total_ok,
            'total_area_mm2': total_area_mm2,
            'roi_used': use_roi,
            'roi_matched': roi_drawn,
            'roi_shape_ok': not roi_shape_ng if use_roi else True,
            'roi_score': getattr(self.detector, 'last_score', None) if use_roi else None
        }
        
        ctx[self.name] = detection_summary

        # 详细的结果日志
        roi_info = ""
        if use_roi:
            roi_status = "OK" if not roi_shape_ng else "NG"
            roi_score = getattr(self.detector, 'last_score', 0.0)
            roi_info = f", ROI形状: {roi_status}({roi_score:.3f})"

        print(f"DEBUG: YOLO process完成 - 检测数量: {len(enhanced_results)}, NG: {total_ng}, OK: {total_ok}, 总面积: {total_area_mm2:.2f}mm²{roi_info}")
        print(f"DEBUG: 返回图像类型: {type(vis)}, 上下文: {detection_summary}")
        return vis, ctx

    # ---------------- UI ----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        """打开 YOLODetectFrame UI，并在关闭时同步配置文件路径。"""
        import tkinter as tk
        from tkinter import messagebox

        try:
            FrameCls = importlib.import_module('plugins.ui.detect_identify.yolo_detect_ui').YOLODetectFrame
        except Exception as e:
            messagebox.showerror('错误', f'加载 UI 失败: {e}')
            return

        win = tk.Toplevel(master)
        win.title('YOLO 检测')
        frame = FrameCls(win)
        frame.pack(fill=tk.BOTH, expand=True)
        # 预览图像
        if preview_img is not None:
            frame.img_src = preview_img.copy()
            frame._show_image(frame.canvas_src, preview_img, is_src=True)
        cfg_file = params.get('config_file', '')
        if cfg_file and Path(cfg_file).is_file():
            frame._load_cfg()

        def _on_close():
            on_change({'config_file': cfg_file or ''})
            win.destroy()
        win.protocol('WM_DELETE_WINDOW', _on_close)
        win.grab_set()
        win.transient(master)


_ = YOLODetectPlugin
