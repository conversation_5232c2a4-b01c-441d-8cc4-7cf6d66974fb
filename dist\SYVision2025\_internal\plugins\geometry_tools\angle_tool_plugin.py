"""Angle Measurement 插件 - 支持三点测量、拟合直线、ROI拟合三种角度测量模式。"""
from __future__ import annotations
from typing import Dict, Any
import importlib
from plugins.plugin_base import PluginBase

import time

class AngleToolPlugin(PluginBase):
    name = "angle_tool"
    label = "角度测量"
    category = "几何测量"
    params = {
        'measurement_mode': 'three_points',  # 测量模式: three_points, fitted_lines, roi_fitting
        'ransac_threshold': 2.0,             # RANSAC拟合阈值
        'canny_threshold1': 50,              # Canny边缘检测低阈值
        'canny_threshold2': 150,             # Canny边缘检测高阈值
        'arc_step': '',                      # 兼容旧版本的弧度步骤
        'angle_tolerance': 5.0,              # 角度容差
        'min_line_length': 15,               # 最小线段长度
    }

    def process(self, img, ctx: Dict[str, Any]):
        start_time = time.time()  # 记录开始时间
        status = "success"
        result = {}

        try:
            measurement_mode = self.params.get('measurement_mode', 'three_points')

            if measurement_mode == 'arc_based':
                # 兼容旧版本：基于弧度工具结果
                arc_step = self.params.get('arc_step', '')
                if arc_step and arc_step in ctx:
                    res = ctx[arc_step]
                    if isinstance(res, dict) and 'central_angle_rad' in res:
                        angle_deg = res['central_angle_rad'] * 180.0 / 3.141592653589793
                        result = {
                            'angle_rad': res['central_angle_rad'],
                            'angle_deg': angle_deg,
                            'measurement_mode': 'arc_based',
                            'source_step': arc_step
                        }
                    else:
                        status = f"error: 弧度步骤 '{arc_step}' 结果无效"
                else:
                    status = f"error: 未找到弧度步骤 '{arc_step}'"

            elif measurement_mode in ['three_points', 'fitted_lines', 'roi_fitting']:
                # 新的角度测量模式需要通过UI进行交互式测量
                # 在流水线中，这些模式主要用于参数配置和结果展示
                result = {
                    'measurement_mode': measurement_mode,
                    'ransac_threshold': self.params.get('ransac_threshold', 2.0),
                    'canny_threshold1': self.params.get('canny_threshold1', 50),
                    'canny_threshold2': self.params.get('canny_threshold2', 150),
                    'angle_tolerance': self.params.get('angle_tolerance', 5.0),
                    'min_line_length': self.params.get('min_line_length', 15),
                    'message': '请使用角度测量工具进行交互式测量'
                }

            else:
                status = f"error: 未知的测量模式 '{measurement_mode}'"

        except Exception as e:
            status = f"error: {str(e)}"

        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算运行时间

        # 更新 ctx 以包含状态和运行时间
        ctx[self.name] = {
            **result,
            "status": status,
            "elapsed_time": elapsed_time,
        }

        return img, ctx

    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img=None):
        """打开完整的角度测量工具UI界面。"""
        try:
            import tkinter as tk
            import importlib
            mod = importlib.import_module('plugins.ui.geometry_tools.angle_tool_ui')
            ui_cls = getattr(mod, 'AngleToolUI', None)
            if ui_cls is None:
                from tkinter import messagebox
                messagebox.showerror('错误', '未找到 AngleToolUI')
                return

            # 创建完整的角度测量工具窗口
            tool_window = tk.Toplevel(master)
            tool_window.title("角度测量工具")
            tool_window.geometry("1000x750")
            tool_window.transient(master)

            # 创建角度测量工具UI
            angle_ui = ui_cls(tool_window, img=preview_img)
            angle_ui.pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror('错误', f'打开角度测量工具失败: {str(e)}')

    @staticmethod
    def show_results(master, ctx: Dict[str, Any], preview_img=None):
        """展示角度测量结果。"""
        try:
            import tkinter as tk
            from tkinter import ttk, messagebox

            angle_info = ctx.get('angle_tool', {})

            # 创建结果展示窗口
            result_window = tk.Toplevel(master)
            result_window.title("角度测量结果")
            result_window.geometry("500x400")
            result_window.transient(master)

            main_frame = ttk.Frame(result_window, padding=10)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 结果信息
            ttk.Label(main_frame, text="角度测量结果", font=('Arial', 12, 'bold')).pack(anchor='w', pady=(0,10))

            # 状态信息
            status = angle_info.get('status', 'unknown')
            status_color = 'green' if status == 'success' else 'red'
            status_label = ttk.Label(main_frame, text=f"状态: {status}", foreground=status_color)
            status_label.pack(anchor='w', pady=2)

            # 测量模式
            mode = angle_info.get('measurement_mode', 'unknown')
            mode_names = {
                'three_points': '三点测量',
                'fitted_lines': '拟合直线',
                'roi_fitting': 'ROI拟合',
                'arc_based': '基于弧度工具'
            }
            mode_text = mode_names.get(mode, mode)
            ttk.Label(main_frame, text=f"测量模式: {mode_text}").pack(anchor='w', pady=2)

            # 角度结果
            if 'angle_deg' in angle_info:
                angle_deg = angle_info['angle_deg']
                angle_rad = angle_info.get('angle_rad', angle_deg * 3.141592653589793 / 180.0)

                ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=10)
                ttk.Label(main_frame, text="测量结果", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0,5))

                result_frame = ttk.Frame(main_frame)
                result_frame.pack(fill=tk.X, pady=5)

                ttk.Label(result_frame, text=f"角度: {angle_deg:.2f}°", font=('Arial', 11)).pack(anchor='w')
                ttk.Label(result_frame, text=f"弧度: {angle_rad:.4f} rad", font=('Arial', 11)).pack(anchor='w')

                # 角度分类
                if angle_deg < 30:
                    angle_type = "锐角"
                elif angle_deg < 90:
                    angle_type = "锐角"
                elif abs(angle_deg - 90) < 1:
                    angle_type = "直角"
                elif angle_deg < 180:
                    angle_type = "钝角"
                else:
                    angle_type = "反射角"

                ttk.Label(result_frame, text=f"类型: {angle_type}", font=('Arial', 11)).pack(anchor='w')

            # 参数信息
            if any(key in angle_info for key in ['ransac_threshold', 'canny_threshold1', 'canny_threshold2']):
                ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=10)
                ttk.Label(main_frame, text="算法参数", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0,5))

                param_frame = ttk.Frame(main_frame)
                param_frame.pack(fill=tk.X, pady=5)

                if 'ransac_threshold' in angle_info:
                    ttk.Label(param_frame, text=f"RANSAC阈值: {angle_info['ransac_threshold']}").pack(anchor='w')
                if 'canny_threshold1' in angle_info:
                    ttk.Label(param_frame, text=f"Canny阈值: {angle_info['canny_threshold1']}-{angle_info.get('canny_threshold2', 'N/A')}").pack(anchor='w')

            # 性能信息
            if 'elapsed_time' in angle_info:
                ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=10)
                elapsed_time = angle_info['elapsed_time']
                ttk.Label(main_frame, text=f"处理时间: {elapsed_time:.3f}秒").pack(anchor='w')

            # 按钮
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=tk.X, pady=(20,0))

            def open_tool():
                """打开角度测量工具"""
                try:
                    mod = importlib.import_module('plugins.ui.geometry_tools.angle_tool_ui')
                    ui_cls = getattr(mod, 'AngleToolUI', None)
                    if ui_cls is None:
                        raise ImportError('未找到 AngleToolUI')
                    ui_cls(master, img=preview_img)
                except Exception as e:
                    messagebox.showerror('错误', f'打开角度测量工具失败: {str(e)}')

            ttk.Button(btn_frame, text="打开测量工具", command=open_tool).pack(side='left')
            ttk.Button(btn_frame, text="关闭", command=result_window.destroy).pack(side='right')

        except Exception as e:
            import tkinter as tk
            from tkinter import messagebox
            messagebox.showerror('错误', f'展示结果失败: {str(e)}')

_ = AngleToolPlugin()
