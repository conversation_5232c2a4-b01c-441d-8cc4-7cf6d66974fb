"""Pipeline plugin wrapper for SmoothProcessor."""
from __future__ import annotations
from typing import Dict, Any
import numpy as np

from plugins.plugin_base import PluginBase
from plugins.core.image_process.smooth import SmoothProcessor

__all__ = ["Smooth"]

class Smooth(PluginBase):
    """图像柔化流水线插件"""

    name = "smooth"
    label = "图像柔化"

    params: Dict[str, Any] = {
        "method": "gaussian",           # gaussian|bilateral|box|median
        "kernel_size": 15,              # 模糊核大小
        "sigma_x": 0,                   # X方向标准差 (0=自动)
        "sigma_y": 0,                   # Y方向标准差 (0=自动)
        "bilateral_d": 9,               # 双边滤波直径
        "bilateral_sigma_color": 75,    # 颜色空间标准差
        "bilateral_sigma_space": 75,    # 坐标空间标准差
        "contrast_alpha": 0.8,          # 对比度调整系数
        "brightness_beta": 20,          # 亮度调整偏移
        "morph_kernel_size": 3,         # 形态学核大小
        "enable_contrast_adjust": True, # 启用对比度调整
        "enable_morph": False,          # 启用形态学处理
        # 新增参数
        "gamma": 1.0,                   # 伽马校正值
        "saturation": 1.0,              # 饱和度倍数
        "hue_shift": 0,                 # 色相偏移
        "enable_gamma": False,          # 启用伽马校正
        "enable_color_adjust": False,   # 启用颜色调整
        "noise_reduction": 0.0,         # 噪声减少强度
        "enable_noise_reduction": False, # 启用噪声减少
        "edge_preserve": 0.5,           # 边缘保持强度
        "enable_edge_preserve": False,  # 启用边缘保持
    }

    param_labels = {
        "method": "平滑方法",
        "kernel_size": "核大小",
        "sigma_x": "X标准差",
        "sigma_y": "Y标准差",
        "bilateral_d": "双边滤波直径",
        "bilateral_sigma_color": "颜色标准差",
        "bilateral_sigma_space": "空间标准差",
        "contrast_alpha": "对比度系数",
        "brightness_beta": "亮度偏移",
        "morph_kernel_size": "形态学核大小",
        "enable_contrast_adjust": "启用对比度调整",
        "enable_morph": "启用形态学处理",
        # 新增参数标签
        "gamma": "伽马校正",
        "saturation": "饱和度",
        "hue_shift": "色相偏移",
        "enable_gamma": "启用伽马校正",
        "enable_color_adjust": "启用颜色调整",
        "noise_reduction": "噪声减少",
        "enable_noise_reduction": "启用噪声减少",
        "edge_preserve": "边缘保持",
        "enable_edge_preserve": "启用边缘保持",
    }

    def __init__(self, **params):
        super().__init__(**params)
        self._proc = SmoothProcessor(**self.params)

    def setup(self, params: Dict[str, Any]):
        self._proc = SmoothProcessor(**params)

    def process(self, img, ctx):
        if img is None:
            return img, ctx
        
        res = self._proc.process(img)
        
        # 保存处理信息到上下文
        ctx[self.name] = {
            "method": self.params.get("method", "gaussian"),
            "kernel_size": self.params.get("kernel_size", 15),
            "processed": True
        }
        
        return res["output"], ctx

    # ---------------- UI -----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, *, preview_img: 'np.ndarray | None' = None, **extra):
        import importlib, tkinter as tk
        try:
            mod = importlib.import_module('plugins.ui.image_process.smooth_ui')
            if hasattr(mod, 'SmoothFrame'):
                win = tk.Toplevel(master)
                win.title("图像柔化参数设置")
                win.geometry("500x600")
                
                frame = mod.SmoothFrame(win, params=params, on_change=on_change, preview_img=preview_img)
                frame.pack(fill='both', expand=True)
                
                return win
        except ImportError as e:
            print(f"无法加载图像柔化UI模块: {e}")
            # 创建简单的参数对话框
            win = tk.Toplevel(master)
            win.title("图像柔化参数")
            win.geometry("400x500")
            
            import tkinter.ttk as ttk
            
            # 方法选择
            ttk.Label(win, text="平滑方法:").pack(pady=5)
            method_var = tk.StringVar(value=params.get("method", "gaussian"))
            method_combo = ttk.Combobox(win, textvariable=method_var, 
                                      values=["gaussian", "bilateral", "box", "median"])
            method_combo.pack(pady=5)
            
            # 核大小
            ttk.Label(win, text="核大小:").pack(pady=5)
            kernel_var = tk.IntVar(value=params.get("kernel_size", 15))
            kernel_scale = ttk.Scale(win, from_=3, to=51, variable=kernel_var, orient='horizontal')
            kernel_scale.pack(pady=5, fill='x', padx=20)
            
            # 对比度系数
            ttk.Label(win, text="对比度系数:").pack(pady=5)
            contrast_var = tk.DoubleVar(value=params.get("contrast_alpha", 0.8))
            contrast_scale = ttk.Scale(win, from_=0.1, to=2.0, variable=contrast_var, orient='horizontal')
            contrast_scale.pack(pady=5, fill='x', padx=20)
            
            # 亮度偏移
            ttk.Label(win, text="亮度偏移:").pack(pady=5)
            brightness_var = tk.IntVar(value=params.get("brightness_beta", 20))
            brightness_scale = ttk.Scale(win, from_=-100, to=100, variable=brightness_var, orient='horizontal')
            brightness_scale.pack(pady=5, fill='x', padx=20)
            
            def update_params():
                new_params = {
                    "method": method_var.get(),
                    "kernel_size": int(kernel_var.get()) | 1,  # 确保奇数
                    "contrast_alpha": contrast_var.get(),
                    "brightness_beta": brightness_var.get(),
                }
                params.update(new_params)
                if on_change:
                    on_change(params)
            
            # 绑定变化事件
            method_var.trace('w', lambda *args: update_params())
            kernel_var.trace('w', lambda *args: update_params())
            contrast_var.trace('w', lambda *args: update_params())
            brightness_var.trace('w', lambda *args: update_params())
            
            return win
