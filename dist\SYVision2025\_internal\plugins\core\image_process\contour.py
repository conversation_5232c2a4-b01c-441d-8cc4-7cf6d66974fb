"""Core contour detection processor."""
from __future__ import annotations
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple

__all__ = ['ContourProcessor']

class ContourProcessor:
    name = 'ImageContour'

    def __init__(self, *, mode: str = 'external', min_area: int = 10, max_area: int = 1_000_000,
                 min_perim: int = 0, max_perim: int = 10_000_000,
                 draw_rect: bool = True, draw_center: bool = False):
        """Parameters
        mode : 'external' 使用 RETR_EXTERNAL，'all' 使用 RETR_LIST
        min_area, max_area : 面积筛选
        min_perim, max_perim : 周长筛选
        draw_rect : 在输出图像绘制最小外接矩形
        draw_center : 绘制中心点
        """
        self.mode = mode
        self.min_area = int(min_area)
        self.max_area = int(max_area)
        self.min_perim = int(min_perim)
        self.max_perim = int(max_perim)
        self.draw_rect = bool(draw_rect)
        self.draw_center = bool(draw_center)

    # ------------------------------------------------------------------
    def process(self, img: np.ndarray, **override) -> Dict[str, Any]:
        if override:
            for k, v in override.items():
                if hasattr(self, k):
                    setattr(self, k, v)
        gray = img
        if gray.ndim == 3:
            gray = cv2.cvtColor(gray, cv2.COLOR_BGR2GRAY)
        # threshold if not binary
        if gray.dtype != np.uint8 or len(np.unique(gray)) > 2:
            _, gray = cv2.threshold(gray, 0, 255, cv2.THRESH_OTSU)
        mode_flag = cv2.RETR_EXTERNAL if self.mode == 'external' else cv2.RETR_LIST
        contours, _ = cv2.findContours(gray, mode_flag, cv2.CHAIN_APPROX_SIMPLE)
        sel: List[np.ndarray] = []
        stats: List[Dict[str, Any]] = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            perim = cv2.arcLength(cnt, True)
            if area < self.min_area or area > self.max_area or perim < self.min_perim or perim > self.max_perim:
                continue
            sel.append(cnt)
            x,y,w,h = cv2.boundingRect(cnt)
            # 最小外接矩形（旋转）
            (cx,cy),(rw,rh),angle = cv2.minAreaRect(cnt)
            rw,rh = float(rw), float(rh)
            roundness = 0 if perim==0 else 4*np.pi*area/(perim**2)
            stats.append({'area': int(area), 'perim': float(f'{perim:.1f}'), 'rect': (x,y,w,h),
                          'roundness': float(f'{roundness:.3f}'),
                          'min_rect': {'center':(cx,cy),'size':(rw,rh),'angle':float(f'{angle:.1f}')}})
        # draw
        out = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
        cv2.drawContours(out, sel, -1, (0,255,0), 1)
        if self.draw_rect:
            for s in stats:
                x,y,w,h = s['rect']
                cv2.rectangle(out, (x,y), (x+w,y+h), (0,255,0), 1)
        if self.draw_center:
            for cnt in sel:
                m = cv2.moments(cnt)
                if m['m00']!=0:
                    cx = int(m['m10']/m['m00']); cy = int(m['m01']/m['m00'])
                    cv2.circle(out,(cx,cy),3,(255,0,0),-1)
        return {'output': out, 'contours': sel, 'stats': stats}
