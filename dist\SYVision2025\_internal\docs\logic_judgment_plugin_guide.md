# 逻辑判断插件使用指南

## 📋 概述

逻辑判断插件是视觉检测系统的决策核心，负责根据各个检测插件的结果进行综合判断，并输出相应的控制信号到IO控制板。

## 🎯 主要功能

### 1. 多条件逻辑判断
- 支持多个判断条件的组合
- 支持AND、OR、NOT逻辑操作
- 支持复杂的逻辑表达式
- 支持条件权重设置

### 2. 灵活的比较操作
- **数值比较**: `==`, `!=`, `>`, `>=`, `<`, `<=`
- **范围检查**: `IN_RANGE`, `OUT_RANGE`
- **字符串匹配**: 精确匹配和模糊匹配

### 3. IO控制输出
- **数字IO**: 继电器、LED指示灯、气缸控制
- **模拟IO**: 电压/电流信号输出
- **网络IO**: TCP/UDP、Modbus、OPC等协议

### 4. 统计分析
- 实时统计合格率、不合格率
- 连续结果监控
- 报警机制

## 🔧 配置说明

### 基本设置

```yaml
enable_judgment: true        # 启用判断功能
judgment_mode: "strict"      # 判断模式：strict/tolerant/custom
default_result: "UNKNOWN"    # 默认结果
ng_threshold: 5             # 连续NG报警阈值
warn_threshold: 10          # 连续WARN报警阈值
```

### 判断条件配置

每个判断条件包含以下参数：

```yaml
conditions:
  - name: "尺寸检查"                    # 条件名称
    source_plugin: "geometry_measurement" # 数据源插件
    source_key: "length_mm"             # 数据源键名
    operator: "IN_RANGE"                # 比较操作符
    target_value: [12.8, 13.8]         # 目标值
    weight: 1.0                         # 权重
    enabled: true                       # 是否启用
```

### 逻辑表达式

使用逻辑表达式组合多个条件：

```yaml
# 所有条件都必须满足
logic_expression: "C1 AND C2 AND C3 AND C4"

# 任一条件满足即可
logic_expression: "C1 OR C2 OR C3"

# 复杂组合
logic_expression: "(C1 AND C2) OR (C3 AND C4)"

# 包含否定
logic_expression: "C1 AND NOT C2"
```

### IO输出配置

```yaml
io_outputs:
  - name: "OK指示灯"          # 输出名称
    io_type: "digital"        # IO类型：digital/analog/network
    address: "DO1"            # IO地址
    ok_value: true            # OK状态输出值
    ng_value: false           # NG状态输出值
    warn_value: null          # WARN状态输出值（可选）
    enabled: true             # 是否启用
```

## 📊 使用示例

### 示例1：简单尺寸检查

```yaml
conditions:
  - name: "长度检查"
    source_plugin: "geometry_measurement"
    source_key: "length_mm"
    operator: "IN_RANGE"
    target_value: [24.5, 25.5]
    weight: 1.0
    enabled: true

logic_expression: "C1"

io_outputs:
  - name: "合格指示"
    io_type: "digital"
    address: "DO1"
    ok_value: true
    ng_value: false
    enabled: true
```

### 示例2：多条件综合判断

```yaml
conditions:
  - name: "尺寸检查"
    source_plugin: "geometry_measurement"
    source_key: "length_mm"
    operator: "IN_RANGE"
    target_value: [24.5, 25.5]
    weight: 1.0
    enabled: true
    
  - name: "表面质量"
    source_plugin: "surface_inspection"
    source_key: "defect_count"
    operator: "=="
    target_value: 0
    weight: 0.8
    enabled: true
    
  - name: "模板匹配"
    source_plugin: "template_matching"
    source_key: "match_score"
    operator: ">="
    target_value: 0.9
    weight: 0.6
    enabled: true

# 尺寸必须合格，且表面质量或模板匹配其中一个合格
logic_expression: "C1 AND (C2 OR C3)"
```

### 示例3：分级判断

```yaml
conditions:
  - name: "尺寸优秀"
    source_plugin: "geometry_measurement"
    source_key: "length_mm"
    operator: "IN_RANGE"
    target_value: [24.8, 25.2]  # 严格范围
    weight: 1.0
    enabled: true
    
  - name: "尺寸合格"
    source_plugin: "geometry_measurement"
    source_key: "length_mm"
    operator: "IN_RANGE"
    target_value: [24.5, 25.5]  # 宽松范围
    weight: 1.0
    enabled: true

# 优秀为OK，合格但不优秀为WARN，不合格为NG
logic_expression: "C1 ? 'OK' : (C2 ? 'WARN' : 'NG')"
```

## 🚨 报警机制

### 连续NG报警
当连续出现指定次数的NG结果时触发报警：

```yaml
ng_threshold: 5  # 连续5次NG触发报警
```

### 连续WARN报警
当连续出现指定次数的WARN结果时触发报警：

```yaml
warn_threshold: 10  # 连续10次WARN触发报警
```

## 📈 统计功能

插件会自动统计以下信息：
- 总检测次数
- OK/NG/WARN/ERROR各类型次数
- 合格率和不合格率
- 最近N次结果（滑动窗口）

## 🔌 IO控制板集成

### 支持的IO类型

1. **数字IO (Digital)**
   - 继电器输出
   - LED指示灯
   - 气缸控制
   - 蜂鸣器

2. **模拟IO (Analog)**
   - 电压信号 (0-10V)
   - 电流信号 (4-20mA)
   - 质量等级信号

3. **网络IO (Network)**
   - Modbus TCP/RTU
   - OPC UA
   - 自定义TCP/UDP协议

### IO控制器配置

需要根据实际使用的IO控制板类型进行配置：

```python
# 在插件初始化时配置IO控制器
io_config = {
    "type": "modbus_tcp",
    "host": "*************",
    "port": 502,
    "unit_id": 1
}
```

## 🛠️ 故障排除

### 常见问题

1. **条件评估失败**
   - 检查数据源插件名称是否正确
   - 检查数据源键名是否存在
   - 检查目标值格式是否正确

2. **逻辑表达式错误**
   - 检查括号是否匹配
   - 检查条件编号是否超出范围
   - 使用表达式验证功能

3. **IO输出无响应**
   - 检查IO控制器连接状态
   - 检查IO地址配置
   - 检查输出值格式

### 调试技巧

1. 启用DEBUG日志级别查看详细信息
2. 使用统计功能监控判断结果
3. 逐个测试条件和IO输出
4. 使用表达式验证功能检查逻辑

## 📝 最佳实践

1. **条件设计**
   - 条件名称要清晰明确
   - 合理设置条件权重
   - 避免过于复杂的逻辑表达式

2. **IO配置**
   - 为每个输出设置清晰的名称
   - 合理分配IO地址
   - 考虑故障安全设计

3. **性能优化**
   - 禁用不必要的条件
   - 合理设置统计窗口大小
   - 避免频繁的IO操作

4. **维护管理**
   - 定期检查统计数据
   - 及时处理报警信息
   - 备份重要配置文件
