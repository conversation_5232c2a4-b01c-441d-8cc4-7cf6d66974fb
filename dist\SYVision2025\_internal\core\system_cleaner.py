#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统清理模块
自动清理临时文件、日志文件、缓存文件等，保持系统运行顺畅
"""

import os
import shutil
import time
import threading
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import json
import psutil

class SystemCleaner:
    """系统清理器"""
    
    def __init__(self, config_file: str = "config/cleaner_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.logger = self._setup_logger()
        self.is_running = False
        self.cleanup_thread = None
        
    def _load_config(self) -> Dict:
        """加载清理配置"""
        default_config = {
            "enabled": True,
            "cleanup_rules": {
                "captured_images": {
                    "enabled": True,
                    "keep_days": 7,
                    "max_files": 1000,
                    "max_size_mb": 500,
                    "path": "captured_images"
                },
                "logs": {
                    "enabled": True,
                    "keep_days": 30,
                    "max_files": 50,
                    "max_file_size_mb": 10,
                    "path": "logs"
                },
                "test_outputs": {
                    "enabled": True,
                    "keep_days": 3,
                    "max_files": 100,
                    "path": "test_outputs"
                },
                "cache": {
                    "enabled": True,
                    "keep_hours": 24,
                    "paths": ["__pycache__", "*.pyc", "*.pyo"]
                },
                "temp_configs": {
                    "enabled": True,
                    "keep_days": 1,
                    "path": "recipe_editor/tmp"
                }
            },
            "schedule": {
                "startup_cleanup": True,
                "daily_cleanup": "02:00",
                "shutdown_cleanup": True,
                "auto_cleanup_on_low_space": True,
                "low_space_threshold_gb": 1.0
            },
            "notifications": {
                "show_cleanup_results": True,
                "log_cleanup_actions": True
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                # 创建默认配置文件
                os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                return default_config
        except Exception as e:
            print(f"[CLEANER] 配置文件加载失败，使用默认配置: {e}")
            return default_config
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('SystemCleaner')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '[%(asctime)s] [CLEANER] %(levelname)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def start_auto_cleanup(self):
        """启动自动清理服务"""
        if not self.config.get("enabled", True):
            self.logger.info("系统清理功能已禁用")
            return
            
        self.is_running = True
        
        # 启动时清理
        if self.config["schedule"].get("startup_cleanup", True):
            self.logger.info("执行启动时清理...")
            self.cleanup_all()
        
        # 启动定时清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_scheduler, daemon=True)
        self.cleanup_thread.start()
        
        self.logger.info("系统清理服务已启动")
    
    def stop_auto_cleanup(self):
        """停止自动清理服务"""
        self.is_running = False
        
        # 关闭时清理
        if self.config["schedule"].get("shutdown_cleanup", True):
            self.logger.info("执行关闭时清理...")
            self.cleanup_all()
        
        self.logger.info("系统清理服务已停止")
    
    def _cleanup_scheduler(self):
        """清理调度器"""
        daily_time = self.config["schedule"].get("daily_cleanup", "02:00")
        
        while self.is_running:
            try:
                current_time = datetime.now().strftime("%H:%M")
                
                # 检查是否到了每日清理时间
                if current_time == daily_time:
                    self.logger.info("执行每日定时清理...")
                    self.cleanup_all()
                    time.sleep(60)  # 避免重复执行
                
                # 检查磁盘空间
                if self.config["schedule"].get("auto_cleanup_on_low_space", True):
                    if self._is_low_disk_space():
                        self.logger.warning("磁盘空间不足，执行紧急清理...")
                        self.cleanup_all(aggressive=True)
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                self.logger.error(f"清理调度器错误: {e}")
                time.sleep(60)
    
    def _is_low_disk_space(self) -> bool:
        """检查磁盘空间是否不足"""
        try:
            threshold_gb = self.config["schedule"].get("low_space_threshold_gb", 1.0)
            free_space_gb = psutil.disk_usage('.').free / (1024**3)
            return free_space_gb < threshold_gb
        except:
            return False
    
    def cleanup_all(self, aggressive: bool = False):
        """执行完整清理"""
        if not self.config.get("enabled", True):
            return
            
        self.logger.info("开始系统清理...")
        start_time = time.time()
        total_freed = 0
        
        rules = self.config["cleanup_rules"]
        
        # 清理捕获的图像
        if rules["captured_images"]["enabled"]:
            freed = self._cleanup_captured_images(aggressive)
            total_freed += freed
        
        # 清理日志文件
        if rules["logs"]["enabled"]:
            freed = self._cleanup_logs(aggressive)
            total_freed += freed
        
        # 清理测试输出
        if rules["test_outputs"]["enabled"]:
            freed = self._cleanup_test_outputs(aggressive)
            total_freed += freed
        
        # 清理缓存文件
        if rules["cache"]["enabled"]:
            freed = self._cleanup_cache()
            total_freed += freed
        
        # 清理临时配置
        if rules["temp_configs"]["enabled"]:
            freed = self._cleanup_temp_configs(aggressive)
            total_freed += freed
        
        elapsed_time = time.time() - start_time
        self.logger.info(f"清理完成，释放空间: {total_freed/1024/1024:.1f}MB，耗时: {elapsed_time:.1f}秒")
        
        if self.config["notifications"].get("show_cleanup_results", True):
            self._show_cleanup_notification(total_freed, elapsed_time)
    
    def _cleanup_captured_images(self, aggressive: bool = False) -> int:
        """清理捕获的图像文件"""
        rules = self.config["cleanup_rules"]["captured_images"]
        path = Path(rules["path"])
        
        if not path.exists():
            return 0
        
        freed_bytes = 0
        keep_days = rules["keep_days"] if not aggressive else 1
        cutoff_time = datetime.now() - timedelta(days=keep_days)
        
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    # 检查文件时间
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_time:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        freed_bytes += file_size
                        self.logger.debug(f"删除过期图像: {file_path}")
            
            self.logger.info(f"清理捕获图像完成，释放: {freed_bytes/1024/1024:.1f}MB")
        except Exception as e:
            self.logger.error(f"清理捕获图像失败: {e}")
        
        return freed_bytes
    
    def _cleanup_logs(self, aggressive: bool = False) -> int:
        """清理日志文件"""
        rules = self.config["cleanup_rules"]["logs"]
        path = Path(rules["path"])
        
        if not path.exists():
            return 0
        
        freed_bytes = 0
        keep_days = rules["keep_days"] if not aggressive else 7
        cutoff_time = datetime.now() - timedelta(days=keep_days)
        
        try:
            log_files = list(path.glob("*.log*"))
            
            # 按修改时间排序，保留最新的
            log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for i, file_path in enumerate(log_files):
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                
                # 保留最新的几个文件，删除过期的
                if file_time < cutoff_time or (aggressive and i > 10):
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    freed_bytes += file_size
                    self.logger.debug(f"删除过期日志: {file_path}")
            
            self.logger.info(f"清理日志文件完成，释放: {freed_bytes/1024/1024:.1f}MB")
        except Exception as e:
            self.logger.error(f"清理日志文件失败: {e}")
        
        return freed_bytes
    
    def _cleanup_test_outputs(self, aggressive: bool = False) -> int:
        """清理测试输出文件"""
        rules = self.config["cleanup_rules"]["test_outputs"]
        path = Path(rules["path"])
        
        if not path.exists():
            return 0
        
        freed_bytes = 0
        keep_days = rules["keep_days"] if not aggressive else 1
        cutoff_time = datetime.now() - timedelta(days=keep_days)
        
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_time:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        freed_bytes += file_size
                        self.logger.debug(f"删除测试输出: {file_path}")
            
            self.logger.info(f"清理测试输出完成，释放: {freed_bytes/1024/1024:.1f}MB")
        except Exception as e:
            self.logger.error(f"清理测试输出失败: {e}")
        
        return freed_bytes
    
    def _cleanup_cache(self) -> int:
        """清理缓存文件"""
        freed_bytes = 0
        
        try:
            # 清理Python缓存
            for cache_dir in Path('.').rglob('__pycache__'):
                if cache_dir.is_dir():
                    dir_size = sum(f.stat().st_size for f in cache_dir.rglob('*') if f.is_file())
                    shutil.rmtree(cache_dir)
                    freed_bytes += dir_size
                    self.logger.debug(f"删除缓存目录: {cache_dir}")
            
            # 清理.pyc文件
            for pyc_file in Path('.').rglob('*.pyc'):
                file_size = pyc_file.stat().st_size
                pyc_file.unlink()
                freed_bytes += file_size
            
            self.logger.info(f"清理缓存文件完成，释放: {freed_bytes/1024/1024:.1f}MB")
        except Exception as e:
            self.logger.error(f"清理缓存文件失败: {e}")
        
        return freed_bytes
    
    def _cleanup_temp_configs(self, aggressive: bool = False) -> int:
        """清理临时配置文件"""
        rules = self.config["cleanup_rules"]["temp_configs"]
        path = Path(rules["path"])
        
        if not path.exists():
            return 0
        
        freed_bytes = 0
        keep_days = rules["keep_days"] if not aggressive else 0
        cutoff_time = datetime.now() - timedelta(days=keep_days)
        
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_time:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        freed_bytes += file_size
                        self.logger.debug(f"删除临时配置: {file_path}")
            
            self.logger.info(f"清理临时配置完成，释放: {freed_bytes/1024/1024:.1f}MB")
        except Exception as e:
            self.logger.error(f"清理临时配置失败: {e}")
        
        return freed_bytes
    
    def _show_cleanup_notification(self, freed_bytes: int, elapsed_time: float):
        """显示清理结果通知"""
        # 这里可以集成到主界面的通知系统
        message = f"系统清理完成\n释放空间: {freed_bytes/1024/1024:.1f}MB\n耗时: {elapsed_time:.1f}秒"
        print(f"[CLEANER] {message}")
    
    def manual_cleanup(self):
        """手动触发清理"""
        self.logger.info("手动触发系统清理")
        self.cleanup_all()
    
    def get_cleanup_status(self) -> Dict:
        """获取清理状态信息"""
        return {
            "enabled": self.config.get("enabled", True),
            "is_running": self.is_running,
            "last_cleanup": getattr(self, 'last_cleanup_time', None),
            "config": self.config
        }


# 全局清理器实例
_system_cleaner = None

def get_system_cleaner() -> SystemCleaner:
    """获取系统清理器实例"""
    global _system_cleaner
    if _system_cleaner is None:
        _system_cleaner = SystemCleaner()
    return _system_cleaner

def start_system_cleaner():
    """启动系统清理服务"""
    cleaner = get_system_cleaner()
    cleaner.start_auto_cleanup()

def stop_system_cleaner():
    """停止系统清理服务"""
    cleaner = get_system_cleaner()
    cleaner.stop_auto_cleanup()
