"""
:mod:`torch.optim` is a package implementing various optimization algorithms.

Most commonly used methods are already supported, and the interface is general
enough, so that more sophisticated ones can also be easily integrated in the
future.
"""

from . import lr_scheduler, swa_utils
from .adadelta import Adadel<PERSON>
from .adagrad import Adagrad
from .adam import Adam
from .adamax import Adamax
from .adamw import Adam<PERSON>
from .asgd import ASGD
from .lbfgs import LBFGS
from .nadam import NAdam
from .optimizer import Optimizer
from .radam import RAdam
from .rmsprop import RMSprop
from .rprop import Rprop
from .sgd import SGD
from .sparse_adam import SparseAdam

del adadelta  # type: ignore[name-defined] # noqa: F821
del adagrad  # type: ignore[name-defined] # noqa: F821
del adam  # type: ignore[name-defined] # noqa: F821
del adamw  # type: ignore[name-defined] # noqa: F821
del sparse_adam  # type: ignore[name-defined] # noqa: F821
del adamax  # type: ignore[name-defined] # noqa: F821
del asgd  # type: ignore[name-defined] # noqa: F821
del sgd  # type: ignore[name-defined] # noqa: F821
del radam  # type: ignore[name-defined] # noqa: F821
del rprop  # type: ignore[name-defined] # noqa: F821
del rmsprop  # type: ignore[name-defined] # noqa: F821
del optimizer  # type: ignore[name-defined] # noqa: F821
del nadam  # type: ignore[name-defined] # noqa: F821
del lbfgs  # type: ignore[name-defined] # noqa: F821
