"""Pipeline plugin wrapper for ContourProcessor."""
from __future__ import annotations
from typing import Dict, Any
import numpy as np

from plugins.plugin_base import PluginBase
from plugins.core.image_process.contour import ContourProcessor

__all__ = ["Contour"]

import time

class ContourPlugin(PluginBase):
    name = "contour"
    label = "轮廓检测"
    params = {
        "mode": "external",
        "min_area": 10,
        "max_area": 1_000_000,
        "min_perim": 0,
        "max_perim": 10_000_000,
        "draw_rect": True,
        "draw_center": False,
    }

    def __init__(self, **params):
        super().__init__(**params)
        valid_params = {k: v for k, v in self.params.items() if k in ['mode', 'min_area', 'max_area', 'min_perim', 'max_perim', 'draw_rect', 'draw_center']}
        self._proc = ContourProcessor(**valid_params)

    def setup(self, params):
        valid_params = {k: v for k, v in params.items() if k != 'pose'}
        self._proc = ContourProcessor(**valid_params)

    def process(self, img, ctx):
        start_time = time.time()  # 记录开始时间
        status = "success"
        
        try:
            if img is None:
                return img, ctx
            res = self._proc.process(img)
            
            # 绘制轮廓
            if 'contours' in res:
                cv2.drawContours(res["output"], res['contours'], -1, (0, 255, 0), 2)
            
            # 绘制中心点（如果有）
            stats = res.get("stats", [])
            for stat in stats:
                if 'center' in stat:
                    center = stat['center']
                    cv2.circle(res["output"], (int(center[0]), int(center[1])), 5, (0, 0, 255), -1)
            
            ctx[self.name] = {
                "stats": stats,
            }
        except Exception as e:
            status = f"error: {str(e)}"
        
        end_time = time.time()  # 记录结束时间
        elapsed_time = end_time - start_time  # 计算运行时间
        
        # 更新 ctx 以包含状态和运行时间
        ctx[self.name] = {
            **ctx.get(self.name, {}),
            "status": status,
            "elapsed_time": elapsed_time,
        }
        
        return res["output"], ctx

    # ---------------- UI -----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, preview_img: 'np.ndarray | None' = None):
        import importlib, tkinter as tk
        mod = importlib.import_module('plugins.ui.image_process.contour_ui')
        if hasattr(mod, 'ContourFrame'):
            win = tk.Toplevel(master)
            frame = mod.ContourFrame(win)
            frame.pack(fill='both', expand=True)
            frame.set_params(params)

            # 加载预览图像
            if preview_img is not None:
                try:
                    frame.img_src = preview_img.copy()
                    if hasattr(frame, '_show_image') and hasattr(frame, 'canvas_src'):
                        frame._show_image(frame.canvas_src, frame.img_src, is_src=True)
                except Exception:
                    pass

            def _ok():
                p = frame.get_params()
                on_change(p)
                win.destroy()
            tk.Button(win, text='确定', command=_ok).pack(pady=2)
        else:
            from tkinter import messagebox
            messagebox.showerror('错误', '未找到 ContourFrame')


# instantiate to register plugin on import
_ = ContourPlugin()