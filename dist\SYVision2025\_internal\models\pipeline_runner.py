"""Runtime executor for a plugin pipeline defined in YAML."""
from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, List, Tuple

import yaml
import logging
from PIL import Image
import cv2
import numpy as np

from pipeline.plugin_base import PluginBase, get_plugin_registry


class PipelineRunner:
    _logger = logging.getLogger("PipelineRunner")
    """Loads plugin pipeline YAML and executes it on images."""

    def __init__(self, yaml_path: str | Path | None = None, *, steps: List[Dict[str, Any]] | None = None, workstation_path: str | Path | None = None):
        if steps is not None:
            self.steps = steps
            self.yaml_path = Path("<in-memory>")
            # 如果提供了工位路径，设置环境变量
            if workstation_path:
                import os
                os.environ['CURRENT_WORKSTATION_PATH'] = str(workstation_path)
                print(f"[PipelineRunner] 设置工位路径环境变量: {workstation_path}")
        else:
            if yaml_path is None:
                raise ValueError("Either yaml_path or steps must be provided")
            self.yaml_path = Path(yaml_path)
            self.steps = self._load_yaml()

            # 设置当前工位路径环境变量，供插件使用
            import os
            # 设置工位yaml文件的路径，插件可以从中读取配置
            os.environ['CURRENT_WORKSTATION_PATH'] = str(self.yaml_path)
            print(f"[PipelineRunner] 设置工位配置文件路径: {self.yaml_path}")

        self.registry = get_plugin_registry()
        # pre-instantiate plugins for perf; key: step idx
        self._instances: Dict[int, PluginBase] = {}

    # ------------------------------------------------------------------
    def _load_yaml(self) -> List[Dict[str, Any]]:
        if not self.yaml_path.is_file():
            raise FileNotFoundError(self.yaml_path)
        data = yaml.safe_load(self.yaml_path.read_text(encoding="utf-8")) or []
        if not isinstance(data, list):
            raise ValueError("Pipeline YAML must be a list of steps")
        return data

    # ------------------------------------------------------------------
    def _get_instance(self, idx: int, step_cfg: Dict[str, Any]) -> PluginBase:
        if idx in self._instances:
            return self._instances[idx]

        # 兼容旧格式：{'plugin_name': {...}}
        if "name" in step_cfg:
            name: str = step_cfg["name"]
            params: Dict[str, Any] = step_cfg.get("params", {})
        else:
            # 取第一对键值作为名称与参数
            if len(step_cfg) != 1:
                raise ValueError("Invalid step format, expect a single-key mapping or 'name' field")
            name, params = next(iter(step_cfg.items()))
            if params is None:
                params = {}

        plugin_cls = self.registry.get(name)
        if plugin_cls is None:
            raise KeyError(f"Plugin not registered: {name}")

        instance = plugin_cls(**params)
        self._instances[idx] = instance
        return instance

    # ------------------------------------------------------------------
    def process(self, img) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Run the pipeline and return (output_image, context)."""
        import time
        start_time = time.time()  # 记录管道开始时间

        # ---- 保证输入为 numpy.ndarray ----
        if isinstance(img, Image.Image):
            img = cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)
        ctx: Dict[str, Any] = {}
        cur = img
        acquire_names = {"image_acquire", "image_load"}
        for idx, step in enumerate(self.steps):
            in_shape = None if cur is None else (getattr(cur, 'shape', None) or getattr(cur, 'size', None))
            self._logger.debug(f"[Step {idx}] before plugin, name=? input={in_shape}")
            # 解析插件名
            if "name" in step:
                _name = step["name"]
            else:
                _name, _ = next(iter(step.items()))
            # 如果外部已提供图像且首步就是采图插件，则跳过
            if idx == 0 and cur is not None and isinstance(cur, np.ndarray) and _name in acquire_names:
                continue
            plugin = self._get_instance(idx, step)
            cur, ctx = plugin.process(cur, ctx)  # type: ignore[arg-type]
            out_shape = None if cur is None else (getattr(cur, 'shape', None) or getattr(cur, 'size', None))
            self._logger.debug(f"[Step {idx}] after plugin {plugin.name}, output={out_shape}, ctx_keys={list(ctx.keys())}")

        # 记录总检测耗时
        end_time = time.time()
        detection_time = end_time - start_time
        ctx['detection_time'] = detection_time

        return cur, ctx
