"""Pipeline plugin wrapper for TemplateMatcher."""
from __future__ import annotations
from typing import Dict, Any
from pathlib import Path
import cv2
from plugins.plugin_base import PluginBase
from plugins.core.image_process.template_match import TemplateMatcher


class TemplateMatch(PluginBase):
    """模板匹配流水线插件"""

    name = "template_match"
    label = "模板匹配"

    params: Dict[str, Any] = {
        "debug_draw": True,
        "method": "TM_CCOEFF_NORMED",   # 匹配方法
        "min_conf": 0.7,                 # 置信度阈值
        "template_file": "",            # 模板路径
        "expand_ratio": 20.0,            # ROI 外扩倍率（>1 放大）
    }

    param_labels = {
        "method": "方法",
        "min_conf": "阈值",
        "template_file": "模板文件",
        "expand_ratio": "外扩倍率",
    }

    def __init__(self, **params):
        super().__init__(**params)
        # 若调用方未显式传入 expand_ratio, 则从 default.yml 读取
        if "expand_ratio" not in params:
            import yaml, pathlib
            cfg_path = pathlib.Path(__file__).resolve().parents[3] / "configs" / "template_match" / "default.yml"
            try:
                yml_val = yaml.safe_load(cfg_path.read_text(encoding="utf-8")).get("expand_ratio", 1.0)
                self.params["expand_ratio"] = yml_val
            except Exception:
                pass
        self._matcher = TemplateMatcher()
        self.setup(self.params)

    # -------------------------------------------------------------
    def setup(self, params: Dict[str, Any]):
        self._matcher = TemplateMatcher(method=params["method"], min_conf=float(params["min_conf"]))
        tpl_path = params.get("template_file", "")
        if tpl_path:
            try:
                self._matcher.load_template(tpl_path)
            except Exception:
                pass

    # -------------------------------------------------------------
    def process(self, img, ctx):
        if img is None:
            # 在流程预览环境下，如果没有上游图像，创建一个虚拟图像用于预览
            import numpy as np
            img = np.zeros((480, 640, 3), dtype=np.uint8)  # 创建黑色虚拟图像
        # 计算外扩比例
        try:
            val = float(self.params.get("expand_ratio", 1.0) or 1.0)
            expand_ratio = val/100 + 1 if val > 3 else val  # >3 视作百分比，如20 -> 1.2
        except Exception:
            expand_ratio = 1.0

        try:
            # 确保输入图像有效
            if not hasattr(img, 'ndim'):
                return img, ctx
            # 决定是否让匹配器绘制
            has_expand = abs(expand_ratio - 1.0) > 1e-3
            is_shape_match = self._matcher.method_name == self._matcher.SHAPE_METHOD
            draw_in_matcher = not (is_shape_match and has_expand)
            res = self._matcher.match(img, draw=draw_in_matcher)
        except Exception as e:
            # 如果模板匹配失败，返回原图像，并记录错误信息
            print(f"Template match error: {e}")
            return img, ctx

        # --------- 外扩 ROI 处理 ---------
        if abs(expand_ratio - 1.0) > 1e-3 and "corners" in res.get("info", {}):
            corners = res["info"].get("corners")
            if corners and len(corners) == 4:
                cx, cy = res["info"].get("center", (0, 0))
                exp_corners = [
                    (int(cx + (x - cx) * expand_ratio), int(cy + (y - cy) * expand_ratio))
                    for x, y in corners
                ]
                res["info"]["corners"] = exp_corners
            # 调试输出
            print(f"[TM] expand_ratio={expand_ratio:.2f}, corners={res['info'].get('corners')}")
        # 统一绘制处理：保证用于显示的图像为 BGR，且不影响后续算子输入
        if draw_in_matcher:
            # 匹配器已经绘制了，直接使用其输出图像
            vis_img = res.get('img_out', img).copy()
        else:
            # 匹配器没有绘制，我们需要自己绘制
            vis_img = img.copy() if img.ndim == 3 else cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)

        # 可视化模板 ROI + 中心点 + 文本信息
        if bool(self.params.get("debug_draw", False)):
            import numpy as _np
            info = res["info"]
            if not draw_in_matcher and "corners" in info:
                # 只有当匹配器没有绘制时，我们才绘制（使用外扩后的角点）
                corners_to_draw = info["corners"]  # 这里已经是外扩后的角点
                pts = _np.array(corners_to_draw, dtype=int).reshape(-1, 1, 2)
                color = (0, 255, 0) if info.get("ok", False) else (0, 0, 255)
                cv2.polylines(vis_img, [pts], isClosed=True, color=color, thickness=2)
                cx, cy = map(int, info["center"])
                cv2.drawMarker(vis_img, (cx, cy), color, markerType=cv2.MARKER_CROSS, thickness=2)
            # 叠加文字：score、angle
            txt = f"s:{info['score']:.2f}  ang:{info['angle']:.1f}"
            cv2.putText(vis_img, txt, (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1, cv2.LINE_AA)
        ctx[self.name] = res["info"]
        # 同步到统一键名，以便几何工具读取
        ctx["pose"] = res["info"]
        if "corners" in res["info"]:
            ctx.setdefault("roi_dict", {})["template_pose"] = {
                "corners": res["info"]["corners"],
                "center": res["info"].get("center"),
                "angle": res["info"].get("angle"),
            }
        return vis_img, ctx

    # ---------------- UI -----------------
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, *, preview_img: 'np.ndarray | None' = None, **extra):
        import importlib, tkinter as tk
        import traceback
        import os
        from pathlib import Path
        
        try:
            mod = importlib.import_module('plugins.ui.image_process.template_match_ui')
            if hasattr(mod, 'TemplateMatchFrame'):
                win = tk.Toplevel(master)
                # 构造预览数据：兼容两种关键字
                preview_data = None
                if preview_img is not None:
                    print(f"[DEBUG] preview_img type: {type(preview_img)}, shape: {getattr(preview_img, 'shape', 'no shape')}")
                    preview_data = {'img': preview_img}
                elif 'preview' in extra:
                    preview_data = extra.get('preview')
                    print(f"[DEBUG] preview from extra: {type(preview_data)}")
                
                print(f"[DEBUG] Creating TemplateMatchFrame with preview_data: {type(preview_data)}")
                frame = mod.TemplateMatchFrame(win, preview=preview_data)
                # 如果使用全局模板，禁用加载按钮避免路径不一致
                if params.get('_use_global_template'):
                    frame.load_btn.configure(state='disabled')
                                # 若已有模板文件路径，提前写入 frame.tpl_path 以便保存时覆盖
                tpl_file = params.get('template_file')
                if tpl_file:
                    frame.tpl_path = tpl_file
                    # 同时预载入模板以便预览
                    try:
                        frame.matcher.load_template(tpl_file)
                    except Exception:
                        pass
                frame.pack(fill='both', expand=True)
                
                # 将参数同步到 UI
                frame.method_var.set({v:k for k,v in frame.METHOD_CN.items()}.get(params.get('method','TM_CCOEFF_NORMED'),'相关系数'))
                frame.thr_var.set(float(params.get('min_conf',0.7)))
                # 同步 expand_ratio
                if hasattr(frame, 'expand_ratio'):
                    frame.expand_ratio.set(float(params.get('expand_ratio', 1.0)))
                
                # 自动加载模板（若已配置）
                tpl_path = params.get('template_file', '')
                print(f"[DEBUG] Template path: '{tpl_path}', cwd: {os.getcwd()}")
                if tpl_path:
                    abs_path = Path(tpl_path).resolve()
                    print(f"[DEBUG] Resolved template path: {abs_path}, exists: {abs_path.exists()}")
                    if abs_path.exists():
                        try:
                            frame.matcher.load_template(str(abs_path))
                            frame.tpl_path = tpl_path
                            print(f"[DEBUG] Template loaded successfully")
                        except Exception as e:
                            print(f"[DEBUG] Failed to load template: {e}")
                            traceback.print_exc()
                    else:
                        print(f"[DEBUG] Template file not found: {abs_path}")
                
                def _ok():
                    new_params = {
                        'method': frame.METHOD_CN[frame.method_var.get()],
                        'min_conf': frame.thr_var.get(),
                        'template_file': frame.tpl_path or params.get('template_file',''),
                    'expand_ratio': float(frame.expand_ratio.get())
                    }
                    on_change(new_params)
                    win.destroy()
                tk.Button(win, text='确定', command=_ok).pack(pady=2)
            else:
                from tkinter import messagebox
                messagebox.showerror('错误', '未找到 TemplateMatchFrame')
        except Exception as e:
            print(f"[ERROR] Failed to open template match dialog: {e}")
            traceback.print_exc()
            raise

# instantiate to ensure registration when module is imported
_ = TemplateMatch()
