"""
逻辑判断插件 - 视觉检测系统的决策核心
负责根据检测结果进行逻辑判断，输出NG/OK等控制信号到IO控制板

功能特点：
1. 多条件逻辑判断（AND/OR/NOT）
2. 阈值范围检查
3. IO信号输出控制
4. 统计分析和数据记录
5. 可视化判断流程
"""

import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import json
from pathlib import Path

from plugins.plugin_base import PluginBase


class JudgmentResult(Enum):
    """判断结果枚举"""
    OK = "OK"           # 合格
    NG = "NG"           # 不合格  
    WARN = "WARN"       # 警告
    ERROR = "ERROR"     # 错误
    UNKNOWN = "UNKNOWN" # 未知


class LogicOperator(Enum):
    """逻辑操作符"""
    AND = "AND"
    OR = "OR"
    NOT = "NOT"


class ComparisonOperator(Enum):
    """比较操作符"""
    EQUAL = "=="           # 等于
    NOT_EQUAL = "!="       # 不等于
    GREATER = ">"          # 大于
    GREATER_EQUAL = ">="   # 大于等于
    LESS = "<"             # 小于
    LESS_EQUAL = "<="      # 小于等于
    IN_RANGE = "IN_RANGE"  # 在范围内
    OUT_RANGE = "OUT_RANGE" # 超出范围


@dataclass
class JudgmentCondition:
    """判断条件"""
    name: str                           # 条件名称
    source_plugin: str                  # 数据源插件名
    source_key: str                     # 数据源键名
    operator: ComparisonOperator        # 比较操作符
    target_value: Union[float, int, str, List] # 目标值
    weight: float = 1.0                 # 权重
    enabled: bool = True                # 是否启用


@dataclass
class IOOutputConfig:
    """IO输出配置"""
    name: str                    # 输出名称
    io_type: str                # IO类型：digital/analog/network
    address: str                # IO地址
    ok_value: Any               # OK状态输出值
    ng_value: Any               # NG状态输出值
    warn_value: Any = None      # 警告状态输出值
    enabled: bool = True        # 是否启用


class LogicJudgmentPlugin(PluginBase):
    """逻辑判断插件"""
    
    name = "logic_judgment"
    label = "逻辑判断"
    category = "决策控制"
    
    # 默认参数
    params = {
        # 基本设置
        "enable_judgment": True,
        "judgment_mode": "strict",  # strict/tolerant/custom
        "default_result": "UNKNOWN",
        
        # 判断条件（JSON格式存储）
        "conditions": "[]",
        
        # 逻辑组合
        "logic_expression": "",  # 如: "(C1 AND C2) OR C3"
        
        # IO输出配置
        "io_outputs": "[]",
        
        # 统计设置
        "enable_statistics": True,
        "statistics_window": 100,  # 统计窗口大小
        
        # 数据记录
        "enable_logging": True,
        "log_level": "INFO",  # DEBUG/INFO/WARN/ERROR
        
        # 报警设置
        "ng_threshold": 5,      # 连续NG次数报警阈值
        "warn_threshold": 10,   # 连续WARN次数报警阈值
    }
    
    param_labels = {
        "enable_judgment": "启用判断",
        "judgment_mode": "判断模式",
        "default_result": "默认结果",
        "conditions": "判断条件",
        "logic_expression": "逻辑表达式",
        "io_outputs": "IO输出配置",
        "enable_statistics": "启用统计",
        "statistics_window": "统计窗口",
        "enable_logging": "启用日志",
        "log_level": "日志级别",
        "ng_threshold": "NG报警阈值",
        "warn_threshold": "警告报警阈值",
    }
    
    def __init__(self, **params):
        super().__init__(**params)
        
        # 判断条件列表
        self.conditions: List[JudgmentCondition] = []
        
        # IO输出配置列表
        self.io_outputs: List[IOOutputConfig] = []
        
        # 统计数据
        self.statistics = {
            "total_count": 0,
            "ok_count": 0,
            "ng_count": 0,
            "warn_count": 0,
            "error_count": 0,
            "recent_results": [],  # 最近的判断结果
            "pass_rate": 0.0,
            "ng_rate": 0.0,
        }
        
        # 连续计数器
        self.consecutive_counters = {
            "ng": 0,
            "warn": 0,
            "ok": 0,
        }
        
        # IO控制器（需要根据实际硬件实现）
        self.io_controller = None
        
    def setup(self, params: Dict[str, Any]):
        """设置插件参数"""
        # 尝试从工位配置加载参数
        enhanced_params = self._load_workstation_config(params)

        # 解析判断条件
        try:
            conditions_json = enhanced_params.get("conditions", "[]")
            if isinstance(conditions_json, str):
                conditions_data = json.loads(conditions_json)
            else:
                conditions_data = conditions_json

            self.conditions = [
                JudgmentCondition(**cond) for cond in conditions_data
            ]
        except Exception as e:
            print(f"[LOGIC] 解析判断条件失败: {e}")
            self.conditions = []

        # 解析IO输出配置
        try:
            io_outputs_json = enhanced_params.get("io_outputs", "[]")
            if isinstance(io_outputs_json, str):
                io_outputs_data = json.loads(io_outputs_json)
            else:
                io_outputs_data = io_outputs_json

            self.io_outputs = [
                IOOutputConfig(**io_config) for io_config in io_outputs_data
            ]
        except Exception as e:
            print(f"[LOGIC] 解析IO输出配置失败: {e}")
            self.io_outputs = []

        # 初始化IO控制器
        self._init_io_controller()

        print(f"[LOGIC] 逻辑判断插件已初始化: {len(self.conditions)}个条件, {len(self.io_outputs)}个输出")

    def _load_workstation_config(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """从工位配置加载参数，与其他插件保持一致"""
        try:
            import yaml
            import os

            # 方法1: 尝试从环境变量获取当前工位路径
            workstation_path = os.environ.get('CURRENT_WORKSTATION_PATH')
            if workstation_path:
                config_file = Path(workstation_path) / "logic_judgment.yml"
                if config_file.exists():
                    print(f"[LOGIC] 从环境变量找到工位配置: {config_file}")
                    config_data = yaml.safe_load(config_file.read_text(encoding="utf-8"))
                    if config_data:
                        merged_params = base_params.copy()
                        merged_params.update(config_data)
                        return merged_params

            # 方法2: 尝试从当前工作目录找到工位配置文件
            cwd = Path.cwd()
            config_file = cwd / "logic_judgment.yml"
            if config_file.exists():
                print(f"[LOGIC] 从当前目录找到工位配置: {config_file}")
                config_data = yaml.safe_load(config_file.read_text(encoding="utf-8"))
                if config_data:
                    merged_params = base_params.copy()
                    merged_params.update(config_data)
                    return merged_params

            # 方法3: 尝试从全局配置目录加载默认配置
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from path_manager import get_plugin_config_path
                default_config_file = Path(get_plugin_config_path('logic_judgment', 'default.yml'))
            except ImportError:
                global_config_dir = Path(__file__).resolve().parents[1] / 'configs' / 'logic_judgment'
                default_config_file = global_config_dir / 'default.yml'
            if default_config_file.exists():
                print(f"[LOGIC] 从全局配置加载默认配置: {default_config_file}")
                config_data = yaml.safe_load(default_config_file.read_text(encoding="utf-8"))
                if config_data:
                    merged_params = base_params.copy()
                    merged_params.update(config_data)
                    return merged_params

            print(f"[LOGIC] 未找到工位配置，使用基础参数")
            return base_params

        except Exception as e:
            print(f"[ERROR] 加载工位配置失败: {e}")
            return base_params

    def save_config(self, file_path: str = None):
        """保存配置到文件，与其他插件保持一致"""
        try:
            import yaml

            if file_path is None:
                # 创建插件独享的配置目录，与其他插件保持一致
                cfg_dir = Path(__file__).resolve().parents[1] / 'configs' / 'logic_judgment'
                cfg_dir.mkdir(parents=True, exist_ok=True)
                file_path = cfg_dir / 'default.yml'

            # 构建配置数据
            config_data = {
                # 基本设置
                "enable_judgment": self.params.get("enable_judgment", True),
                "judgment_mode": self.params.get("judgment_mode", "strict"),
                "default_result": self.params.get("default_result", "UNKNOWN"),
                "ng_threshold": self.params.get("ng_threshold", 5),
                "warn_threshold": self.params.get("warn_threshold", 10),

                # 逻辑表达式
                "logic_expression": self.params.get("logic_expression", ""),

                # 条件和IO输出
                "conditions": [cond.__dict__ for cond in self.conditions],
                "io_outputs": [io_output.__dict__ for io_output in self.io_outputs],

                # 统计设置
                "enable_statistics": self.params.get("enable_statistics", True),
                "statistics_window": self.params.get("statistics_window", 100),
                "enable_logging": self.params.get("enable_logging", True),
                "log_level": self.params.get("log_level", "INFO"),
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config_data, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ 逻辑判断配置已保存到: {file_path}")

        except Exception as e:
            print(f"❌ 保存逻辑判断配置失败: {e}")

    def load_config(self, file_path: str = None):
        """从文件加载配置，与其他插件保持一致"""
        try:
            import yaml

            if file_path is None:
                # 从插件独享的配置目录加载默认配置，与其他插件保持一致
                cfg_dir = Path(__file__).resolve().parents[1] / 'configs' / 'logic_judgment'
                file_path = cfg_dir / 'default.yml'

                # 如果默认配置文件不存在，先保存当前参数作为默认配置
                if not Path(file_path).exists():
                    self.save_config()
                    return

            with open(file_path, 'r', encoding='utf-8') as f:
                loaded_params = yaml.safe_load(f)

            # 更新参数
            if loaded_params:
                self.params.update(loaded_params)
                # 重新初始化
                self.setup(self.params)

            print(f"✅ 逻辑判断配置已从文件加载: {file_path}")

        except Exception as e:
            print(f"❌ 加载逻辑判断配置失败: {e}")

    def _init_io_controller(self):
        """初始化IO控制器"""
        try:
            # 这里需要根据实际的IO控制板类型进行初始化
            # 例如：Modbus、串口、网络IO等
            # self.io_controller = IOController(config)
            print("[LOGIC] IO控制器初始化完成")
        except Exception as e:
            print(f"[LOGIC] IO控制器初始化失败: {e}")
    
    def process(self, img, ctx):
        """处理函数 - 执行逻辑判断"""
        start_time = time.time()
        
        try:
            # 如果未启用判断，直接返回
            if not self.params.get("enable_judgment", True):
                return img, ctx
            
            # 执行逻辑判断
            judgment_result = self._execute_judgment(ctx)
            
            # 更新统计信息
            self._update_statistics(judgment_result)
            
            # 输出IO控制信号
            self._output_io_signals(judgment_result)
            
            # 记录日志
            self._log_result(judgment_result, ctx)
            
            # 检查报警条件
            self._check_alarms(judgment_result)
            
            # 将结果添加到上下文
            ctx[self.name] = {
                "result": judgment_result.value,
                "statistics": self.statistics.copy(),
                "processing_time": time.time() - start_time,
                "conditions_evaluated": len(self.conditions),
            }
            
            print(f"[LOGIC] 判断结果: {judgment_result.value}, 耗时: {(time.time() - start_time)*1000:.2f}ms")
            
        except Exception as e:
            print(f"[LOGIC] 逻辑判断处理失败: {e}")
            ctx[self.name] = {
                "result": "ERROR",
                "error": str(e),
                "processing_time": time.time() - start_time,
            }
        
        return img, ctx
    
    def _execute_judgment(self, ctx: Dict[str, Any]) -> JudgmentResult:
        """执行逻辑判断"""
        if not self.conditions:
            return JudgmentResult(self.params.get("default_result", "UNKNOWN"))
        
        # 评估每个条件
        condition_results = {}
        for i, condition in enumerate(self.conditions):
            if not condition.enabled:
                continue
                
            result = self._evaluate_condition(condition, ctx)
            condition_results[f"C{i+1}"] = result
            print(f"[LOGIC] 条件 {condition.name}: {result}")
        
        # 执行逻辑表达式
        logic_expression = self.params.get("logic_expression", "")
        if logic_expression and condition_results:
            final_result = self._evaluate_logic_expression(logic_expression, condition_results)
        else:
            # 默认使用AND逻辑
            final_result = all(condition_results.values()) if condition_results else False
        
        # 转换为判断结果
        if final_result:
            return JudgmentResult.OK
        else:
            return JudgmentResult.NG
    
    def _evaluate_condition(self, condition: JudgmentCondition, ctx: Dict[str, Any]) -> bool:
        """评估单个条件"""
        try:
            # 获取数据源值
            source_data = ctx.get(condition.source_plugin, {})
            if isinstance(source_data, dict):
                actual_value = source_data.get(condition.source_key)
            else:
                actual_value = source_data
            
            if actual_value is None:
                print(f"[LOGIC] 警告: 条件 {condition.name} 的数据源值为空")
                return False
            
            # 执行比较操作
            return self._compare_values(actual_value, condition.operator, condition.target_value)
            
        except Exception as e:
            print(f"[LOGIC] 评估条件 {condition.name} 失败: {e}")
            return False
    
    def _compare_values(self, actual: Any, operator: ComparisonOperator, target: Any) -> bool:
        """比较两个值"""
        try:
            if operator == ComparisonOperator.EQUAL:
                return actual == target
            elif operator == ComparisonOperator.NOT_EQUAL:
                return actual != target
            elif operator == ComparisonOperator.GREATER:
                return float(actual) > float(target)
            elif operator == ComparisonOperator.GREATER_EQUAL:
                return float(actual) >= float(target)
            elif operator == ComparisonOperator.LESS:
                return float(actual) < float(target)
            elif operator == ComparisonOperator.LESS_EQUAL:
                return float(actual) <= float(target)
            elif operator == ComparisonOperator.IN_RANGE:
                if isinstance(target, list) and len(target) == 2:
                    return target[0] <= float(actual) <= target[1]
                return False
            elif operator == ComparisonOperator.OUT_RANGE:
                if isinstance(target, list) and len(target) == 2:
                    return not (target[0] <= float(actual) <= target[1])
                return False
            else:
                return False
        except Exception as e:
            print(f"[LOGIC] 值比较失败: {e}")
            return False

    def _evaluate_logic_expression(self, expression: str, condition_results: Dict[str, bool]) -> bool:
        """评估逻辑表达式"""
        try:
            # 简单的逻辑表达式解析器
            # 支持格式: "(C1 AND C2) OR C3", "C1 AND (C2 OR C3)", "NOT C1" 等

            # 替换条件名为实际值
            eval_expression = expression
            for condition_name, result in condition_results.items():
                eval_expression = eval_expression.replace(condition_name, str(result))

            # 替换逻辑操作符
            eval_expression = eval_expression.replace("AND", "and")
            eval_expression = eval_expression.replace("OR", "or")
            eval_expression = eval_expression.replace("NOT", "not")

            # 安全评估表达式
            try:
                result = eval(eval_expression)
                return bool(result)
            except:
                print(f"[LOGIC] 逻辑表达式评估失败: {expression}")
                return False

        except Exception as e:
            print(f"[LOGIC] 逻辑表达式处理失败: {e}")
            return False

    def _update_statistics(self, result: JudgmentResult):
        """更新统计信息"""
        self.statistics["total_count"] += 1

        # 更新各类型计数
        if result == JudgmentResult.OK:
            self.statistics["ok_count"] += 1
            self.consecutive_counters["ok"] += 1
            self.consecutive_counters["ng"] = 0
            self.consecutive_counters["warn"] = 0
        elif result == JudgmentResult.NG:
            self.statistics["ng_count"] += 1
            self.consecutive_counters["ng"] += 1
            self.consecutive_counters["ok"] = 0
            self.consecutive_counters["warn"] = 0
        elif result == JudgmentResult.WARN:
            self.statistics["warn_count"] += 1
            self.consecutive_counters["warn"] += 1
            self.consecutive_counters["ok"] = 0
            self.consecutive_counters["ng"] = 0
        elif result == JudgmentResult.ERROR:
            self.statistics["error_count"] += 1

        # 更新最近结果列表
        window_size = self.params.get("statistics_window", 100)
        self.statistics["recent_results"].append(result.value)
        if len(self.statistics["recent_results"]) > window_size:
            self.statistics["recent_results"].pop(0)

        # 计算合格率
        total = self.statistics["total_count"]
        if total > 0:
            self.statistics["pass_rate"] = self.statistics["ok_count"] / total * 100
            self.statistics["ng_rate"] = self.statistics["ng_count"] / total * 100

    def _output_io_signals(self, result: JudgmentResult):
        """输出IO控制信号"""
        if not self.io_controller or not self.io_outputs:
            return

        try:
            for io_config in self.io_outputs:
                if not io_config.enabled:
                    continue

                # 根据判断结果选择输出值
                if result == JudgmentResult.OK:
                    output_value = io_config.ok_value
                elif result == JudgmentResult.NG:
                    output_value = io_config.ng_value
                elif result == JudgmentResult.WARN and io_config.warn_value is not None:
                    output_value = io_config.warn_value
                else:
                    output_value = io_config.ng_value  # 默认为NG

                # 输出到IO控制板
                self._write_io_output(io_config, output_value)

        except Exception as e:
            print(f"[LOGIC] IO信号输出失败: {e}")

    def _write_io_output(self, io_config: IOOutputConfig, value: Any):
        """写入IO输出"""
        try:
            if io_config.io_type == "digital":
                # 数字IO输出
                self.io_controller.write_digital(io_config.address, bool(value))
            elif io_config.io_type == "analog":
                # 模拟IO输出
                self.io_controller.write_analog(io_config.address, float(value))
            elif io_config.io_type == "network":
                # 网络IO输出
                self.io_controller.write_network(io_config.address, value)

            print(f"[LOGIC] IO输出 {io_config.name}: {value}")

        except Exception as e:
            print(f"[LOGIC] IO输出 {io_config.name} 失败: {e}")

    def _log_result(self, result: JudgmentResult, ctx: Dict[str, Any]):
        """记录判断结果日志"""
        if not self.params.get("enable_logging", True):
            return

        try:
            log_level = self.params.get("log_level", "INFO")

            # 构建日志信息
            log_data = {
                "timestamp": time.time(),
                "result": result.value,
                "conditions": len(self.conditions),
                "statistics": self.statistics.copy(),
            }

            # 根据日志级别决定是否记录详细信息
            if log_level == "DEBUG":
                log_data["context"] = ctx

            # 这里可以写入到文件、数据库或发送到日志服务器
            print(f"[LOGIC] 记录日志: {result.value}")

        except Exception as e:
            print(f"[LOGIC] 日志记录失败: {e}")

    def _check_alarms(self, result: JudgmentResult):
        """检查报警条件"""
        try:
            ng_threshold = self.params.get("ng_threshold", 5)
            warn_threshold = self.params.get("warn_threshold", 10)

            # 检查连续NG报警
            if self.consecutive_counters["ng"] >= ng_threshold:
                self._trigger_alarm("NG", f"连续{self.consecutive_counters['ng']}次NG")

            # 检查连续WARN报警
            if self.consecutive_counters["warn"] >= warn_threshold:
                self._trigger_alarm("WARN", f"连续{self.consecutive_counters['warn']}次警告")

        except Exception as e:
            print(f"[LOGIC] 报警检查失败: {e}")

    def _trigger_alarm(self, alarm_type: str, message: str):
        """触发报警"""
        try:
            alarm_data = {
                "type": alarm_type,
                "message": message,
                "timestamp": time.time(),
                "statistics": self.statistics.copy(),
            }

            print(f"[LOGIC] 🚨 报警触发: {alarm_type} - {message}")

            # 这里可以发送报警通知：邮件、短信、声光报警等
            # self._send_alarm_notification(alarm_data)

        except Exception as e:
            print(f"[LOGIC] 报警触发失败: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.statistics.copy()

    def reset_statistics(self):
        """重置统计信息"""
        self.statistics = {
            "total_count": 0,
            "ok_count": 0,
            "ng_count": 0,
            "warn_count": 0,
            "error_count": 0,
            "recent_results": [],
            "pass_rate": 0.0,
            "ng_rate": 0.0,
        }

        self.consecutive_counters = {
            "ng": 0,
            "warn": 0,
            "ok": 0,
        }

        print("[LOGIC] 统计信息已重置")

    # -------------------------------------------------- UI对话框
    @staticmethod
    def open_param_dialog(master, params: Dict[str, Any], on_change, **extra):
        """打开参数配置对话框"""
        try:
            import importlib
            ui_module = importlib.import_module("plugins.ui.logic_judgment_ui")
            ui_class = getattr(ui_module, "LogicJudgmentFrame")

            import tkinter as tk
            dialog = tk.Toplevel(master)
            dialog.title("逻辑判断配置")
            dialog.geometry("800x600")

            frame = ui_class(dialog, params=params)
            frame.pack(fill=tk.BOTH, expand=True)

            def on_save():
                new_params = frame.get_params()
                on_change(new_params)
                dialog.destroy()

            # 添加保存按钮
            import tkinter.ttk as ttk
            btn_frame = ttk.Frame(dialog)
            btn_frame.pack(fill=tk.X, pady=5)

            ttk.Button(btn_frame, text="保存", command=on_save).pack(side=tk.RIGHT, padx=5)
            ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        except ImportError:
            import tkinter.messagebox as messagebox
            messagebox.showerror("错误", "未找到逻辑判断UI模块")
        except Exception as e:
            import tkinter.messagebox as messagebox
            messagebox.showerror("错误", f"打开配置对话框失败: {str(e)}")


# 示例IO控制器接口（需要根据实际硬件实现）
class IOController:
    """IO控制器基类"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connected = False

    def connect(self) -> bool:
        """连接IO控制板"""
        # 实现具体的连接逻辑
        self.connected = True
        return True

    def disconnect(self):
        """断开连接"""
        self.connected = False

    def write_digital(self, address: str, value: bool):
        """写入数字IO"""
        if not self.connected:
            raise Exception("IO控制器未连接")
        # 实现数字IO写入
        pass

    def write_analog(self, address: str, value: float):
        """写入模拟IO"""
        if not self.connected:
            raise Exception("IO控制器未连接")
        # 实现模拟IO写入
        pass

    def write_network(self, address: str, value: Any):
        """网络IO输出"""
        if not self.connected:
            raise Exception("IO控制器未连接")
        # 实现网络IO输出
        pass
