"""Core edge detection processor."""
from __future__ import annotations
import cv2
import numpy as np
from typing import Dict, Any

__all__ = ["EdgeProcessor"]

class EdgeProcessor:
    name = "ImageEdge"

    def __init__(self, *, mode: str = "canny", thresh1: int = 100, thresh2: int = 200,
                 sobel_dir: str = "xy", kernel_size: int = 3):
        self.mode = mode.lower()  # canny, sobel, laplacian
        self.thresh1 = int(thresh1)
        self.thresh2 = int(thresh2)
        self.sobel_dir = sobel_dir.lower()  # x, y, xy
        self.kernel_size = max(1, int(kernel_size) | 1)  # ensure odd

    # ------------------------------------------------------------------
    def process(self, img: np.ndarray, **override) -> Dict[str, Any]:
        if override:
            for k, v in override.items():
                if hasattr(self, k):
                    setattr(self, k, v)
        gray = img
        if gray.ndim == 3:
            gray = cv2.cvtColor(gray, cv2.COLOR_BGR2GRAY)
        m = self.mode
        if m == "canny":
            dst = cv2.Canny(gray, self.thresh1, self.thresh2)
        elif m == "sobel":
            k = self.kernel_size
            dx = 1 if "x" in self.sobel_dir else 0
            dy = 1 if "y" in self.sobel_dir else 0
            sob = cv2.Sobel(gray, cv2.CV_16S, dx, dy, ksize=k)
            dst = cv2.convertScaleAbs(sob)
        elif m == "laplacian":
            k = self.kernel_size
            lap = cv2.Laplacian(gray, cv2.CV_16S, ksize=k)
            dst = cv2.convertScaleAbs(lap)
        else:
            dst = gray.copy()
        return {"output": dst}
