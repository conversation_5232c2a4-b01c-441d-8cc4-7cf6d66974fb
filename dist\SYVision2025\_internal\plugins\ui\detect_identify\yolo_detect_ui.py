"""YOLO Detect Tkinter UI
Similar layout style to template_match_ui.
"""
import os, sys
from pathlib import Path
from ..geometry_tools._recipe_sync import update_mm_in_recipe
sys.path.append(str(Path(__file__).resolve().parents[3]))  # 三级上跳加入项目根
import cv2
import numpy as np

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import numpy as np
from typing import Optional
import yaml
import time

from plugins.DetectIdentify.yolo_detect import load_model, YOLODetector

PREVIEW_MAX = 600
CFG_DIR = Path(__file__).resolve().parents[3]/'configs'/ 'yolo'
CFG_DIR.mkdir(parents=True, exist_ok=True)
DEFAULT_CFG = CFG_DIR/'default.yml'

class YOLODetectFrame(ttk.Frame):
    def __init__(self, master, *, recipe_path: Optional[str] = None):
        super().__init__(master)
        self.model_path = tk.StringVar()
        self.conf_var = tk.DoubleVar(value=0.25)
        self.device_var = tk.StringVar(value='cpu')
        self.img_src = None
        self.img_out = None
        self.detector: YOLODetector | None = None
        # 记录配方路径用于同步 mm_per_px
        self._recipe_path: Optional[Path] = Path(recipe_path) if recipe_path else None
        self.use_roi_var = tk.BooleanVar(value=False)
        self.tpl_thresh = tk.DoubleVar(value=0.8)
        self.expand_var = tk.DoubleVar(value=10)
        self.mm_per_px = tk.DoubleVar(value=0.01)  # 0.01 mm per pixel default
        self.ng_conf_th = tk.DoubleVar(value=0.5)
        self.ng_size_th = tk.DoubleVar(value=0.3)  # mm
        self.min_contour_area = tk.DoubleVar(value=100)  # 最小轮廓面积（像素）

        # ROI绘制相关变量
        self.is_drawing_roi = False
        self.roi_start = None
        self.roi_rect_id = None
        self.roi_points = []

        self._build_ui()

    # ---------------- UI -----------------
    def _build_ui(self):
        pfrm = ttk.LabelFrame(self, text='参数'); pfrm.grid(row=0,column=0,sticky='ns')
        ttk.Button(pfrm, text='加载模型', command=self._load_model).grid(row=0,column=0,columnspan=2,sticky='we')
        ttk.Entry(pfrm,textvariable=self.model_path,width=28).grid(row=1,column=0,columnspan=2,sticky='we')
        # 设备选择仅适用于ONNX(CPU/GPU), 暂留界面但未传递
        ttk.Label(pfrm,text='设备').grid(row=2,column=0,sticky='e')
        ttk.OptionMenu(pfrm,self.device_var,'cpu','cpu','cuda').grid(row=2,column=1,sticky='w')
        ttk.Button(pfrm, text='加载图片', command=self._load_image).grid(row=3,column=0,columnspan=2,sticky='we')
        ttk.Label(pfrm,text='置信度').grid(row=4,column=0,sticky='e')
        ttk.Entry(pfrm,textvariable=self.conf_var,width=6).grid(row=4,column=1)
        # 模板 ROI
        ttk.Checkbutton(pfrm,text='使用模板ROI',variable=self.use_roi_var).grid(row=5,column=0,columnspan=2,sticky='w')
        roi_frame = ttk.Frame(pfrm)
        roi_frame.grid(row=6,column=0,columnspan=2,sticky='we')
        ttk.Button(roi_frame,text='制作模板',command=self._make_template).grid(row=0,column=0,sticky='we')
        ttk.Button(roi_frame,text='绘制ROI',command=self._draw_roi).grid(row=0,column=1,sticky='we')
        roi_frame.columnconfigure(0, weight=1)
        roi_frame.columnconfigure(1, weight=1)
        ttk.Label(pfrm,text='ROI阈值').grid(row=7,column=0,sticky='e')
        ttk.Entry(pfrm,textvariable=self.tpl_thresh,width=6).grid(row=7,column=1)
        ttk.Label(pfrm,text='外扩%').grid(row=8,column=0,sticky='e')
        ttk.Entry(pfrm,textvariable=self.expand_var,width=6).grid(row=8,column=1)
        ttk.Label(pfrm,text='mm/px').grid(row=9,column=0,sticky='e')
        ttk.Entry(pfrm,textvariable=self.mm_per_px,width=6).grid(row=9,column=1)
        ttk.Button(pfrm,text='运行',command=self._run).grid(row=10,column=0,columnspan=2,sticky='we')
        ttk.Label(pfrm,text='NG置信度>=').grid(row=12,column=0,sticky='e')
        ttk.Entry(pfrm,textvariable=self.ng_conf_th,width=6).grid(row=12,column=1)
        ttk.Label(pfrm,text='NG尺寸>=').grid(row=13,column=0,sticky='e')
        ttk.Entry(pfrm,textvariable=self.ng_size_th,width=6).grid(row=13,column=1)
        ttk.Label(pfrm,text='最小轮廓面积').grid(row=14,column=0,sticky='e')
        ttk.Entry(pfrm,textvariable=self.min_contour_area,width=6).grid(row=14,column=1)
        ttk.Button(pfrm,text='保存配置',command=self._save_cfg).grid(row=15,column=0,sticky='we')
        ttk.Button(pfrm,text='加载配置',command=self._load_cfg).grid(row=15,column=1,sticky='we')

        self.info_var = tk.StringVar(value='')
        ttk.Label(self,textvariable=self.info_var).grid(row=1,column=0,columnspan=2,sticky='w')

        # canvases
        self.canvas_src = tk.Canvas(self,width=PREVIEW_MAX,height=PREVIEW_MAX,bg='gray')
        self.canvas_src.grid(row=0,column=1)
        self.canvas_res = tk.Canvas(self,width=PREVIEW_MAX,height=PREVIEW_MAX,bg='gray')
        self.canvas_res.grid(row=0,column=2)

        # 绑定鼠标事件用于ROI绘制
        self.canvas_src.bind("<Button-1>", self._on_mouse_down)
        self.canvas_src.bind("<B1-Motion>", self._on_mouse_drag)
        self.canvas_src.bind("<ButtonRelease-1>", self._on_mouse_up)

    # ---------------- actions -----------------
    def _load_model(self):
        path = filedialog.askopenfilename(filetypes=[('model','*.onnx;*.pt')])
        if not path: return
        self.model_path.set(path)
        try:
            self.detector = load_model(path)
            messagebox.showinfo('模型','加载成功')
        except Exception as e:
            messagebox.showerror('模型加载失败',str(e))

    def _make_template(self):
        if self.img_src is None:
            messagebox.showwarning('提示','请先加载图片'); return
        if self.detector is None:
            messagebox.showwarning('提示','请先加载模型'); return
        try:
            self.detector.make_template_from_image(self.img_src,threshold=self.tpl_thresh.get())
            messagebox.showinfo('模板','已从当前图生成模板')
        except Exception as e:
            messagebox.showerror('模板生成失败',str(e))

    def _draw_roi(self):
        """开始绘制ROI模式"""
        if self.img_src is None:
            messagebox.showwarning('提示','请先加载图片')
            return
        messagebox.showinfo('绘制ROI', '请在左侧图像上拖拽绘制ROI区域')
        self.is_drawing_roi = True

    def _on_mouse_down(self, event):
        """鼠标按下事件"""
        if not self.is_drawing_roi or self.img_src is None:
            return
        self.roi_start = (event.x, event.y)
        # 删除之前的ROI框
        if self.roi_rect_id:
            self.canvas_src.delete(self.roi_rect_id)
            self.roi_rect_id = None

    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.is_drawing_roi or not self.roi_start:
            return
        # 删除之前的临时ROI框
        if self.roi_rect_id:
            self.canvas_src.delete(self.roi_rect_id)
        # 绘制新的ROI框
        self.roi_rect_id = self.canvas_src.create_rectangle(
            self.roi_start[0], self.roi_start[1],
            event.x, event.y,
            outline="red", width=2
        )

    def _on_mouse_up(self, event):
        """鼠标释放事件"""
        if not self.is_drawing_roi or not self.roi_start:
            return

        # 计算ROI区域
        x1, y1 = self.roi_start
        x2, y2 = event.x, event.y

        # 确保坐标正确
        x_min, x_max = min(x1, x2), max(x1, x2)
        y_min, y_max = min(y1, y2), max(y1, y2)

        # 转换为图像坐标（考虑缩放）
        if hasattr(self, '_src_zoom'):
            zoom = self._src_zoom
        else:
            zoom = 1.0

        img_x1 = int(x_min / zoom)
        img_y1 = int(y_min / zoom)
        img_x2 = int(x_max / zoom)
        img_y2 = int(y_max / zoom)

        # 确保坐标在图像范围内
        h, w = self.img_src.shape[:2]
        img_x1 = max(0, min(img_x1, w-1))
        img_y1 = max(0, min(img_y1, h-1))
        img_x2 = max(0, min(img_x2, w-1))
        img_y2 = max(0, min(img_y2, h-1))

        if img_x2 - img_x1 > 10 and img_y2 - img_y1 > 10:
            # 从ROI区域创建轮廓模板
            roi_img = self.img_src[img_y1:img_y2, img_x1:img_x2]
            try:
                if self.detector is None:
                    messagebox.showwarning('提示','请先加载模型')
                    return

                # 从ROI区域生成轮廓模板
                gray = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY) if roi_img.ndim==3 else roi_img
                _, th = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                cnts, _ = cv2.findContours(th, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                if cnts:
                    # 选择最大轮廓并调整坐标到原图
                    largest_cnt = max(cnts, key=cv2.contourArea)
                    # 调整轮廓坐标到原图坐标系
                    adjusted_cnt = largest_cnt + np.array([img_x1, img_y1])
                    self.detector.tpl_cnt = adjusted_cnt
                    messagebox.showinfo('ROI模板', f'ROI模板创建成功！轮廓点数: {len(adjusted_cnt)}')
                else:
                    messagebox.showwarning('ROI模板', 'ROI区域未找到有效轮廓')

            except Exception as e:
                messagebox.showerror('ROI模板创建失败', str(e))
        else:
            messagebox.showwarning('ROI太小', '请绘制更大的ROI区域')

        # 重置绘制状态
        self.is_drawing_roi = False
        self.roi_start = None

    def _load_image(self):
        path = filedialog.askopenfilename(filetypes=[('image','*.png;*.jpg;*.bmp')])
        if not path: return
        self.img_src = cv2.imdecode(np.fromfile(path,dtype=np.uint8),cv2.IMREAD_COLOR)
        self._show_image(self.canvas_src,self.img_src,is_src=True)

    def _run(self):
        if self.img_src is None or self.detector is None:
            messagebox.showwarning('提示','请先加载模型和图片'); return
        self.detector.roi_expand = self.expand_var.get()/100.0
        t0=time.perf_counter()
        res = self.detector.detect(self.img_src, conf=self.conf_var.get(), iou=0.45, use_roi=self.use_roi_var.get())
        infer_ms = (time.perf_counter()-t0)*1000
        out = self.img_src.copy()
        # draw ROI
        if self.use_roi_var.get() and getattr(self.detector,'last_roi_pts',None) is not None:
            pts = self.detector.last_roi_pts
            cv2.polylines(out,[pts],isClosed=True,color=(0,255,0),thickness=1)
        info_lines = []
        overall_ng = False
        for det in res:
            x1,y1,x2,y2 = det['box']
            w = x2 - x1; h = y2 - y1
            mmpp = self.mm_per_px.get() if self.mm_per_px.get()>0 else 1.0
            wmm = w*mmpp; hmm = h*mmpp
            is_ng = (det['score']>=self.ng_conf_th.get()) and (wmm>=self.ng_size_th.get() or hmm>=self.ng_size_th.get())
            color = (0,0,255) if is_ng else (0,255,0)
            if is_ng:
                overall_ng = True
            cv2.rectangle(out,(x1,y1),(x2,y2),color,2)
            txt = f"{det['label']} {det['score']:.2f} {wmm:.2f}x{hmm:.2f}mm"
            cv2.putText(out,txt,(x1,max(10,y1-5)),cv2.FONT_HERSHEY_SIMPLEX,0.5,color,1,cv2.LINE_AA)
            info_lines.append(txt)
        self.img_out = out
        self._show_image(self.canvas_res,out)
        txt = f"目标数:{len(res)}"
        if self.use_roi_var.get() and self.detector.last_score is not None:
            txt += f"  匹配度:{self.detector.last_score:.2f}"
        if info_lines:
            txt += " | " + "; ".join(info_lines)
        txt += f"  耗时:{infer_ms:.1f}ms"
        txt += f"  结果:{'NG' if overall_ng else 'OK'}"
        self.info_var.set(txt)

    # ---------------- utils -----------------
    def _show_image(self, canvas: tk.Canvas, img: Optional[np.ndarray], is_src: bool=False):
        canvas.delete('all')
        if img is None: return
        h,w = img.shape[:2]
        scale = PREVIEW_MAX/max(h,w)
        ph,pw = int(h*scale), int(w*scale)
        img_rs = cv2.resize(img,(pw,ph),interpolation=cv2.INTER_AREA)
        img_rgb = cv2.cvtColor(img_rs, cv2.COLOR_BGR2RGB)
        im_pil = ImageTk.PhotoImage(Image.fromarray(img_rgb))
        canvas.image = im_pil
        canvas.create_image(PREVIEW_MAX/2,PREVIEW_MAX/2,image=im_pil)

    # -------- config save/load --------
    def _save_cfg(self):
        cfg = {
            'model': self.model_path.get(),
            'conf': float(self.conf_var.get()),
            'device': self.device_var.get(),
            'use_roi': bool(self.use_roi_var.get()),
            'roi_thresh': float(self.tpl_thresh.get()),
            'roi_expand': float(self.expand_var.get()),
            'mm_per_px': float(self.mm_per_px.get()),
            'ng_conf_th': float(self.ng_conf_th.get()),
            'ng_size_th': float(self.ng_size_th.get()),
            'min_contour_area': float(self.min_contour_area.get()),
            'template_npy': None,
        }
        # save template contour if exists
        if self.detector and getattr(self.detector,'tpl_cnt',None) is not None:
            tpl_path = CFG_DIR/'template_cnt.npy'
            np.save(tpl_path, self.detector.tpl_cnt)
            cfg['template_npy'] = str(tpl_path.name)
        path = filedialog.asksaveasfilename(defaultextension='.yml',initialfile=DEFAULT_CFG.name,initialdir=str(CFG_DIR),filetypes=[('YAML','*.yml')])
        if not path:
            path = DEFAULT_CFG  # fallback auto
        try:
            with open(path,'w',encoding='utf-8') as f:
                yaml.safe_dump(cfg,f,allow_unicode=True)
            # 同步 mm_per_px 回配方
            update_mm_in_recipe(self._recipe_path, 'yolo_detect', cfg['mm_per_px'])
            messagebox.showinfo('配置','保存成功')
        except Exception as e:
            messagebox.showerror('保存失败',str(e))

    def _load_cfg(self):
        # try default first
        path = DEFAULT_CFG if DEFAULT_CFG.exists() else ''
        if not path:
            path = filedialog.askopenfilename(initialdir=str(CFG_DIR),filetypes=[('YAML','*.yml')])
            if not path:
                return
        try:
            with open(path,'r',encoding='utf-8') as f:
                cfg = yaml.safe_load(f) or {}
            # set vars
            self.model_path.set(cfg.get('model',''))
            self.conf_var.set(cfg.get('conf',0.25))
            self.device_var.set(cfg.get('device','cpu'))
            self.use_roi_var.set(cfg.get('use_roi',False))
            self.tpl_thresh.set(cfg.get('roi_thresh',0.8))
            self.expand_var.set(cfg.get('roi_expand',10))
            self.mm_per_px.set(cfg.get('mm_per_px',0.01))
            self.ng_conf_th.set(cfg.get('ng_conf_th',0.5))
            self.ng_size_th.set(cfg.get('ng_size_th',0.3))
            self.min_contour_area.set(cfg.get('min_contour_area',100))
            # auto load model if path exists
            if self.model_path.get():
                try:
                    self.detector = load_model(self.model_path.get())
                except Exception:
                    pass
            # load template contour if present (after detector ensured)
            tpl_file = cfg.get('template_npy')
            if tpl_file and self.detector is not None:
                p = CFG_DIR / tpl_file
                if p.exists():
                    try:
                        self.detector.tpl_cnt = np.load(p,allow_pickle=False)
                    except Exception:
                        pass
            messagebox.showinfo('配置','加载完成')
        except Exception as e:
            messagebox.showerror('加载失败',str(e))

if __name__=='__main__':
    root = tk.Tk(); root.title('YOLO 检测'); YOLODetectFrame(root).pack(); root.mainloop()
